{"name": "payment-service", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --legacy-watch app/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "intuit-oauth": "^4.2.0", "jsonwebtoken": "^9.0.2", "kafka-node": "^5.0.0", "md5": "^2.3.0", "nodemon": "^3.1.10", "prom-client": "^15.1.3", "ts-node": "^10.9.2", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/express-rate-limit": "^5.1.3", "@types/express-validator": "^2.20.33", "@types/helmet": "^0.0.48", "@types/jsonwebtoken": "^9.0.9", "@types/md5": "^2.3.5", "@types/node": "^22.15.17", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}}