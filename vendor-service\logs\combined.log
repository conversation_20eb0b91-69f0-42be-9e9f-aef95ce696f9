0{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 11:35:37:3537"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: bcd74e14f27155c8fb4388314af0fbcb ","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"2025-05-27T06:08:20.541Z  GET qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"Response sent with status: 200 - MessageID: bcd74e14f27155c8fb4388314af0fbcb","timestamp":"2025-05-27 11:38:23:3823"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: 1df82a54c249516e5c981c84874caace ","timestamp":"2025-05-27 11:42:34:4234"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"POST /api/service?service=qbo&entity=bill: Bad request","stack":"Error: Bad request\n    at validateAuthParams (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:40:11)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:57:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:51:52\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1df82a54c249516e5c981c84874caace","timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:11:37:1137"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313 ","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"2025-05-27T06:42:11.889Z  POST qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"QBO POST request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313","timestamp":"2025-05-27 12:12:15:1215"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: 68e79fd371f2326cb390a307c4cbf901 ","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"2025-05-27T06:45:07.909Z  GET qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 12:15:07:157"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 68e79fd371f2326cb390a307c4cbf901","timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:50:36:5036"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 86fc654fe31165db6c7e6d29964b475d ","timestamp":"2025-05-27 12:56:31:5631"}
{"level":"info","message":"2025-05-27T07:26:31.447Z  POST qbo payment","timestamp":"2025-05-27 12:56:31:5631"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 12:56:31:5631"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 12:56:33:5633"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 86fc654fe31165db6c7e6d29964b475d","timestamp":"2025-05-27 12:56:33:5633"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:19:5819"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:33:5833"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:44:5844"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:58:5858"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:59:18:5918"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:59:53:5953"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:01:01"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:10:010"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:15:015"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:19:019"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:33:033"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 8a9ad1acefbcd60126b61b9037eb0504 ","timestamp":"2025-05-27 13:00:36:036"}
{"level":"info","message":"2025-05-27T07:30:36.811Z  POST qbo payment","timestamp":"2025-05-27 13:00:36:036"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:00:36:036"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:00:38:038"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 8a9ad1acefbcd60126b61b9037eb0504","timestamp":"2025-05-27 13:00:38:038"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:00:10"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:13:113"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:28:128"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:32:132"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:38:138"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: d3d64d2c14101b5a4b10f3e39137c9ee ","timestamp":"2025-05-27 13:01:41:141"}
{"level":"info","message":"2025-05-27T07:31:41.524Z  POST qbo payment","timestamp":"2025-05-27 13:01:41:141"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:01:41:141"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:01:43:143"}
{"level":"info","message":"Response sent with status: 400 - MessageID: d3d64d2c14101b5a4b10f3e39137c9ee","timestamp":"2025-05-27 13:01:43:143"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:02:06:26"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:02:39:239"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: e0df83fa570e7d661885f3e1090358f8 ","timestamp":"2025-05-27 13:02:40:240"}
{"level":"info","message":"2025-05-27T07:32:40.059Z  POST qbo payment","timestamp":"2025-05-27 13:02:40:240"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:02:40:240"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:02:41:241"}
{"level":"info","message":"Response sent with status: 400 - MessageID: e0df83fa570e7d661885f3e1090358f8","timestamp":"2025-05-27 13:02:41:241"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:03:01:31"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 7cce08f2d12576f829d33795181942b6 ","timestamp":"2025-05-27 13:03:04:34"}
{"level":"info","message":"2025-05-27T07:33:04.320Z  POST qbo payment","timestamp":"2025-05-27 13:03:04:34"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:03:04:34"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:03:05:35"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 7cce08f2d12576f829d33795181942b6","timestamp":"2025-05-27 13:03:05:35"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:39:439"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:48:448"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:53:453"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:05:02:52"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: bfb2a6c14503df03aa1b86c070bfb61d ","timestamp":"2025-05-27 13:05:07:57"}
{"level":"info","message":"2025-05-27T07:35:07.944Z  POST qbo payment","timestamp":"2025-05-27 13:05:07:57"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:05:07:57"}
{"code":105,"errorDescription":"Invalid Reference Id","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:05:10:510"}
{"level":"info","message":"Response sent with status: 400 - MessageID: bfb2a6c14503df03aa1b86c070bfb61d","timestamp":"2025-05-27 13:05:10:510"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 64c5f9d1ac24384f220e0703339ad95a ","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"2025-05-27T07:36:11.388Z  POST qbo payment","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 64c5f9d1ac24384f220e0703339ad95a","timestamp":"2025-05-27 13:06:12:612"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:07:16:716"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 38e8ceac9138f359fdc1fc533d225ee8 ","timestamp":"2025-05-27 13:07:20:720"}
{"level":"info","message":"2025-05-27T07:37:20.537Z  POST qbo payment","timestamp":"2025-05-27 13:07:20:720"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:07:20:720"}
{"code":105,"errorDescription":"Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id","stack":"Error: Invalid Reference Id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:07:22:722"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 38e8ceac9138f359fdc1fc533d225ee8","timestamp":"2025-05-27 13:07:22:722"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:08:06:86"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: a43a4ed744be7fd43a959ee93717decd ","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"2025-05-27T07:40:21.277Z  GET qbo payment","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a43a4ed744be7fd43a959ee93717decd","timestamp":"2025-05-27 13:10:23:1023"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:13:52:1352"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 38bc260d576e0ba386b3e2fa02e4c337 ","timestamp":"2025-05-27 14:16:25:1625"}
{"level":"info","message":"2025-05-27T08:46:25.815Z  GET qbo vendor","timestamp":"2025-05-27 14:16:25:1625"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:16:25:1625"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 14:16:27:1627"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 38bc260d576e0ba386b3e2fa02e4c337","timestamp":"2025-05-27 14:16:27:1627"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 20168854bde7307e9d40ca6d2254fcbe ","timestamp":"2025-05-27 14:18:16:1816"}
{"level":"info","message":"2025-05-27T08:48:16.437Z  GET qbo vendor","timestamp":"2025-05-27 14:18:16:1816"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:18:16:1816"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 20168854bde7307e9d40ca6d2254fcbe","timestamp":"2025-05-27 14:18:17:1817"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: a67ea6c897964f55703e4dda17765c5f ","timestamp":"2025-05-27 14:19:14:1914"}
{"level":"info","message":"2025-05-27T08:49:14.675Z  GET qbo vendor","timestamp":"2025-05-27 14:19:14:1914"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:19:14:1914"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a67ea6c897964f55703e4dda17765c5f","timestamp":"2025-05-27 14:19:16:1916"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 398617914d778f960c950532a52f3844 ","timestamp":"2025-05-27 14:20:22:2022"}
{"level":"info","message":"2025-05-27T08:50:22.745Z  GET qbo vendor","timestamp":"2025-05-27 14:20:22:2022"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:20:22:2022"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 398617914d778f960c950532a52f3844","timestamp":"2025-05-27 14:20:24:2024"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:23:02:232"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 184d23ff38b09028368c2e2de432bc43 ","timestamp":"2025-05-27 14:23:03:233"}
{"level":"info","message":"2025-05-27T08:53:03.720Z  GET qbo vendor","timestamp":"2025-05-27 14:23:03:233"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:23:03:233"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 184d23ff38b09028368c2e2de432bc43","timestamp":"2025-05-27 14:23:05:235"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 9c742ed63d9611cc4c31c889859760c8 ","timestamp":"2025-05-27 14:34:44:3444"}
{"level":"info","message":"2025-05-27T09:04:44.092Z  POST qbo vendor","timestamp":"2025-05-27 14:34:44:3444"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:34:44:3444"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 9c742ed63d9611cc4c31c889859760c8","timestamp":"2025-05-27 14:34:46:3446"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: c9dff688391090979ad8ec5f41f7518a ","timestamp":"2025-05-27 14:35:29:3529"}
{"level":"info","message":"2025-05-27T09:05:29.605Z  POST qbo vendor","timestamp":"2025-05-27 14:35:29:3529"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:35:29:3529"}
{"code":105,"errorDescription":"Title, GivenName, MiddleName, FamilyName, DisplayName, Suffix - one of these must be non-empty.","level":"error","message":"POST /api/service?service=qbo&entity=vendor: No name provided","stack":"Error: No name provided\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 14:35:31:3531"}
{"level":"info","message":"Response sent with status: 400 - MessageID: c9dff688391090979ad8ec5f41f7518a","timestamp":"2025-05-27 14:35:31:3531"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-05-27 14:40:11:4011"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 4c6acdd3bbc052a67f51a3c66eb4cc37 ","timestamp":"2025-05-27 14:47:12:4712"}
{"level":"info","message":"2025-05-27T09:17:12.906Z  POST qbo vendor","timestamp":"2025-05-27 14:47:12:4712"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:47:12:4712"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 4c6acdd3bbc052a67f51a3c66eb4cc37","timestamp":"2025-05-27 14:47:15:4715"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 0a7013b50485f3499bab5f9b36d77d98 ","timestamp":"2025-05-27 14:47:48:4748"}
{"level":"info","message":"2025-05-27T09:17:48.206Z  GET qbo vendor","timestamp":"2025-05-27 14:47:48:4748"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:47:48:4748"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 0a7013b50485f3499bab5f9b36d77d98","timestamp":"2025-05-27 14:47:49:4749"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-05-27 15:13:35:1335"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: b01c93c30cd56a3b9c9f4a820c564df2 ","timestamp":"2025-05-27 15:13:50:1350"}
{"level":"info","message":"2025-05-27T09:43:50.886Z  GET qbo vendor","timestamp":"2025-05-27 15:13:50:1350"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 15:13:50:1350"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b01c93c30cd56a3b9c9f4a820c564df2","timestamp":"2025-05-27 15:13:52:1352"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-05-27 16:01:50:150"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: f1865e8846f63b8a6300d2d551f9437b ","timestamp":"2025-05-27 16:02:01:21"}
{"level":"info","message":"2025-05-27T10:32:01.298Z  GET qbo vendor","timestamp":"2025-05-27 16:02:01:21"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 16:02:01:21"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f1865e8846f63b8a6300d2d551f9437b","timestamp":"2025-05-27 16:02:03:23"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendors - MessageID: 6dda480f45fe42c6987136c9fb495234 ","timestamp":"2025-05-27 16:02:06:26"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'vendor' or 'VENDOR'","level":"error","message":"GET /api/service?service=qbo&entity=vendors: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:02:06:26"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 6dda480f45fe42c6987136c9fb495234","timestamp":"2025-05-27 16:02:06:26"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 3c62e77744bebf699831c93266487146 ","timestamp":"2025-05-27 16:02:15:215"}
{"level":"info","message":"2025-05-27T10:32:15.318Z  GET qbo vendor","timestamp":"2025-05-27 16:02:15:215"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 93414544417370","timestamp":"2025-05-27 16:02:15:215"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:02:17:217"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 3c62e77744bebf699831c93266487146","timestamp":"2025-05-27 16:02:17:217"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 51d3d5f8de30edd44b8c98775b9ee50a ","timestamp":"2025-05-27 16:02:26:226"}
{"level":"info","message":"2025-05-27T10:32:26.468Z  GET qbo vendor","timestamp":"2025-05-27 16:02:26:226"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 16:02:26:226"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:02:28:228"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 51d3d5f8de30edd44b8c98775b9ee50a","timestamp":"2025-05-27 16:02:28:228"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 599c5b13dfc95eee7933f815cac247ea ","timestamp":"2025-05-27 16:02:31:231"}
{"level":"info","message":"2025-05-27T10:32:31.445Z  GET qbo vendor","timestamp":"2025-05-27 16:02:31:231"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 16:02:31:231"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 599c5b13dfc95eee7933f815cac247ea","timestamp":"2025-05-27 16:02:31:231"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-05-27 18:12:22:1222"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: eb09562d5103e55e476a66df4f3121dd ","timestamp":"2025-05-27 18:14:18:1418"}
{"level":"info","message":"2025-05-27T12:44:18.667Z  GET qbo vendor","timestamp":"2025-05-27 18:14:18:1418"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 18:14:18:1418"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 18:14:20:1420"}
{"level":"info","message":"Response sent with status: 401 - MessageID: eb09562d5103e55e476a66df4f3121dd","timestamp":"2025-05-27 18:14:20:1420"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 0cbab71c02dcc16aabeebb21ff131a89 ","timestamp":"2025-05-27 18:18:09:189"}
{"level":"info","message":"2025-05-27T12:48:09.165Z  GET qbo vendor","timestamp":"2025-05-27 18:18:09:189"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 18:18:09:189"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 0cbab71c02dcc16aabeebb21ff131a89","timestamp":"2025-05-27 18:18:11:1811"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 641192729cfe57824c7388ef76f111fc ","timestamp":"2025-05-27 18:18:15:1815"}
{"level":"info","message":"2025-05-27T12:48:15.966Z  GET qbo vendor","timestamp":"2025-05-27 18:18:15:1815"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033swdadasdasd","timestamp":"2025-05-27 18:18:15:1815"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=vendor: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 18:18:16:1816"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 641192729cfe57824c7388ef76f111fc","timestamp":"2025-05-27 18:18:16:1816"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 75481ddf1c2f0d8ebea5297619b4ca96 ","timestamp":"2025-05-27 18:18:29:1829"}
{"level":"info","message":"2025-05-27T12:48:29.236Z  GET qbo vendor","timestamp":"2025-05-27 18:18:29:1829"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: asasdsad9341454441737033","timestamp":"2025-05-27 18:18:29:1829"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=vendor: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 18:18:30:1830"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 75481ddf1c2f0d8ebea5297619b4ca96","timestamp":"2025-05-27 18:18:30:1830"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-05-27 18:19:40:1940"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-05-27 18:20:33:2033"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-03 10:40:48:4048"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-03 10:41:47:4147"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-03 10:41:57:4157"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-03 10:42:03:423"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=vendor - MessageID: 12a1cca3f09a88731dfd9cb69be10174 ","timestamp":"2025-06-03 10:42:31:4231"}
{"level":"info","message":"2025-06-03T05:12:31.805Z  GET qbo vendor","timestamp":"2025-06-03 10:42:31:4231"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: {{companyId}}","timestamp":"2025-06-03 10:42:31:4231"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 10:42:34:4234"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 12a1cca3f09a88731dfd9cb69be10174","timestamp":"2025-06-03 10:42:34:4234"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=vendor - MessageID: 7b448283b93f5cf791e4204be3d3980c ","timestamp":"2025-06-03 10:48:04:484"}
{"level":"info","message":"2025-06-03T05:18:04.174Z  GET qbo vendor","timestamp":"2025-06-03 10:48:04:484"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 10:48:04:484"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 7b448283b93f5cf791e4204be3d3980c","timestamp":"2025-06-03 10:48:07:487"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2025-08-12&entity=vendor - MessageID: f21766221df9ef616ba6e2ae49ba7496 ","timestamp":"2025-06-03 10:48:19:4819"}
{"level":"info","message":"2025-06-03T05:18:19.963Z  GET qbo vendor","timestamp":"2025-06-03 10:48:19:4819"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 10:48:19:4819"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f21766221df9ef616ba6e2ae49ba7496","timestamp":"2025-06-03 10:48:22:4822"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-0-11&endDate=2025-08-12&entity=vendor - MessageID: 32f482b38832f445a1700a9fc4dc7ddb ","timestamp":"2025-06-03 10:48:39:4839"}
{"level":"info","message":"2025-06-03T05:18:39.286Z  GET qbo vendor","timestamp":"2025-06-03 10:48:39:4839"}
{"code":102,"errorDescription":"Invalid startDate format. Use ISO date format (YYYY-MM-DD)","level":"error","message":"GET /api/service?service=qbo&dateField=createdDate&startDate=2023-0-11&endDate=2025-08-12&entity=vendor: Bad request","stack":"Error: Bad request\n    at validateDateParams (D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:64:11)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:128:7\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:110:52\n    at D:\\Zact_New_Arch\\vendor-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 10:48:39:4839"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 32f482b38832f445a1700a9fc4dc7ddb","timestamp":"2025-06-03 10:48:39:4839"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2024-04-11&endDate=2025-08-12&entity=vendor - MessageID: 24579b21db4c427efa442f717cd2a441 ","timestamp":"2025-06-03 10:48:48:4848"}
{"level":"info","message":"2025-06-03T05:18:48.796Z  GET qbo vendor","timestamp":"2025-06-03 10:48:48:4848"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 10:48:48:4848"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 24579b21db4c427efa442f717cd2a441","timestamp":"2025-06-03 10:48:51:4851"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2025-04-11&endDate=2025-08-12&entity=vendor - MessageID: 3e233c7ea95b53712d4712f8b5ea0ae3 ","timestamp":"2025-06-03 10:49:09:499"}
{"level":"info","message":"2025-06-03T05:19:09.544Z  GET qbo vendor","timestamp":"2025-06-03 10:49:09:499"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 10:49:09:499"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 3e233c7ea95b53712d4712f8b5ea0ae3","timestamp":"2025-06-03 10:49:12:4912"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2025-04-11&endDate=2025-08-12&entity=vendor - MessageID: 26571645d4f5fc6d46833dbb06ded5ba ","timestamp":"2025-06-03 10:51:23:5123"}
{"level":"info","message":"2025-06-03T05:21:23.765Z  GET qbo vendor","timestamp":"2025-06-03 10:51:23:5123"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 10:51:23:5123"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 26571645d4f5fc6d46833dbb06ded5ba","timestamp":"2025-06-03 10:51:26:5126"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-03 11:23:40:2340"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&dateField=createdDate&startDate=2025-04-11&endDate=2025-08-12&entity=vendor - MessageID: 4a7650ad356001f2a94445e7b56576c9 ","timestamp":"2025-06-03 11:25:03:253"}
{"level":"info","message":"2025-06-03T05:55:03.304Z  POST qbo vendor","timestamp":"2025-06-03 11:25:03:253"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 11:25:03:253"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 4a7650ad356001f2a94445e7b56576c9","timestamp":"2025-06-03 11:25:07:257"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-03 15:37:09:379"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 1a9b20dc6a621a15692ed81a288264b5 ","timestamp":"2025-06-03 15:40:05:405"}
{"level":"info","message":"2025-06-03T10:10:05.435Z  GET qbo vendor","timestamp":"2025-06-03 15:40:05:405"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:40:05:405"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:40:08:408"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 3db98f360aab3acdee04bb04fd3b9e18 ","timestamp":"2025-06-03 15:40:20:4020"}
{"level":"info","message":"2025-06-03T10:10:20.511Z  GET qbo vendor","timestamp":"2025-06-03 15:40:20:4020"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:40:20:4020"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:40:23:4023"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 0bc442e27b15cc2490206a9a57fabae9 ","timestamp":"2025-06-03 15:40:59:4059"}
{"level":"info","message":"2025-06-03T10:10:59.818Z  GET qbo vendor","timestamp":"2025-06-03 15:40:59:4059"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:40:59:4059"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:41:02:412"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: b5f84becf7fa9708486a8491eb6124bf ","timestamp":"2025-06-03 15:41:28:4128"}
{"level":"info","message":"2025-06-03T10:11:28.777Z  GET qbo vendor","timestamp":"2025-06-03 15:41:28:4128"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:41:28:4128"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:41:31:4131"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 7e8198a5504732f0649590a83f978520 ","timestamp":"2025-06-03 15:42:06:426"}
{"level":"info","message":"2025-06-03T10:12:06.780Z  GET qbo vendor","timestamp":"2025-06-03 15:42:06:426"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:42:06:426"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 59e383d0114c9b1861d5a960b646a75f ","timestamp":"2025-06-03 15:42:08:428"}
{"level":"info","message":"2025-06-03T10:12:08.441Z  GET qbo vendor","timestamp":"2025-06-03 15:42:08:428"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:42:08:428"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:42:09:429"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:42:10:4210"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 06902dae9ff9635e7664cbd29bcff37a ","timestamp":"2025-06-03 15:42:23:4223"}
{"level":"info","message":"2025-06-03T10:12:23.194Z  GET qbo vendor","timestamp":"2025-06-03 15:42:23:4223"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:42:23:4223"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:42:25:4225"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 5f864f95d558b522316c234b72c62635 ","timestamp":"2025-06-03 15:42:32:4232"}
{"level":"info","message":"2025-06-03T10:12:32.540Z  GET qbo vendor","timestamp":"2025-06-03 15:42:32:4232"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:42:32:4232"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:42:35:4235"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: e1f52dea072427b019291ab287425a59 ","timestamp":"2025-06-03 15:42:36:4236"}
{"level":"info","message":"2025-06-03T10:12:36.987Z  GET qbo vendor","timestamp":"2025-06-03 15:42:36:4236"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:42:36:4236"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:42:37:4237"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: a46347176774609c59cb1ca047eaa1be ","timestamp":"2025-06-03 15:43:01:431"}
{"level":"info","message":"2025-06-03T10:13:01.874Z  GET qbo vendor","timestamp":"2025-06-03 15:43:01:431"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:43:01:431"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:43:04:434"}
{"level":"info","message":"Response sent with status: 401 - MessageID: a46347176774609c59cb1ca047eaa1be","timestamp":"2025-06-03 15:43:04:434"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: e07b174b10d498ea0c11795a83a060e7 ","timestamp":"2025-06-03 15:43:45:4345"}
{"level":"info","message":"2025-06-03T10:13:45.007Z  GET qbo vendor","timestamp":"2025-06-03 15:43:45:4345"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:43:45:4345"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:43:47:4347"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 629fdeeec713540744c59f88e360f162 ","timestamp":"2025-06-03 15:43:56:4356"}
{"level":"info","message":"2025-06-03T10:13:56.639Z  GET qbo vendor","timestamp":"2025-06-03 15:43:56:4356"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:43:56:4356"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:43:59:4359"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 629fdeeec713540744c59f88e360f162","timestamp":"2025-06-03 15:43:59:4359"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: f180e3723b9cd12becbe12716a6170aa ","timestamp":"2025-06-03 15:44:53:4453"}
{"level":"info","message":"2025-06-03T10:14:53.817Z  GET qbo vendor","timestamp":"2025-06-03 15:44:53:4453"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:44:53:4453"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:44:56:4456"}
{"level":"info","message":"Response sent with status: 401 - MessageID: f180e3723b9cd12becbe12716a6170aa","timestamp":"2025-06-03 15:44:56:4456"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 6cbedf011686003284075bb7097a2e60 ","timestamp":"2025-06-03 15:45:11:4511"}
{"level":"info","message":"2025-06-03T10:15:11.229Z  GET qbo vendor","timestamp":"2025-06-03 15:45:11:4511"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:45:11:4511"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:45:13:4513"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 6cbedf011686003284075bb7097a2e60","timestamp":"2025-06-03 15:45:13:4513"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: b975a60502897313a7a72a7f90ccb2ba ","timestamp":"2025-06-03 15:48:03:483"}
{"level":"info","message":"2025-06-03T10:18:03.189Z  GET qbo vendor","timestamp":"2025-06-03 15:48:03:483"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 15:48:03:483"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 15:48:06:486"}
{"level":"info","message":"Response sent with status: 401 - MessageID: b975a60502897313a7a72a7f90ccb2ba","timestamp":"2025-06-03 15:48:06:486"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 5ed67e486cdefc29a84c9be68c5a4051 ","timestamp":"2025-06-03 16:05:20:520"}
{"level":"info","message":"2025-06-03T10:35:20.997Z  GET qbo vendor","timestamp":"2025-06-03 16:05:20:520"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:05:20:520"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:05:23:523"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 3fc733268bf6067c8d190ac09e3d6ee7 ","timestamp":"2025-06-03 16:05:59:559"}
{"level":"info","message":"2025-06-03T10:35:59.952Z  GET qbo vendor","timestamp":"2025-06-03 16:05:59:559"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:05:59:559"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:06:02:62"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 51e1844459f4744fa2ce8bc39cd61e85 ","timestamp":"2025-06-03 16:06:53:653"}
{"level":"info","message":"2025-06-03T10:36:54.031Z  GET qbo vendor","timestamp":"2025-06-03 16:06:54:654"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:06:54:654"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:06:56:656"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 51e1844459f4744fa2ce8bc39cd61e85","timestamp":"2025-06-03 16:06:56:656"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 7881362e01991a3429b59955e6179b15 ","timestamp":"2025-06-03 16:07:42:742"}
{"level":"info","message":"2025-06-03T10:37:42.916Z  GET qbo vendor","timestamp":"2025-06-03 16:07:42:742"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:07:42:742"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:07:45:745"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 7881362e01991a3429b59955e6179b15","timestamp":"2025-06-03 16:07:45:745"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 7a3fa40a664a2573dde011ce94d4eede ","timestamp":"2025-06-03 16:07:53:753"}
{"level":"info","message":"2025-06-03T10:37:53.404Z  GET qbo vendor","timestamp":"2025-06-03 16:07:53:753"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:07:53:753"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:07:56:756"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 6cc1fec3da52114a2681376d32a7363b ","timestamp":"2025-06-03 16:08:28:828"}
{"level":"info","message":"2025-06-03T10:38:28.395Z  GET qbo vendor","timestamp":"2025-06-03 16:08:28:828"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:08:28:828"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:08:31:831"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 2ca6d6eebf883733dbd0a2e8df14b225 ","timestamp":"2025-06-03 16:11:51:1151"}
{"level":"info","message":"2025-06-03T10:41:51.970Z  GET qbo vendor","timestamp":"2025-06-03 16:11:51:1151"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:11:51:1151"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:11:54:1154"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 79da6080268a2d0f6816932bdcf8eb38 ","timestamp":"2025-06-03 16:13:55:1355"}
{"level":"info","message":"2025-06-03T10:43:55.299Z  GET qbo vendor","timestamp":"2025-06-03 16:13:55:1355"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:13:55:1355"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:13:58:1358"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 1474682821b36867ad2617f147f9f405 ","timestamp":"2025-06-03 16:14:25:1425"}
{"level":"info","message":"2025-06-03T10:44:25.837Z  GET qbo vendor","timestamp":"2025-06-03 16:14:25:1425"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:14:25:1425"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:14:28:1428"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 1b23186834aca6bccd3f4058b8c9ec7a ","timestamp":"2025-06-03 16:15:01:151"}
{"level":"info","message":"2025-06-03T10:45:01.279Z  GET qbo vendor","timestamp":"2025-06-03 16:15:01:151"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:15:01:151"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:15:04:154"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 803a139573f689bd544115a078905afd ","timestamp":"2025-06-03 16:15:50:1550"}
{"level":"info","message":"2025-06-03T10:45:50.915Z  GET qbo vendor","timestamp":"2025-06-03 16:15:50:1550"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:15:50:1550"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:15:53:1553"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 4fc1bfc83d9f0e4f80046dfc2d32393a ","timestamp":"2025-06-03 16:24:19:2419"}
{"level":"info","message":"2025-06-03T10:54:19.914Z  GET qbo vendor","timestamp":"2025-06-03 16:24:19:2419"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:24:19:2419"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:24:22:2422"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: d5e3a1bfcae88c14ed246ec5cfb6d3a5 ","timestamp":"2025-06-03 16:24:28:2428"}
{"level":"info","message":"2025-06-03T10:54:28.224Z  GET qbo vendor","timestamp":"2025-06-03 16:24:28:2428"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:24:28:2428"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:24:30:2430"}
{"level":"info","message":"Response sent with status: 401 - MessageID: d5e3a1bfcae88c14ed246ec5cfb6d3a5","timestamp":"2025-06-03 16:24:30:2430"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: d2d10cd1904a67b9d08d41ca09c6097d ","timestamp":"2025-06-03 16:27:13:2713"}
{"level":"info","message":"2025-06-03T10:57:13.945Z  GET qbo vendor","timestamp":"2025-06-03 16:27:13:2713"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:27:13:2713"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 8b47d0347f4f551fc66336bc037c1d84 ","timestamp":"2025-06-03 16:27:15:2715"}
{"level":"info","message":"2025-06-03T10:57:15.475Z  GET qbo vendor","timestamp":"2025-06-03 16:27:15:2715"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:27:15:2715"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:27:16:2716"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:27:18:2718"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 618a9e58df1c11f19a500de8018ca15d ","timestamp":"2025-06-03 16:28:19:2819"}
{"level":"info","message":"2025-06-03T10:58:19.744Z  GET qbo vendor","timestamp":"2025-06-03 16:28:19:2819"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:28:19:2819"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:28:22:2822"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 35590a76f43085181d145065172c60ea ","timestamp":"2025-06-03 16:28:43:2843"}
{"level":"info","message":"2025-06-03T10:58:43.016Z  GET qbo vendor","timestamp":"2025-06-03 16:28:43:2843"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:28:43:2843"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 66cb08a053988915b5113de479b6f8f1 ","timestamp":"2025-06-03 16:28:44:2844"}
{"level":"info","message":"2025-06-03T10:58:44.707Z  GET qbo vendor","timestamp":"2025-06-03 16:28:44:2844"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:28:44:2844"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:28:45:2845"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:28:47:2847"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 751549706cc2975011b64b39b79ac2ca ","timestamp":"2025-06-03 16:30:56:3056"}
{"level":"info","message":"2025-06-03T11:00:56.614Z  GET qbo vendor","timestamp":"2025-06-03 16:30:56:3056"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:30:56:3056"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:30:59:3059"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 751549706cc2975011b64b39b79ac2ca","timestamp":"2025-06-03 16:30:59:3059"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 776cf14d674e0092182f18983d1007e2 ","timestamp":"2025-06-03 16:32:39:3239"}
{"level":"info","message":"2025-06-03T11:02:39.107Z  GET qbo vendor","timestamp":"2025-06-03 16:32:39:3239"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:32:39:3239"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:32:42:3242"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 776cf14d674e0092182f18983d1007e2","timestamp":"2025-06-03 16:32:42:3242"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 1067c166a79f38378120229de960d585 ","timestamp":"2025-06-03 16:32:51:3251"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1067c166a79f38378120229de960d585","timestamp":"2025-06-03 16:32:51:3251"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 4b643643c2da0ccc785fc18f037bcda5 ","timestamp":"2025-06-03 16:32:57:3257"}
{"level":"info","message":"2025-06-03T11:02:57.642Z  GET qbo vendor","timestamp":"2025-06-03 16:32:57:3257"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:32:57:3257"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:33:00:330"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 4b643643c2da0ccc785fc18f037bcda5","timestamp":"2025-06-03 16:33:00:330"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 6e99d3fee86d7264327e266d3c2547f5 ","timestamp":"2025-06-03 16:33:17:3317"}
{"level":"info","message":"2025-06-03T11:03:17.126Z  GET qbo vendor","timestamp":"2025-06-03 16:33:17:3317"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:33:17:3317"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:33:19:3319"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 6e99d3fee86d7264327e266d3c2547f5","timestamp":"2025-06-03 16:33:19:3319"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 0ec2fd683b5060ea79f5da28686c1764 ","timestamp":"2025-06-03 16:33:25:3325"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 0ec2fd683b5060ea79f5da28686c1764","timestamp":"2025-06-03 16:33:25:3325"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 7ef54b8c2c2165b95037f32298a8b6c9 ","timestamp":"2025-06-03 16:33:47:3347"}
{"level":"info","message":"2025-06-03T11:03:47.414Z  POST qbo vendor","timestamp":"2025-06-03 16:33:47:3347"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:33:47:3347"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"POST /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:96:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 16:33:50:3350"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 7ef54b8c2c2165b95037f32298a8b6c9","timestamp":"2025-06-03 16:33:50:3350"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: e4112fa3353fcdf4b31f369b5d78a6b4 ","timestamp":"2025-06-03 16:34:39:3439"}
{"level":"info","message":"2025-06-03T11:04:39.325Z  POST qbo vendor","timestamp":"2025-06-03 16:34:39:3439"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:34:39:3439"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e4112fa3353fcdf4b31f369b5d78a6b4","timestamp":"2025-06-03 16:34:42:3442"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 6a765fcd0bffe79d9311f9d9c2a1823a ","timestamp":"2025-06-03 16:36:39:3639"}
{"level":"info","message":"2025-06-03T11:06:39.481Z  GET qbo vendor","timestamp":"2025-06-03 16:36:39:3639"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:36:39:3639"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6a765fcd0bffe79d9311f9d9c2a1823a","timestamp":"2025-06-03 16:36:42:3642"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 538beb9298d664cbffbdf159a6eb6a96 ","timestamp":"2025-06-03 16:38:00:380"}
{"level":"info","message":"2025-06-03T11:08:00.590Z  GET qbo vendor","timestamp":"2025-06-03 16:38:00:380"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:38:00:380"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 538beb9298d664cbffbdf159a6eb6a96","timestamp":"2025-06-03 16:38:03:383"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 8afe1670f75dcddd2584ec6cf82455f1 ","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"info","message":"2025-06-03T11:10:27.944Z  GET qbo vendor","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8afe1670f75dcddd2584ec6cf82455f1","timestamp":"2025-06-03 16:40:30:4030"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: a6665c5a0798e99b62dca3e2ed15b811 ","timestamp":"2025-06-03 16:40:45:4045"}
{"level":"info","message":"Response sent with status: 400 - MessageID: a6665c5a0798e99b62dca3e2ed15b811","timestamp":"2025-06-03 16:40:45:4045"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: f4f8bdb650f574978e71768a9a4d5ef1 ","timestamp":"2025-06-03 16:51:18:5118"}
{"level":"info","message":"Response sent with status: 400 - MessageID: f4f8bdb650f574978e71768a9a4d5ef1","timestamp":"2025-06-03 16:51:18:5118"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 34773d3271b3252586907f75894e2a37 ","timestamp":"2025-06-03 16:51:32:5132"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 34773d3271b3252586907f75894e2a37","timestamp":"2025-06-03 16:51:32:5132"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 3ba22307281a2991032ad6ebb4b58e2b ","timestamp":"2025-06-03 16:56:05:565"}
{"level":"info","message":"2025-06-03T11:26:05.894Z  GET qbo vendor","timestamp":"2025-06-03 16:56:05:565"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 16:56:05:565"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 3ba22307281a2991032ad6ebb4b58e2b","timestamp":"2025-06-03 16:56:08:568"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 63ac243cc5722aab4a0db690f438d52f ","timestamp":"2025-06-03 17:03:16:316"}
{"level":"info","message":"2025-06-03T11:33:16.237Z  GET qbo vendor","timestamp":"2025-06-03 17:03:16:316"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-03 17:03:16:316"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 63ac243cc5722aab4a0db690f438d52f","timestamp":"2025-06-03 17:03:19:319"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-04 10:50:47:5047"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor&dateField=createdDate&totalCount=true - MessageID: 8ec46244d0078fed6decefa068b62521 ","timestamp":"2025-06-04 10:53:19:5319"}
{"level":"info","message":"2025-06-04T05:23:19.626Z  GET qbo vendor","timestamp":"2025-06-04 10:53:19:5319"}
{"code":102,"errorDescription":"startDate is required when dateField is provided","level":"error","message":"GET /api/service?service=qbo&entity=vendor&dateField=createdDate&totalCount=true: Bad request","stack":"Error: Bad request\n    at validateDateParams (D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:50:11)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:133:7\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:110:52\n    at D:\\Zact_New_Arch\\vendor-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-04 10:53:19:5319"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 8ec46244d0078fed6decefa068b62521","timestamp":"2025-06-04 10:53:19:5319"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor&startDate=2025-04-11&totalCount=true - MessageID: bb563fc81a988f084b7e0a99014ed85a ","timestamp":"2025-06-04 10:53:26:5326"}
{"level":"info","message":"2025-06-04T05:23:26.391Z  GET qbo vendor","timestamp":"2025-06-04 10:53:26:5326"}
{"code":102,"errorDescription":"dateField is required when startDate is provided","level":"error","message":"GET /api/service?service=qbo&entity=vendor&startDate=2025-04-11&totalCount=true: Bad request","stack":"Error: Bad request\n    at validateDateParams (D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:57:11)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:133:7\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\router\\api.routes.ts:110:52\n    at D:\\Zact_New_Arch\\vendor-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\vendor-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-04 10:53:26:5326"}
{"level":"info","message":"Response sent with status: 400 - MessageID: bb563fc81a988f084b7e0a99014ed85a","timestamp":"2025-06-04 10:53:26:5326"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor&totalCount=true - MessageID: e87dfb5686347f408c267fb1d9e12b19 ","timestamp":"2025-06-04 10:53:32:5332"}
{"level":"info","message":"2025-06-04T05:23:32.652Z  GET qbo vendor","timestamp":"2025-06-04 10:53:32:5332"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: {{companyId}}","timestamp":"2025-06-04 10:53:32:5332"}
{"level":"info","message":"QBO query: SELECT COUNT(*) FROM vendor","timestamp":"2025-06-04 10:53:32:5332"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor&totalCount=true: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\vendor-service\\app\\services\\qboApiServices\\index.ts:99:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-04 10:53:34:5334"}
{"level":"info","message":"Response sent with status: 401 - MessageID: e87dfb5686347f408c267fb1d9e12b19","timestamp":"2025-06-04 10:53:34:5334"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor&totalCount=true - MessageID: 7200201e35313cea1dd10cec2983df8d ","timestamp":"2025-06-04 10:59:09:599"}
{"level":"info","message":"2025-06-04T05:29:09.105Z  GET qbo vendor","timestamp":"2025-06-04 10:59:09:599"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-04 10:59:09:599"}
{"level":"info","message":"QBO query: SELECT COUNT(*) FROM vendor","timestamp":"2025-06-04 10:59:09:599"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 7200201e35313cea1dd10cec2983df8d","timestamp":"2025-06-04 10:59:12:5912"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor&dateField=createdDate&startDate=2025-04-11&totalCount=true - MessageID: 7ce7eaf41d4690702c19639bed79bdf6 ","timestamp":"2025-06-04 10:59:22:5922"}
{"level":"info","message":"2025-06-04T05:29:22.290Z  GET qbo vendor","timestamp":"2025-06-04 10:59:22:5922"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-04 10:59:22:5922"}
{"level":"info","message":"QBO query: SELECT COUNT(*) FROM vendor WHERE MetaData.CreateTime >= '2025-04-11'","timestamp":"2025-06-04 10:59:22:5922"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 7ce7eaf41d4690702c19639bed79bdf6","timestamp":"2025-06-04 10:59:25:5925"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor&dateField=createdDate&startDate=2025-04-11 - MessageID: c0280e24ce5bf9c139b6088c45f49aa5 ","timestamp":"2025-06-04 10:59:33:5933"}
{"level":"info","message":"2025-06-04T05:29:33.875Z  GET qbo vendor","timestamp":"2025-06-04 10:59:33:5933"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-04 10:59:33:5933"}
{"level":"info","message":"QBO query: SELECT * FROM vendor WHERE MetaData.CreateTime >= '2025-04-11'","timestamp":"2025-06-04 10:59:33:5933"}
{"level":"info","message":"Response sent with status: 200 - MessageID: c0280e24ce5bf9c139b6088c45f49aa5","timestamp":"2025-06-04 10:59:36:5936"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 8e74516164371a783cf3fe4fb969893c ","timestamp":"2025-06-04 11:00:00:00"}
{"level":"info","message":"2025-06-04T05:30:00.585Z  GET qbo vendor","timestamp":"2025-06-04 11:00:00:00"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-04 11:00:00:00"}
{"level":"info","message":"QBO query: SELECT * FROM vendor","timestamp":"2025-06-04 11:00:00:00"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8e74516164371a783cf3fe4fb969893c","timestamp":"2025-06-04 11:00:03:03"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-04 11:00:56:056"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-04 11:01:10:110"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-04 11:01:21:121"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-06-04 11:02:00:20"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: ca42d01b7222db14cdce543838e6478f ","timestamp":"2025-06-04 11:02:20:220"}
{"level":"info","message":"2025-06-04T05:32:20.032Z  GET qbo vendor","timestamp":"2025-06-04 11:02:20:220"}
{"level":"info","message":"QBO GET request | entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-04 11:02:20:220"}
{"level":"info","message":"QBO query: SELECT * FROM vendor","timestamp":"2025-06-04 11:02:20:220"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ca42d01b7222db14cdce543838e6478f","timestamp":"2025-06-04 11:02:23:223"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 506ad22b920621214b5b8b14c65819dc ","timestamp":"2025-06-04 11:04:08:48"}
{"level":"info","message":"2025-06-04T05:34:08.090Z  POST qbo vendor","timestamp":"2025-06-04 11:04:08:48"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 4620816365356807410","timestamp":"2025-06-04 11:04:08:48"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 506ad22b920621214b5b8b14c65819dc","timestamp":"2025-06-04 11:04:11:411"}
