version: '3.8'

name: zact-ms

services:
  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
      - NODE_ENV=development
      - ACCOUNT_SERVICE_URL=http://account-service:8003
      - BILL_SERVICE_URL=http://bill-service:8006
      - CLASS_SERVICE_URL=http://class-service:8002
      - JOURNALENTRY_SERVICE_URL=http://journalentry-service:8005
      - PAYMENT_SERVICE_URL=http://payment-service:8004
      - VENDOR_SERVICE_URL=http://vendor-service:8001
      
    depends_on:
      - account-service
      - bill-service
      - class-service
      - journalentry-service
      - payment-service
      - vendor-service
    networks:
      - zact-network
    restart: always

  # Account Service
  account-service:
    build:
      context: ./account-service
      dockerfile: Dockerfile
    ports:
      - "8003:8003"
    environment:
      - PORT=8003
      - QBO_API_URL=https://sandbox-quickbooks.api.intuit.com/v3/company
      - QBO_MINOR_VERSION=75
      - NODE_ENV=development
    networks:
      - zact-network
    restart: always

  # Bill Service
  bill-service:
    build:
      context: ./bill-service
      dockerfile: Dockerfile
    ports:
      - "8006:8006"
    environment:
      - PORT=8006
      - QBO_API_URL=https://sandbox-quickbooks.api.intuit.com/v3/company
      - QBO_MINOR_VERSION=75
      - NODE_ENV=development
    networks:
      - zact-network
    restart: always

  # Class Service
  class-service:
    build:
      context: ./class-service
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - PORT=8002
      - QBO_API_URL=https://sandbox-quickbooks.api.intuit.com/v3/company
      - QBO_MINOR_VERSION=75
      - NODE_ENV=development
    networks:
      - zact-network
    restart: always

  # Journal Entry Service
  journalentry-service:
    build:
      context: ./journalentry-service
      dockerfile: Dockerfile
    ports:
      - "8005:8005"
    environment:
      - PORT=8005
      - QBO_API_URL=https://sandbox-quickbooks.api.intuit.com/v3/company
      - QBO_MINOR_VERSION=75
      - NODE_ENV=development
    networks:
      - zact-network
    restart: always

  # Payment Service
  payment-service:
    build:
      context: ./payment-service
      dockerfile: Dockerfile
    ports:
      - "8004:8004"
    environment:
      - PORT=8004
      - QBO_API_URL=https://sandbox-quickbooks.api.intuit.com/v3/company
      - QBO_MINOR_VERSION=75
      - NODE_ENV=development
    networks:
      - zact-network
    restart: always

  # Vendor Service
  vendor-service:
    build:
      context: ./vendor-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - PORT=8001
      - QBO_API_URL=https://sandbox-quickbooks.api.intuit.com/v3/company
      - QBO_MINOR_VERSION=75
      - NODE_ENV=development
    networks:
      - zact-network
    restart: always

networks:
  zact-network:
    driver: bridge

# Optional: Add volumes for development (uncomment if needed)
# volumes:
#   node_modules_api_gateway:
#   node_modules_account:
#   node_modules_bill:
#   node_modules_class:
#   node_modules_journalentry:
#   node_modules_payment:
#   node_modules_vendor: