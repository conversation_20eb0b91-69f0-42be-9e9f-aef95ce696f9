0{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 11:35:37:3537"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: bcd74e14f27155c8fb4388314af0fbcb ","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"2025-05-27T06:08:20.541Z  GET qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"Response sent with status: 200 - MessageID: bcd74e14f27155c8fb4388314af0fbcb","timestamp":"2025-05-27 11:38:23:3823"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: 1df82a54c249516e5c981c84874caace ","timestamp":"2025-05-27 11:42:34:4234"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"POST /api/service?service=qbo&entity=bill: Bad request","stack":"Error: Bad request\n    at validateAuthParams (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:40:11)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:57:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:51:52\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1df82a54c249516e5c981c84874caace","timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:11:37:1137"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313 ","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"2025-05-27T06:42:11.889Z  POST qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"QBO POST request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313","timestamp":"2025-05-27 12:12:15:1215"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: 68e79fd371f2326cb390a307c4cbf901 ","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"2025-05-27T06:45:07.909Z  GET qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 12:15:07:157"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 68e79fd371f2326cb390a307c4cbf901","timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:50:36:5036"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 86fc654fe31165db6c7e6d29964b475d ","timestamp":"2025-05-27 12:56:31:5631"}
{"level":"info","message":"2025-05-27T07:26:31.447Z  POST qbo payment","timestamp":"2025-05-27 12:56:31:5631"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 12:56:31:5631"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 12:56:33:5633"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 86fc654fe31165db6c7e6d29964b475d","timestamp":"2025-05-27 12:56:33:5633"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:19:5819"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:33:5833"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:44:5844"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:58:5858"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:59:18:5918"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:59:53:5953"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:01:01"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:10:010"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:15:015"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:19:019"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:33:033"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 8a9ad1acefbcd60126b61b9037eb0504 ","timestamp":"2025-05-27 13:00:36:036"}
{"level":"info","message":"2025-05-27T07:30:36.811Z  POST qbo payment","timestamp":"2025-05-27 13:00:36:036"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:00:36:036"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:00:38:038"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 8a9ad1acefbcd60126b61b9037eb0504","timestamp":"2025-05-27 13:00:38:038"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:00:10"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:13:113"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:28:128"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:32:132"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:38:138"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: d3d64d2c14101b5a4b10f3e39137c9ee ","timestamp":"2025-05-27 13:01:41:141"}
{"level":"info","message":"2025-05-27T07:31:41.524Z  POST qbo payment","timestamp":"2025-05-27 13:01:41:141"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:01:41:141"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:01:43:143"}
{"level":"info","message":"Response sent with status: 400 - MessageID: d3d64d2c14101b5a4b10f3e39137c9ee","timestamp":"2025-05-27 13:01:43:143"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:02:06:26"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:02:39:239"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: e0df83fa570e7d661885f3e1090358f8 ","timestamp":"2025-05-27 13:02:40:240"}
{"level":"info","message":"2025-05-27T07:32:40.059Z  POST qbo payment","timestamp":"2025-05-27 13:02:40:240"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:02:40:240"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:02:41:241"}
{"level":"info","message":"Response sent with status: 400 - MessageID: e0df83fa570e7d661885f3e1090358f8","timestamp":"2025-05-27 13:02:41:241"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:03:01:31"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 7cce08f2d12576f829d33795181942b6 ","timestamp":"2025-05-27 13:03:04:34"}
{"level":"info","message":"2025-05-27T07:33:04.320Z  POST qbo payment","timestamp":"2025-05-27 13:03:04:34"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:03:04:34"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:03:05:35"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 7cce08f2d12576f829d33795181942b6","timestamp":"2025-05-27 13:03:05:35"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:39:439"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:48:448"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:53:453"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:05:02:52"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: bfb2a6c14503df03aa1b86c070bfb61d ","timestamp":"2025-05-27 13:05:07:57"}
{"level":"info","message":"2025-05-27T07:35:07.944Z  POST qbo payment","timestamp":"2025-05-27 13:05:07:57"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:05:07:57"}
{"code":105,"errorDescription":"Invalid Reference Id","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:05:10:510"}
{"level":"info","message":"Response sent with status: 400 - MessageID: bfb2a6c14503df03aa1b86c070bfb61d","timestamp":"2025-05-27 13:05:10:510"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 64c5f9d1ac24384f220e0703339ad95a ","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"2025-05-27T07:36:11.388Z  POST qbo payment","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 64c5f9d1ac24384f220e0703339ad95a","timestamp":"2025-05-27 13:06:12:612"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:07:16:716"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 38e8ceac9138f359fdc1fc533d225ee8 ","timestamp":"2025-05-27 13:07:20:720"}
{"level":"info","message":"2025-05-27T07:37:20.537Z  POST qbo payment","timestamp":"2025-05-27 13:07:20:720"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:07:20:720"}
{"code":105,"errorDescription":"Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id","stack":"Error: Invalid Reference Id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:07:22:722"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 38e8ceac9138f359fdc1fc533d225ee8","timestamp":"2025-05-27 13:07:22:722"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:08:06:86"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: a43a4ed744be7fd43a959ee93717decd ","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"2025-05-27T07:40:21.277Z  GET qbo payment","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a43a4ed744be7fd43a959ee93717decd","timestamp":"2025-05-27 13:10:23:1023"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 15:14:07:147"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 08b92a115652c9a96dea720bc2e50471 ","timestamp":"2025-05-27 15:14:19:1419"}
{"level":"info","message":"2025-05-27T09:44:19.638Z  GET qbo payment","timestamp":"2025-05-27 15:14:19:1419"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 15:14:19:1419"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:14:20:1420"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 08b92a115652c9a96dea720bc2e50471","timestamp":"2025-05-27 15:14:20:1420"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 16:08:12:812"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payments - MessageID: bb163994b9270546c732ce2cb3476a02 ","timestamp":"2025-05-27 16:08:50:850"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'payment' or 'PAYMENT'","level":"error","message":"GET /api/service?service=qbo&entity=payments: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:08:50:850"}
{"level":"info","message":"Response sent with status: 400 - MessageID: bb163994b9270546c732ce2cb3476a02","timestamp":"2025-05-27 16:08:50:850"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=paymeNT - MessageID: dbca30ab8a97709595844d1450875a29 ","timestamp":"2025-05-27 16:08:56:856"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'payment' or 'PAYMENT'","level":"error","message":"GET /api/service?service=qbo&entity=paymeNT: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:08:56:856"}
{"level":"info","message":"Response sent with status: 400 - MessageID: dbca30ab8a97709595844d1450875a29","timestamp":"2025-05-27 16:08:56:856"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=paymeNT - MessageID: cfb17649ffeaa513de6df05464a98288 ","timestamp":"2025-05-27 16:08:57:857"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'payment' or 'PAYMENT'","level":"error","message":"GET /api/service?service=qbo&entity=paymeNT: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:08:57:857"}
{"level":"info","message":"Response sent with status: 400 - MessageID: cfb17649ffeaa513de6df05464a98288","timestamp":"2025-05-27 16:08:57:857"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: d077dbfe4d9f576447da56b90ac5e1f8 ","timestamp":"2025-05-27 16:09:11:911"}
{"level":"info","message":"2025-05-27T10:39:11.145Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:09:11:911"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: 9341454441737033","timestamp":"2025-05-27 16:09:11:911"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:09:12:912"}
{"level":"info","message":"Response sent with status: 401 - MessageID: d077dbfe4d9f576447da56b90ac5e1f8","timestamp":"2025-05-27 16:09:12:912"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENt - MessageID: 9f251addd73439a4dec423a1be7654e3 ","timestamp":"2025-05-27 16:09:23:923"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'payment' or 'PAYMENT'","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENt: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:09:23:923"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 9f251addd73439a4dec423a1be7654e3","timestamp":"2025-05-27 16:09:23:923"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: 05bc4183940040085cf7fdbb20e79810 ","timestamp":"2025-05-27 16:09:45:945"}
{"level":"info","message":"2025-05-27T10:39:45.381Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:09:45:945"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: 9341454441737033","timestamp":"2025-05-27 16:09:45:945"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 05bc4183940040085cf7fdbb20e79810","timestamp":"2025-05-27 16:09:47:947"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: 1566c6a57fa03dab5cb16307a09d8794 ","timestamp":"2025-05-27 16:09:51:951"}
{"level":"info","message":"2025-05-27T10:39:51.464Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:09:51:951"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: 9341454441737","timestamp":"2025-05-27 16:09:51:951"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:09:51:951"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 1566c6a57fa03dab5cb16307a09d8794","timestamp":"2025-05-27 16:09:51:951"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: 11b8849737f3f4abcb49f289138f0c64 ","timestamp":"2025-05-27 16:09:55:955"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:09:55:955"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 11b8849737f3f4abcb49f289138f0c64","timestamp":"2025-05-27 16:09:55:955"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: efd58b2ef9de205ebceb179da09e50bd ","timestamp":"2025-05-27 16:10:25:1025"}
{"level":"info","message":"2025-05-27T10:40:25.789Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:10:25:1025"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: efjefjefjefjef","timestamp":"2025-05-27 16:10:25:1025"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:10:27:1027"}
{"level":"info","message":"Response sent with status: 500 - MessageID: efd58b2ef9de205ebceb179da09e50bd","timestamp":"2025-05-27 16:10:27:1027"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: a9880bb388e1444318d2015c76b18589 ","timestamp":"2025-05-27 16:10:36:1036"}
{"level":"info","message":"2025-05-27T10:40:36.681Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:10:36:1036"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: 9341454441737","timestamp":"2025-05-27 16:10:36:1036"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:10:38:1038"}
{"level":"info","message":"Response sent with status: 401 - MessageID: a9880bb388e1444318d2015c76b18589","timestamp":"2025-05-27 16:10:38:1038"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: e66618e2fea1ad10bf5d6e6b559aa6c0 ","timestamp":"2025-05-27 16:10:42:1042"}
{"level":"info","message":"2025-05-27T10:40:42.644Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:10:42:1042"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: xascascscc","timestamp":"2025-05-27 16:10:42:1042"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:10:42:1042"}
{"level":"info","message":"Response sent with status: 500 - MessageID: e66618e2fea1ad10bf5d6e6b559aa6c0","timestamp":"2025-05-27 16:10:42:1042"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: 0ae7977a5cc298137f178d48443a6215 ","timestamp":"2025-05-27 16:13:01:131"}
{"level":"info","message":"2025-05-27T10:43:01.415Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:13:01:131"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: 9341454441737","timestamp":"2025-05-27 16:13:01:131"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:13:02:132"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 0ae7977a5cc298137f178d48443a6215","timestamp":"2025-05-27 16:13:02:132"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: a8ec025c37871b726c55fe5eaf4bb3f1 ","timestamp":"2025-05-27 16:13:06:136"}
{"level":"info","message":"2025-05-27T10:43:06.730Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:13:06:136"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: 9341454441737033","timestamp":"2025-05-27 16:13:06:136"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a8ec025c37871b726c55fe5eaf4bb3f1","timestamp":"2025-05-27 16:13:07:137"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: c9fac73fafe97d2794ac0735807d48d8 ","timestamp":"2025-05-27 16:13:13:1313"}
{"level":"info","message":"2025-05-27T10:43:13.159Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:13:13:1313"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: 9341454441737033","timestamp":"2025-05-27 16:13:13:1313"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:13:14:1314"}
{"level":"info","message":"Response sent with status: 401 - MessageID: c9fac73fafe97d2794ac0735807d48d8","timestamp":"2025-05-27 16:13:14:1314"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: 1cf817bc1f48c62d06d8c056e8a222d8 ","timestamp":"2025-05-27 16:13:18:1318"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:13:18:1318"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1cf817bc1f48c62d06d8c056e8a222d8","timestamp":"2025-05-27 16:13:18:1318"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=PAYMENT - MessageID: 2267a68b3cb882390cadf4e1fc61b0e6 ","timestamp":"2025-05-27 16:13:26:1326"}
{"level":"info","message":"2025-05-27T10:43:26.407Z  GET qbo PAYMENT","timestamp":"2025-05-27 16:13:26:1326"}
{"level":"info","message":"QBO GET request for entity: PAYMENT | companyId: 9341454441737033","timestamp":"2025-05-27 16:13:26:1326"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=PAYMENT: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:13:27:1327"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 2267a68b3cb882390cadf4e1fc61b0e6","timestamp":"2025-05-27 16:13:27:1327"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 175da35accc4d2f2eee284fd477bd249 ","timestamp":"2025-05-27 16:15:48:1548"}
{"level":"info","message":"2025-05-27T10:45:48.211Z  GET qbo payment","timestamp":"2025-05-27 16:15:48:1548"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: aasasas","timestamp":"2025-05-27 16:15:48:1548"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:15:49:1549"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 175da35accc4d2f2eee284fd477bd249","timestamp":"2025-05-27 16:15:49:1549"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 9854a749470ab87b95dd6f14307cec7b ","timestamp":"2025-05-27 16:16:02:162"}
{"level":"info","message":"2025-05-27T10:46:02.761Z  GET qbo payment","timestamp":"2025-05-27 16:16:02:162"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: aasasassfsfssssfsfsf","timestamp":"2025-05-27 16:16:02:162"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:16:03:163"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 9854a749470ab87b95dd6f14307cec7b","timestamp":"2025-05-27 16:16:03:163"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 49ec2fb4aee6c186e91caac46ed41c9a ","timestamp":"2025-05-27 16:16:25:1625"}
{"level":"info","message":"2025-05-27T10:46:25.012Z  GET qbo payment","timestamp":"2025-05-27 16:16:25:1625"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 16:16:25:1625"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:16:25:1625"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 49ec2fb4aee6c186e91caac46ed41c9a","timestamp":"2025-05-27 16:16:25:1625"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: ac8d8e2713f6ab0bf5fa4e01af55303b ","timestamp":"2025-05-27 16:16:52:1652"}
{"level":"info","message":"2025-05-27T10:46:52.980Z  GET qbo payment","timestamp":"2025-05-27 16:16:52:1652"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 16:16:52:1652"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ac8d8e2713f6ab0bf5fa4e01af55303b","timestamp":"2025-05-27 16:16:53:1653"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: e6ab690c9841e99e7defb25a5f360326 ","timestamp":"2025-05-27 16:17:00:170"}
{"level":"info","message":"2025-05-27T10:47:00.035Z  GET qbo payment","timestamp":"2025-05-27 16:17:00:170"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: fdfsfdsfdsfsdfdfsdf","timestamp":"2025-05-27 16:17:00:170"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:17:01:171"}
{"level":"info","message":"Response sent with status: 500 - MessageID: e6ab690c9841e99e7defb25a5f360326","timestamp":"2025-05-27 16:17:01:171"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 16:21:01:211"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 8585f851fe94481a9e0221acf6fcfacc ","timestamp":"2025-05-27 16:21:09:219"}
{"level":"info","message":"2025-05-27T10:51:09.378Z  GET qbo payment","timestamp":"2025-05-27 16:21:09:219"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: fdfsfdsfdsfsdfdfsdf","timestamp":"2025-05-27 16:21:09:219"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:21:10:2110"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 8585f851fe94481a9e0221acf6fcfacc","timestamp":"2025-05-27 16:21:10:2110"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 16:21:48:2148"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: a8875d7c8ce8355a168a984f6fe142bd ","timestamp":"2025-05-27 16:21:50:2150"}
{"level":"info","message":"2025-05-27T10:51:50.166Z  GET qbo payment","timestamp":"2025-05-27 16:21:50:2150"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: fdfsfdsfdsfsdfdfsdf","timestamp":"2025-05-27 16:21:50:2150"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:51:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:21:51:2151"}
{"level":"info","message":"Response sent with status: 500 - MessageID: a8875d7c8ce8355a168a984f6fe142bd","timestamp":"2025-05-27 16:21:51:2151"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 16:24:55:2455"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 16:24:59:2459"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 386f88a700e29dc0cb865b08a07e09d2 ","timestamp":"2025-05-27 16:25:03:253"}
{"level":"info","message":"2025-05-27T10:55:03.152Z  GET qbo payment","timestamp":"2025-05-27 16:25:03:253"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: fdfsfdsfdsfsdfdfsdf","timestamp":"2025-05-27 16:25:03:253"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:53:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:25:04:254"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 386f88a700e29dc0cb865b08a07e09d2","timestamp":"2025-05-27 16:25:04:254"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 16:25:47:2547"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 79ce5ab3e08376fd07de82c5658cd89a ","timestamp":"2025-05-27 16:26:07:267"}
{"level":"info","message":"2025-05-27T10:56:07.261Z  GET qbo payment","timestamp":"2025-05-27 16:26:07:267"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: fdfsfdsfdsfsdfdfsdf","timestamp":"2025-05-27 16:26:07:267"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:54:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:26:08:268"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 79ce5ab3e08376fd07de82c5658cd89a","timestamp":"2025-05-27 16:26:08:268"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: bcf2a6ff10f3bd5f183e285ebe9c71ea ","timestamp":"2025-05-27 16:27:02:272"}
{"level":"info","message":"2025-05-27T10:57:02.088Z  GET qbo payment","timestamp":"2025-05-27 16:27:02:272"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 16:27:02:272"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:27:03:273"}
{"level":"info","message":"Response sent with status: 401 - MessageID: bcf2a6ff10f3bd5f183e285ebe9c71ea","timestamp":"2025-05-27 16:27:03:273"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 16:28:10:2810"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 16:28:17:2817"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: e640cd36749c341497f77bbcfaae1347 ","timestamp":"2025-05-27 16:28:23:2823"}
{"level":"info","message":"2025-05-27T10:58:23.060Z  GET qbo payment","timestamp":"2025-05-27 16:28:23:2823"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 16:28:23:2823"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 16:28:24:2824"}
{"level":"info","message":"Response sent with status: 401 - MessageID: e640cd36749c341497f77bbcfaae1347","timestamp":"2025-05-27 16:28:24:2824"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 510f46839ef94f318da355b0147346a1 ","timestamp":"2025-05-27 16:28:37:2837"}
{"level":"info","message":"2025-05-27T10:58:37.093Z  GET qbo payment","timestamp":"2025-05-27 16:28:37:2837"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033asdasda","timestamp":"2025-05-27 16:28:37:2837"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:54:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:28:38:2838"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 510f46839ef94f318da355b0147346a1","timestamp":"2025-05-27 16:28:38:2838"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:11:40:1140"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 94d7ad78a3d730ba917ffc4238416548 ","timestamp":"2025-05-27 17:12:24:1224"}
{"level":"info","message":"2025-05-27T11:42:24.979Z  GET qbo payment","timestamp":"2025-05-27 17:12:24:1224"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033asdasda","timestamp":"2025-05-27 17:12:24:1224"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:54:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:12:26:1226"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 94d7ad78a3d730ba917ffc4238416548","timestamp":"2025-05-27 17:12:26:1226"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 6b24bc19b3a514f26bb1d44d91a37d7e ","timestamp":"2025-05-27 17:12:35:1235"}
{"level":"info","message":"2025-05-27T11:42:35.817Z  GET qbo payment","timestamp":"2025-05-27 17:12:35:1235"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 93414544417370","timestamp":"2025-05-27 17:12:35:1235"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:12:37:1237"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 6b24bc19b3a514f26bb1d44d91a37d7e","timestamp":"2025-05-27 17:12:37:1237"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: b0674f5adf3f6965f94b584c980a3916 ","timestamp":"2025-05-27 17:14:22:1422"}
{"level":"info","message":"2025-05-27T11:44:22.398Z  GET qbo payment","timestamp":"2025-05-27 17:14:22:1422"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 93414544417370ffsfsfs","timestamp":"2025-05-27 17:14:22:1422"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:14:23:1423"}
{"level":"info","message":"Response sent with status: 401 - MessageID: b0674f5adf3f6965f94b584c980a3916","timestamp":"2025-05-27 17:14:23:1423"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 118f56795ce40622d039198f02fca41c ","timestamp":"2025-05-27 17:14:29:1429"}
{"level":"info","message":"2025-05-27T11:44:29.994Z  GET qbo payment","timestamp":"2025-05-27 17:14:29:1429"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 93414544417370ffsfsfsghghghg","timestamp":"2025-05-27 17:14:29:1429"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:14:31:1431"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 118f56795ce40622d039198f02fca41c","timestamp":"2025-05-27 17:14:31:1431"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 71621f377d823a96e77e6d13d581358d ","timestamp":"2025-05-27 17:14:36:1436"}
{"level":"info","message":"2025-05-27T11:44:36.587Z  GET qbo payment","timestamp":"2025-05-27 17:14:36:1436"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454470ffsfsfsghghghg","timestamp":"2025-05-27 17:14:36:1436"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:14:38:1438"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 71621f377d823a96e77e6d13d581358d","timestamp":"2025-05-27 17:14:38:1438"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: c9aa7882ec629ea330bb780426701405 ","timestamp":"2025-05-27 17:14:44:1444"}
{"level":"info","message":"2025-05-27T11:44:44.202Z  GET qbo payment","timestamp":"2025-05-27 17:14:44:1444"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: gfgfgfgfgfg","timestamp":"2025-05-27 17:14:44:1444"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:54:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:14:45:1445"}
{"level":"info","message":"Response sent with status: 500 - MessageID: c9aa7882ec629ea330bb780426701405","timestamp":"2025-05-27 17:14:45:1445"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:26:20:2620"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: b4eb794bf37be0cbb88ca30154076dd2 ","timestamp":"2025-05-27 17:26:21:2621"}
{"level":"info","message":"2025-05-27T11:56:21.664Z  GET qbo payment","timestamp":"2025-05-27 17:26:21:2621"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: gfgfgfgfgfg","timestamp":"2025-05-27 17:26:21:2621"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:54:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:26:22:2622"}
{"level":"info","message":"Response sent with status: 500 - MessageID: b4eb794bf37be0cbb88ca30154076dd2","timestamp":"2025-05-27 17:26:22:2622"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: ade78ebeaf79a10c04bc99209d08c9a3 ","timestamp":"2025-05-27 17:27:31:2731"}
{"level":"info","message":"2025-05-27T11:57:31.097Z  GET qbo payment","timestamp":"2025-05-27 17:27:31:2731"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454470ffsfsfsghghghg","timestamp":"2025-05-27 17:27:31:2731"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:27:32:2732"}
{"level":"info","message":"Response sent with status: 401 - MessageID: ade78ebeaf79a10c04bc99209d08c9a3","timestamp":"2025-05-27 17:27:32:2732"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 331adbc22d6c960bdc37eb628bace0b6 ","timestamp":"2025-05-27 17:28:52:2852"}
{"level":"info","message":"2025-05-27T11:58:52.981Z  GET qbo payment","timestamp":"2025-05-27 17:28:52:2852"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: tytytytyjyhjy","timestamp":"2025-05-27 17:28:52:2852"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:54:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:28:54:2854"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 331adbc22d6c960bdc37eb628bace0b6","timestamp":"2025-05-27 17:28:54:2854"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 42df3392f8f6ffbe4546f01d3a05fd54 ","timestamp":"2025-05-27 17:30:55:3055"}
{"level":"info","message":"2025-05-27T12:00:55.628Z  GET qbo payment","timestamp":"2025-05-27 17:30:55:3055"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 93414544417370ffsfsfs","timestamp":"2025-05-27 17:30:55:3055"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:30:57:3057"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 42df3392f8f6ffbe4546f01d3a05fd54","timestamp":"2025-05-27 17:30:57:3057"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: bab3b3796968512ca061a48e134bca75 ","timestamp":"2025-05-27 17:31:22:3122"}
{"level":"info","message":"2025-05-27T12:01:22.743Z  GET qbo payment","timestamp":"2025-05-27 17:31:22:3122"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: ffeefefef12345679io0098765432","timestamp":"2025-05-27 17:31:22:3122"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:54:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:31:24:3124"}
{"level":"info","message":"Response sent with status: 500 - MessageID: bab3b3796968512ca061a48e134bca75","timestamp":"2025-05-27 17:31:24:3124"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:33:10:3310"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:33:21:3321"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:33:29:3329"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: cb74faf807d11ade678b8ef41928aac7 ","timestamp":"2025-05-27 17:35:02:352"}
{"level":"info","message":"2025-05-27T12:05:02.762Z  GET qbo payment","timestamp":"2025-05-27 17:35:02:352"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 17:35:02:352"}
{"level":"info","message":"Response sent with status: 200 - MessageID: cb74faf807d11ade678b8ef41928aac7","timestamp":"2025-05-27 17:35:04:354"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 681b17587d1482c0a5f62a6467ee550b ","timestamp":"2025-05-27 17:35:35:3535"}
{"level":"info","message":"2025-05-27T12:05:35.051Z  GET qbo payment","timestamp":"2025-05-27 17:35:35:3535"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033456546dfdfgfgdfgdfg","timestamp":"2025-05-27 17:35:35:3535"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:35:36:3536"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 681b17587d1482c0a5f62a6467ee550b","timestamp":"2025-05-27 17:35:36:3536"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: f331477cd66e77a6b1c8be106b4fabd8 ","timestamp":"2025-05-27 17:36:11:3611"}
{"level":"info","message":"2025-05-27T12:06:11.823Z  GET qbo payment","timestamp":"2025-05-27 17:36:11:3611"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: fgfgdfgdf56546456546","timestamp":"2025-05-27 17:36:11:3611"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:36:13:3613"}
{"level":"info","message":"Response sent with status: 500 - MessageID: f331477cd66e77a6b1c8be106b4fabd8","timestamp":"2025-05-27 17:36:13:3613"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:45:39:4539"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:45:58:4558"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:47:46:4746"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:54:04:544"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 1da5359f01121885486adad6ddeb5a6d ","timestamp":"2025-05-27 17:54:26:5426"}
{"level":"info","message":"2025-05-27T12:24:26.501Z  GET qbo payment","timestamp":"2025-05-27 17:54:26:5426"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: fgfgdfgdf56546456546","timestamp":"2025-05-27 17:54:26:5426"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:62:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:54:28:5428"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 1da5359f01121885486adad6ddeb5a6d","timestamp":"2025-05-27 17:54:28:5428"}
{"level":"info","message":"Server is listening on port 8004","timestamp":"2025-05-27 17:55:46:5546"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 5296fa54f7646e4c23d2953e47340af2 ","timestamp":"2025-05-27 17:56:50:5650"}
{"level":"info","message":"2025-05-27T12:26:50.955Z  GET qbo payment","timestamp":"2025-05-27 17:56:50:5650"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: dfggfgrgrgergregerg","timestamp":"2025-05-27 17:56:50:5650"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=payment: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:62:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:56:52:5652"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 5296fa54f7646e4c23d2953e47340af2","timestamp":"2025-05-27 17:56:52:5652"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 8c5cae89fca981ee1d5611a5d7dff64d ","timestamp":"2025-05-27 17:57:22:5722"}
{"level":"info","message":"2025-05-27T12:27:22.859Z  GET qbo payment","timestamp":"2025-05-27 17:57:22:5722"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 17:57:22:5722"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8c5cae89fca981ee1d5611a5d7dff64d","timestamp":"2025-05-27 17:57:24:5724"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: 3f0caa94a2674b8487cf9d587b7d73b3 ","timestamp":"2025-05-27 17:57:45:5745"}
{"level":"info","message":"2025-05-27T12:27:45.491Z  GET qbo payment","timestamp":"2025-05-27 17:57:45:5745"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 93414544417370335675675675677","timestamp":"2025-05-27 17:57:45:5745"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=payment: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:54:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:57:47:5747"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 3f0caa94a2674b8487cf9d587b7d73b3","timestamp":"2025-05-27 17:57:47:5747"}
