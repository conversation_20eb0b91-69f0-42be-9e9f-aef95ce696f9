{"name": "api-gateway", "version": "1.0.0", "description": "API Gateway for Zact microservices", "main": "src/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --legacy-watch app/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@types/body-parser": "^1.19.5", "@types/dotenv": "^6.1.1", "@types/express-validator": "^2.20.33", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "http-proxy-middleware": "^2.0.6", "md5": "^2.3.0", "morgan": "^1.10.0", "prom-client": "^15.1.3", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/helmet": "^0.0.48", "@types/md5": "^2.3.5", "@types/morgan": "^1.9.9", "@types/node": "^22.15.21", "@types/uuid": "^10.0.0", "nodemon": "^3.0.1", "typescript": "^5.8.3"}}