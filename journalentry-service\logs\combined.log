0{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 11:35:37:3537"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: bcd74e14f27155c8fb4388314af0fbcb ","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"2025-05-27T06:08:20.541Z  GET qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"Response sent with status: 200 - MessageID: bcd74e14f27155c8fb4388314af0fbcb","timestamp":"2025-05-27 11:38:23:3823"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: 1df82a54c249516e5c981c84874caace ","timestamp":"2025-05-27 11:42:34:4234"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"POST /api/service?service=qbo&entity=bill: Bad request","stack":"Error: Bad request\n    at validateAuthParams (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:40:11)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:57:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:51:52\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1df82a54c249516e5c981c84874caace","timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:11:37:1137"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313 ","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"2025-05-27T06:42:11.889Z  POST qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"QBO POST request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313","timestamp":"2025-05-27 12:12:15:1215"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: 68e79fd371f2326cb390a307c4cbf901 ","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"2025-05-27T06:45:07.909Z  GET qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 12:15:07:157"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 68e79fd371f2326cb390a307c4cbf901","timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:18:48:1848"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:37:05:375"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:39:10:3910"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:40:02:402"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:40:26:4026"}
{"level":"info","message":"Server is listening on port 3000","timestamp":"2025-05-27 14:42:34:4234"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 81cdab5b183dbf304dd5477fff4c9467 ","timestamp":"2025-05-27 14:42:45:4245"}
{"level":"info","message":"2025-05-27T09:12:45.147Z  GET qbo journalentry","timestamp":"2025-05-27 14:42:45:4245"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 14:42:45:4245"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 14:42:46:4246"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 81cdab5b183dbf304dd5477fff4c9467","timestamp":"2025-05-27 14:42:46:4246"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 52360b75a6aae730f05f3d0128f70580 ","timestamp":"2025-05-27 14:45:16:4516"}
{"level":"info","message":"2025-05-27T09:15:16.701Z  GET qbo journalentry","timestamp":"2025-05-27 14:45:16:4516"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 14:45:16:4516"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 52360b75a6aae730f05f3d0128f70580","timestamp":"2025-05-27 14:45:18:4518"}
{"level":"info","message":"Server is listening on port 3000","timestamp":"2025-05-27 15:04:15:415"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 6fc9dec7cc4af5c2d8aaa88e84f8f41e ","timestamp":"2025-05-27 15:04:18:418"}
{"level":"info","message":"2025-05-27T09:34:18.754Z  GET qbo journalentry","timestamp":"2025-05-27 15:04:18:418"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:04:18:418"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6fc9dec7cc4af5c2d8aaa88e84f8f41e","timestamp":"2025-05-27 15:04:20:420"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: f64afc8ff62100bacfa6a938b6ce98f7 ","timestamp":"2025-05-27 15:04:43:443"}
{"level":"info","message":"2025-05-27T09:34:43.627Z  GET qbo journalentry","timestamp":"2025-05-27 15:04:43:443"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:04:43:443"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f64afc8ff62100bacfa6a938b6ce98f7","timestamp":"2025-05-27 15:04:45:445"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 68c606302a8bbd565feed876d6628368 ","timestamp":"2025-05-27 15:19:43:1943"}
{"level":"info","message":"2025-05-27T09:49:43.012Z  GET qbo journalentry","timestamp":"2025-05-27 15:19:43:1943"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:19:43:1943"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 68c606302a8bbd565feed876d6628368","timestamp":"2025-05-27 15:19:44:1944"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=journalentry - MessageID: bc6f249cb9ee2745b21efdd3aa7cc874 ","timestamp":"2025-05-27 15:19:58:1958"}
{"level":"info","message":"2025-05-27T09:49:58.934Z  POST qbo journalentry","timestamp":"2025-05-27 15:19:58:1958"}
{"level":"info","message":"QBO POST request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:19:58:1958"}
{"level":"info","message":"Response sent with status: 200 - MessageID: bc6f249cb9ee2745b21efdd3aa7cc874","timestamp":"2025-05-27 15:20:00:200"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: c1f00a3ef7ae41cdd35a21afe395bcf4 ","timestamp":"2025-05-27 15:20:16:2016"}
{"level":"info","message":"2025-05-27T09:50:16.964Z  GET qbo journalentry","timestamp":"2025-05-27 15:20:16:2016"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:20:16:2016"}
{"level":"info","message":"Response sent with status: 200 - MessageID: c1f00a3ef7ae41cdd35a21afe395bcf4","timestamp":"2025-05-27 15:20:18:2018"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=journalentry - MessageID: 2ced02e3e94e3b4b8a409fe207e2aa10 ","timestamp":"2025-05-27 15:23:25:2325"}
{"level":"info","message":"2025-05-27T09:53:25.286Z  POST qbo journalentry","timestamp":"2025-05-27 15:23:25:2325"}
{"level":"info","message":"QBO POST request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:23:25:2325"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=journalentry: Required parameter Line.DetailType is missing in the request","stack":"Error: Required parameter Line.DetailType is missing in the request\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 15:23:26:2326"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 2ced02e3e94e3b4b8a409fe207e2aa10","timestamp":"2025-05-27 15:23:26:2326"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 15:25:04:254"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 6da3c4deb06e964d3ab4448c24176cfb ","timestamp":"2025-05-27 15:25:08:258"}
{"level":"info","message":"2025-05-27T09:55:08.441Z  GET qbo journalentry","timestamp":"2025-05-27 15:25:08:258"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:25:08:258"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6da3c4deb06e964d3ab4448c24176cfb","timestamp":"2025-05-27 15:25:09:259"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=journalentry - MessageID: d1c07e43f6f9a2f6191b104ff11dc1c2 ","timestamp":"2025-05-27 15:30:16:3016"}
{"level":"info","message":"2025-05-27T10:00:16.927Z  POST qbo journalentry","timestamp":"2025-05-27 15:30:16:3016"}
{"level":"info","message":"QBO POST request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:30:16:3016"}
{"level":"info","message":"Response sent with status: 200 - MessageID: d1c07e43f6f9a2f6191b104ff11dc1c2","timestamp":"2025-05-27 15:30:18:3018"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: f26e8afd7d058b14920c691340536fee ","timestamp":"2025-05-27 15:30:22:3022"}
{"level":"info","message":"2025-05-27T10:00:22.809Z  GET qbo journalentry","timestamp":"2025-05-27 15:30:22:3022"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:30:22:3022"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f26e8afd7d058b14920c691340536fee","timestamp":"2025-05-27 15:30:23:3023"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 65bd5fdbf7313ea99b3d414622eaeb8d ","timestamp":"2025-05-27 15:59:32:5932"}
{"level":"info","message":"2025-05-27T10:29:32.690Z  GET qbo journalentry","timestamp":"2025-05-27 15:59:32:5932"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 15:59:32:5932"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 65bd5fdbf7313ea99b3d414622eaeb8d","timestamp":"2025-05-27 15:59:34:5934"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 16:01:31:131"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 16:01:35:135"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journal - MessageID: c35f22104695071b07c36dc58c721a2b ","timestamp":"2025-05-27 16:02:21:221"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'journalentry' or 'JOURNALENTRY'","level":"error","message":"GET /api/service?service=qbo&entity=journal: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:02:21:221"}
{"level":"info","message":"Response sent with status: 400 - MessageID: c35f22104695071b07c36dc58c721a2b","timestamp":"2025-05-27 16:02:21:221"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 24c0df3402080642b0859dd39e8d40fb ","timestamp":"2025-05-27 16:02:31:231"}
{"level":"info","message":"2025-05-27T10:32:31.477Z  GET qbo journalentry","timestamp":"2025-05-27 16:02:31:231"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 16:02:31:231"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 24c0df3402080642b0859dd39e8d40fb","timestamp":"2025-05-27 16:02:33:233"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 16:03:52:352"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:12:33:1233"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:12:39:1239"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 0f6af5df3319df4ee7fe8ea5a9d1a006 ","timestamp":"2025-05-27 17:27:51:2751"}
{"level":"info","message":"2025-05-27T11:57:51.914Z  GET qbo journalentry","timestamp":"2025-05-27 17:27:51:2751"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:27:51:2751"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:27:53:2753"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 0f6af5df3319df4ee7fe8ea5a9d1a006","timestamp":"2025-05-27 17:27:53:2753"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 5b41f258cc2758d8acf580ab7f4eae63 ","timestamp":"2025-05-27 17:27:57:2757"}
{"level":"info","message":"2025-05-27T11:57:57.894Z  GET qbo journalentry","timestamp":"2025-05-27 17:27:57:2757"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:27:57:2757"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:27:58:2758"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 5b41f258cc2758d8acf580ab7f4eae63","timestamp":"2025-05-27 17:27:58:2758"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 0a920220fd7d8d0204e1c18f25e96279 ","timestamp":"2025-05-27 17:28:03:283"}
{"level":"info","message":"2025-05-27T11:58:03.482Z  GET qbo journalentry","timestamp":"2025-05-27 17:28:03:283"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:28:03:283"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:28:04:284"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 0a920220fd7d8d0204e1c18f25e96279","timestamp":"2025-05-27 17:28:04:284"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: e0d8c2fff61031aeadd011d2038eaf07 ","timestamp":"2025-05-27 17:28:13:2813"}
{"level":"info","message":"2025-05-27T11:58:13.790Z  GET qbo journalentry","timestamp":"2025-05-27 17:28:13:2813"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:28:13:2813"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:28:15:2815"}
{"level":"info","message":"Response sent with status: 401 - MessageID: e0d8c2fff61031aeadd011d2038eaf07","timestamp":"2025-05-27 17:28:15:2815"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 6c8ceee88187430bdb97963443ac4209 ","timestamp":"2025-05-27 17:29:11:2911"}
{"level":"info","message":"2025-05-27T11:59:11.572Z  GET qbo journalentry","timestamp":"2025-05-27 17:29:11:2911"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:29:11:2911"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6c8ceee88187430bdb97963443ac4209","timestamp":"2025-05-27 17:29:13:2913"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 1c3a85402e417ae16d2a7fadb19755e2 ","timestamp":"2025-05-27 17:29:58:2958"}
{"level":"info","message":"2025-05-27T11:59:58.522Z  GET qbo journalentry","timestamp":"2025-05-27 17:29:58:2958"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:29:58:2958"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:30:00:300"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 1c3a85402e417ae16d2a7fadb19755e2","timestamp":"2025-05-27 17:30:00:300"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 5128a9482e985cb71af4644030fa6a4f ","timestamp":"2025-05-27 17:30:12:3012"}
{"level":"info","message":"2025-05-27T12:00:12.231Z  GET qbo journalentry","timestamp":"2025-05-27 17:30:12:3012"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:30:12:3012"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:30:13:3013"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 5128a9482e985cb71af4644030fa6a4f","timestamp":"2025-05-27 17:30:13:3013"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: b2616afa2eb03d882d611a9880915429 ","timestamp":"2025-05-27 17:30:31:3031"}
{"level":"info","message":"2025-05-27T12:00:31.253Z  GET qbo journalentry","timestamp":"2025-05-27 17:30:31:3031"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:30:31:3031"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b2616afa2eb03d882d611a9880915429","timestamp":"2025-05-27 17:30:33:3033"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 5e6d9cae76041db1e9c8b369036d51ed ","timestamp":"2025-05-27 17:30:36:3036"}
{"level":"info","message":"2025-05-27T12:00:36.266Z  GET qbo journalentry","timestamp":"2025-05-27 17:30:36:3036"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:30:36:3036"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:30:36:3036"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 5e6d9cae76041db1e9c8b369036d51ed","timestamp":"2025-05-27 17:30:36:3036"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 6d74d0b375d4d513440100804fc9a78e ","timestamp":"2025-05-27 17:31:32:3132"}
{"level":"info","message":"2025-05-27T12:01:32.227Z  GET qbo journalentry","timestamp":"2025-05-27 17:31:32:3132"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:31:32:3132"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:31:33:3133"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 6d74d0b375d4d513440100804fc9a78e","timestamp":"2025-05-27 17:31:33:3133"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: dd219e2eb0ede759b7b7e5bd40f6d879 ","timestamp":"2025-05-27 17:32:03:323"}
{"level":"info","message":"2025-05-27T12:02:03.639Z  GET qbo journalentry","timestamp":"2025-05-27 17:32:03:323"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:32:03:323"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:32:05:325"}
{"level":"info","message":"Response sent with status: 500 - MessageID: dd219e2eb0ede759b7b7e5bd40f6d879","timestamp":"2025-05-27 17:32:05:325"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 0bc7a6ee6d1e0e5137d2f6809171d7d6 ","timestamp":"2025-05-27 17:32:32:3232"}
{"level":"info","message":"2025-05-27T12:02:32.071Z  GET qbo journalentry","timestamp":"2025-05-27 17:32:32:3232"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gggjtjj","timestamp":"2025-05-27 17:32:32:3232"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:32:33:3233"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 0bc7a6ee6d1e0e5137d2f6809171d7d6","timestamp":"2025-05-27 17:32:33:3233"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:33:24:3324"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: dab6332583d99aa45fae236308b73891 ","timestamp":"2025-05-27 17:33:28:3328"}
{"level":"info","message":"2025-05-27T12:03:28.941Z  GET qbo journalentry","timestamp":"2025-05-27 17:33:28:3328"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:33:28:3328"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:33:30:3330"}
{"level":"info","message":"Response sent with status: 500 - MessageID: dab6332583d99aa45fae236308b73891","timestamp":"2025-05-27 17:33:30:3330"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 342ea7fe5034a18101bab48b67ef83ce ","timestamp":"2025-05-27 17:34:05:345"}
{"level":"info","message":"2025-05-27T12:04:05.189Z  GET qbo journalentry","timestamp":"2025-05-27 17:34:05:345"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:34:05:345"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:34:06:346"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 342ea7fe5034a18101bab48b67ef83ce","timestamp":"2025-05-27 17:34:06:346"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: dd953dfa2aa213427684df89def7a76f ","timestamp":"2025-05-27 17:34:48:3448"}
{"level":"info","message":"2025-05-27T12:04:48.138Z  GET qbo journalentry","timestamp":"2025-05-27 17:34:48:3448"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:34:48:3448"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:34:49:3449"}
{"level":"info","message":"Response sent with status: 401 - MessageID: dd953dfa2aa213427684df89def7a76f","timestamp":"2025-05-27 17:34:49:3449"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: a72087a3a779b95f5005dd9646849675 ","timestamp":"2025-05-27 17:35:01:351"}
{"level":"info","message":"2025-05-27T12:05:01.881Z  GET qbo journalentry","timestamp":"2025-05-27 17:35:01:351"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 93414544538740","timestamp":"2025-05-27 17:35:01:351"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:35:03:353"}
{"level":"info","message":"Response sent with status: 401 - MessageID: a72087a3a779b95f5005dd9646849675","timestamp":"2025-05-27 17:35:03:353"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 3ff897595511f1d4ae6f5d026456848f ","timestamp":"2025-05-27 17:35:12:3512"}
{"level":"info","message":"2025-05-27T12:05:12.006Z  GET qbo journalentry","timestamp":"2025-05-27 17:35:12:3512"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:35:12:3512"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:35:13:3513"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 3ff897595511f1d4ae6f5d026456848f","timestamp":"2025-05-27 17:35:13:3513"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: f77fe8268f92c3d08e2221153592cf47 ","timestamp":"2025-05-27 17:35:19:3519"}
{"level":"info","message":"2025-05-27T12:05:19.998Z  GET qbo journalentry","timestamp":"2025-05-27 17:35:19:3519"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: ggggg","timestamp":"2025-05-27 17:35:19:3519"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:35:21:3521"}
{"level":"info","message":"Response sent with status: 500 - MessageID: f77fe8268f92c3d08e2221153592cf47","timestamp":"2025-05-27 17:35:21:3521"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 081ac65e7dd02b7e0e5276b1d472d2b6 ","timestamp":"2025-05-27 17:35:32:3532"}
{"level":"info","message":"2025-05-27T12:05:32.097Z  GET qbo journalentry","timestamp":"2025-05-27 17:35:32:3532"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:35:32:3532"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 081ac65e7dd02b7e0e5276b1d472d2b6","timestamp":"2025-05-27 17:35:33:3533"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 88fca72aadd50d6ba11cd55d0ebff0c6 ","timestamp":"2025-05-27 17:36:22:3622"}
{"level":"info","message":"2025-05-27T12:06:22.399Z  GET qbo journalentry","timestamp":"2025-05-27 17:36:22:3622"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:36:22:3622"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:36:23:3623"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 88fca72aadd50d6ba11cd55d0ebff0c6","timestamp":"2025-05-27 17:36:23:3623"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: da8a4efe813bdfc692280bc3ca1133f0 ","timestamp":"2025-05-27 17:36:42:3642"}
{"level":"info","message":"2025-05-27T12:06:42.034Z  GET qbo journalentry","timestamp":"2025-05-27 17:36:42:3642"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: uu9341454453874086","timestamp":"2025-05-27 17:36:42:3642"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:36:43:3643"}
{"level":"info","message":"Response sent with status: 500 - MessageID: da8a4efe813bdfc692280bc3ca1133f0","timestamp":"2025-05-27 17:36:43:3643"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 61851e6dd0c5dd8b52cae4258ef41115 ","timestamp":"2025-05-27 17:36:52:3652"}
{"level":"info","message":"2025-05-27T12:06:52.588Z  GET qbo journalentry","timestamp":"2025-05-27 17:36:52:3652"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:36:52:3652"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 61851e6dd0c5dd8b52cae4258ef41115","timestamp":"2025-05-27 17:36:54:3654"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 5f688ab8dbdeac303a72bdd4d77026bd ","timestamp":"2025-05-27 17:36:58:3658"}
{"level":"info","message":"2025-05-27T12:06:58.727Z  GET qbo journalentry","timestamp":"2025-05-27 17:36:58:3658"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:36:58:3658"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:36:59:3659"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 5f688ab8dbdeac303a72bdd4d77026bd","timestamp":"2025-05-27 17:36:59:3659"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:37:53:3753"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: c00b92a20aebb69ef82c9455c87d2253 ","timestamp":"2025-05-27 17:38:00:380"}
{"level":"info","message":"2025-05-27T12:08:00.017Z  GET qbo journalentry","timestamp":"2025-05-27 17:38:00:380"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:38:00:380"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:38:01:381"}
{"level":"info","message":"Response sent with status: 500 - MessageID: c00b92a20aebb69ef82c9455c87d2253","timestamp":"2025-05-27 17:38:01:381"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: daf240ffa22f6cf888ba7c7544842f14 ","timestamp":"2025-05-27 17:38:24:3824"}
{"level":"info","message":"2025-05-27T12:08:24.164Z  GET qbo journalentry","timestamp":"2025-05-27 17:38:24:3824"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: HGHGSCCDV9341454453874086","timestamp":"2025-05-27 17:38:24:3824"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:38:25:3825"}
{"level":"info","message":"Response sent with status: 500 - MessageID: daf240ffa22f6cf888ba7c7544842f14","timestamp":"2025-05-27 17:38:25:3825"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: c3b304628656489f5d0d55f51706d14a ","timestamp":"2025-05-27 17:38:57:3857"}
{"level":"info","message":"2025-05-27T12:08:57.266Z  GET qbo journalentry","timestamp":"2025-05-27 17:38:57:3857"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:38:57:3857"}
{"level":"info","message":"Response sent with status: 200 - MessageID: c3b304628656489f5d0d55f51706d14a","timestamp":"2025-05-27 17:38:58:3858"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 82832b8e66335bde9d1c971656c5d04c ","timestamp":"2025-05-27 17:39:22:3922"}
{"level":"info","message":"2025-05-27T12:09:22.855Z  GET qbo journalentry","timestamp":"2025-05-27 17:39:22:3922"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086GG","timestamp":"2025-05-27 17:39:22:3922"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:39:24:3924"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 82832b8e66335bde9d1c971656c5d04c","timestamp":"2025-05-27 17:39:24:3924"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 71be8436e4ecccb0bfe417930aac0240 ","timestamp":"2025-05-27 17:39:56:3956"}
{"level":"info","message":"2025-05-27T12:09:56.786Z  GET qbo journalentry","timestamp":"2025-05-27 17:39:56:3956"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086GG","timestamp":"2025-05-27 17:39:56:3956"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:39:57:3957"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 71be8436e4ecccb0bfe417930aac0240","timestamp":"2025-05-27 17:39:57:3957"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: ec85dcf71a4dfd251aa52eaa8f821d92 ","timestamp":"2025-05-27 17:40:42:4042"}
{"level":"info","message":"2025-05-27T12:10:42.584Z  GET qbo journalentry","timestamp":"2025-05-27 17:40:42:4042"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:40:42:4042"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:40:44:4044"}
{"level":"info","message":"Response sent with status: 500 - MessageID: ec85dcf71a4dfd251aa52eaa8f821d92","timestamp":"2025-05-27 17:40:44:4044"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 99aa951e346af239797878ac2eee4964 ","timestamp":"2025-05-27 17:40:59:4059"}
{"level":"info","message":"2025-05-27T12:10:59.457Z  GET qbo journalentry","timestamp":"2025-05-27 17:40:59:4059"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:40:59:4059"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:41:00:410"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 99aa951e346af239797878ac2eee4964","timestamp":"2025-05-27 17:41:00:410"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 8591ee52bdb26dc856cdb718c4dea688 ","timestamp":"2025-05-27 17:41:10:4110"}
{"level":"info","message":"2025-05-27T12:11:10.535Z  GET qbo journalentry","timestamp":"2025-05-27 17:41:10:4110"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:41:10:4110"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8591ee52bdb26dc856cdb718c4dea688","timestamp":"2025-05-27 17:41:12:4112"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: fae7fd7b286223fa3d94cfbb209b24ea ","timestamp":"2025-05-27 17:41:20:4120"}
{"level":"info","message":"2025-05-27T12:11:20.293Z  GET qbo journalentry","timestamp":"2025-05-27 17:41:20:4120"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 93414544538740","timestamp":"2025-05-27 17:41:20:4120"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:41:21:4121"}
{"level":"info","message":"Response sent with status: 401 - MessageID: fae7fd7b286223fa3d94cfbb209b24ea","timestamp":"2025-05-27 17:41:21:4121"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 78cc661197630564725eb9ef30907ee3 ","timestamp":"2025-05-27 17:41:27:4127"}
{"level":"info","message":"2025-05-27T12:11:27.217Z  GET qbo journalentry","timestamp":"2025-05-27 17:41:27:4127"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:41:27:4127"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:41:28:4128"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 78cc661197630564725eb9ef30907ee3","timestamp":"2025-05-27 17:41:28:4128"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:45:55:4555"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:47:29:4729"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:47:36:4736"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:48:55:4855"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:49:35:4935"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:49:39:4939"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:51:12:5112"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:54:22:5422"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: cb9dd78af6ede9757dae0b65ce618df1 ","timestamp":"2025-05-27 17:55:30:5530"}
{"level":"info","message":"2025-05-27T12:25:30.786Z  GET qbo journalentry","timestamp":"2025-05-27 17:55:30:5530"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:55:30:5530"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:55:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:55:32:5532"}
{"level":"info","message":"Response sent with status: 500 - MessageID: cb9dd78af6ede9757dae0b65ce618df1","timestamp":"2025-05-27 17:55:32:5532"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 17:56:08:568"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 3af59369d4056276f6f84217deaedb21 ","timestamp":"2025-05-27 17:56:42:5642"}
{"level":"info","message":"2025-05-27T12:26:42.865Z  GET qbo journalentry","timestamp":"2025-05-27 17:56:42:5642"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086gg","timestamp":"2025-05-27 17:56:42:5642"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:55:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:56:44:5644"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 3af59369d4056276f6f84217deaedb21","timestamp":"2025-05-27 17:56:44:5644"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=journalentry - MessageID: 2f76f31eeba89840c23f553c098e5fda ","timestamp":"2025-05-27 17:57:24:5724"}
{"level":"info","message":"2025-05-27T12:27:24.546Z  GET qbo journalentry","timestamp":"2025-05-27 17:57:24:5724"}
{"level":"info","message":"QBO GET request for entity: journalentry | companyId: 9341454453874086","timestamp":"2025-05-27 17:57:24:5724"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 2f76f31eeba89840c23f553c098e5fda","timestamp":"2025-05-27 17:57:25:5725"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 18:02:53:253"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 18:53:14:5314"}
{"level":"info","message":"Server is listening on port 8005","timestamp":"2025-05-27 18:53:23:5323"}
