# GitHub Actions workflow for deploying ZACT microservices
name: Deploy ZACT Microservices

on:
  push:
    branches: [main, staging, develop]
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: "development"
        type: choice
        options:
          - development
          - staging
          - production

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Helm
        uses: azure/setup-helm@v3
        with:
          version: "3.12.0"

      - name: Configure kubectl
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG }}

      - name: Determine environment
        id: env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/staging" ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
          else
            echo "environment=development" >> $GITHUB_OUTPUT
          fi

      - name: Build Helm dependencies
        run: |
          cd zact-umbrella-chart
          helm dependency build

      - name: Deploy to Development
        if: steps.env.outputs.environment == 'development'
        run: |
          helm upgrade --install zact-dev ./zact-umbrella-chart \
            -f zact-umbrella-chart/environments/development.yaml \
            --namespace zact-dev \
            --create-namespace \
            --wait

      - name: Deploy to Staging
        if: steps.env.outputs.environment == 'staging'
        run: |
          helm upgrade --install zact-staging ./zact-umbrella-chart \
            -f zact-umbrella-chart/environments/staging.yaml \
            --namespace zact-staging \
            --create-namespace \
            --wait

      - name: Deploy to Production
        if: steps.env.outputs.environment == 'production'
        run: |
          helm upgrade --install zact-prod ./zact-umbrella-chart \
            -f zact-umbrella-chart/environments/production.yaml \
            --namespace zact-prod \
            --create-namespace \
            --wait

      - name: Verify deployment
        run: |
          kubectl get pods -n zact-${{ steps.env.outputs.environment }}
          kubectl get services -n zact-${{ steps.env.outputs.environment }}
