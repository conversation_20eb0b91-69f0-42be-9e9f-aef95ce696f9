{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:23:32:2332"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2025-08-12&entity=bill - MessageID: 045eddea7a152a8d4d90059898ff1c66 ","timestamp":"2025-06-02 18:23:50:2350"}
{"level":"info","message":"2025-06-02T12:53:50.425Z  GET qbo bill","timestamp":"2025-06-02 18:23:50:2350"}
{"level":"info","message":"QBO GET request | entity: bill | companyId: 4620816365356807410","timestamp":"2025-06-02 18:23:50:2350"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2025-08-12&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:86:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 18:23:53:2353"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 045eddea7a152a8d4d90059898ff1c66","timestamp":"2025-06-02 18:23:53:2353"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2025-08-12&entity=bill - MessageID: ca047ffd63cb2e30bf48b23930b7ca59 ","timestamp":"2025-06-02 18:25:06:256"}
{"level":"info","message":"2025-06-02T12:55:06.818Z  GET qbo bill","timestamp":"2025-06-02 18:25:06:256"}
{"level":"info","message":"QBO GET request | entity: bill | companyId: 4620816365356807410","timestamp":"2025-06-02 18:25:06:256"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ca047ffd63cb2e30bf48b23930b7ca59","timestamp":"2025-06-02 18:25:10:2510"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:26:21:2621"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2025-08-12&entity=bill - MessageID: e31f7753d7abe8e7cba1cd6481762e06 ","timestamp":"2025-06-02 18:26:27:2627"}
{"level":"info","message":"2025-06-02T12:56:27.896Z  GET qbo bill","timestamp":"2025-06-02 18:26:27:2627"}
{"level":"info","message":"QBO GET request | entity: bill | companyId: 4620816365356807410","timestamp":"2025-06-02 18:26:27:2627"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e31f7753d7abe8e7cba1cd6481762e06","timestamp":"2025-06-02 18:26:30:2630"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=bill - MessageID: 7574e54500ad08e9bfb1ea7a7856a471 ","timestamp":"2025-06-02 18:31:03:313"}
{"level":"info","message":"2025-06-02T13:01:03.215Z  GET qbo bill","timestamp":"2025-06-02 18:31:03:313"}
{"level":"info","message":"QBO GET request | entity: bill | companyId: 4620816365356807410","timestamp":"2025-06-02 18:31:03:313"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 7574e54500ad08e9bfb1ea7a7856a471","timestamp":"2025-06-02 18:31:05:315"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:20:3120"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:24:3124"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:30:3130"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:37:3137"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:40:3140"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:44:3144"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:47:3147"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:53:3153"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:31:57:3157"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-06-02 18:47:55:4755"}
