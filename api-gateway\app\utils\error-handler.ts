// src/middlewares/errorHandler.ts
import { Request, Response, NextFunction } from "express";
import { ErrorCodes, ErrorResponse } from "./response";
import { logger } from "../utils/logger";
import { isInvalidStatus } from "./helpers";
import ApiException from "./api-exception";
import { RequestExtended } from "../interfaces/globalInterface";

export const errorHandler = (
  err: any, // Can be Error, ApiException, or other
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (res.headersSent) return;

  logger.error(`GlobalErrorHandler: ${req.method} ${req.originalUrl}:`, {
    message: err.message,
    stack: err.stack,
    errorObject: err,
  });

  let status = err.status || err.statusCode || 500;
  let code = err.code;
  let message = err.message;
  let errorDescription = err.errorDescription || err.message;

  if (err instanceof ApiException) {
    status = err.status || 500;
    code = err.code || ErrorCodes.INTERNAL_ERROR.code;
    message = err.message || "An error occurred.";
    errorDescription = err.errorDescription || message;
  } else {
    // Handle specific error types or names
    if (err.message?.includes("invalid input syntax")) {
      status = ErrorCodes.BAD_REQUEST.status;
      code = ErrorCodes.BAD_REQUEST.code;
      message = ErrorCodes.BAD_REQUEST.message;
      errorDescription = err.message;
    } else if (
      ["invalid token", "jwt malformed"].includes(err.message) ||
      err.name === "JsonWebTokenError"
    ) {
      status = ErrorCodes.UNAUTHORIZED.status;
      code = ErrorCodes.UNAUTHORIZED.code;
      message = "Invalid token format. Please provide a valid JWT.";
      errorDescription = message;
    } else if (err.name === "TokenExpiredError") {
      status = ErrorCodes.UNAUTHORIZED.status;
      code = ErrorCodes.UNAUTHORIZED.code;
      message = "Your session has timed out. Please login again.";
      errorDescription = message;
    } else if (status === 404 && !code) {
      // For generic 404s passed via next(error)
      code = ErrorCodes.NOT_FOUND.code;
      message = message || ErrorCodes.NOT_FOUND.message;
      errorDescription = errorDescription || ErrorCodes.NOT_FOUND.message;
    } else if (status === 405 && !code) {
      // For generic 405s
      code = ErrorCodes.METHOD_NOT_ALLOWED.code;
      message = message || ErrorCodes.METHOD_NOT_ALLOWED.message;
      errorDescription =
        errorDescription || ErrorCodes.METHOD_NOT_ALLOWED.message;
    }
    // Default to internal error if code is still missing for a 500
    if (isInvalidStatus(status) || status >= 500) {
      status = ErrorCodes.INTERNAL_ERROR.status;
      code = code || ErrorCodes.INTERNAL_ERROR.code;
      message = message || ErrorCodes.INTERNAL_ERROR.message;
      errorDescription = errorDescription || "Something went wrong";
    }
  }

  if (isInvalidStatus(status)) {
    // Final safety net
    status = 500;
    code = code || ErrorCodes.INTERNAL_ERROR.code;
    message = message || ErrorCodes.INTERNAL_ERROR.message;
    errorDescription = errorDescription || "An internal server error occurred.";
  }

  return res.status(status).json(
    ErrorResponse({
      status: status,
      code: code,
      message: message,
      errorDescription: errorDescription,
    })
  );
};

export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const error: any = new Error("Path not found");
  error.status = 404;
  next(error); // Forward to global error handler
};

export const methodNotAllowedHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const error: any = new Error("Method not allowed");
  error.status = 405;
  next(error); // Forward to global error handler
};

export const unsupportedMediaTypeHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const error: any = new Error("Unsupported media type");
  error.status = 415;
  next(error); // Forward to global error handler
};

// Enhanced error handler using your ApiException
export const proxyErrorHandler = (
  err: Error | ApiException,
  req: RequestExtended,
  res: Response,
  next: NextFunction // Though next is not typically used in the final error handler if it sends a response
) => {
  logger.error("🚨 Gateway Proxy Error", {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    name: err.name,
  });

  if (res.headersSent) {
    return; // If headers already sent, do nothing
  }

  let status: number;
  let errorCode: string | number;
  let errorMessage: string;
  let errorDesc: string | undefined;

  if (err instanceof ApiException) {
    status = err.status || 500;
    errorCode =
      err.code || (status >= 500 ? ErrorCodes.INTERNAL_ERROR.code : status);
    errorMessage =
      err.message ||
      (status >= 500
        ? ErrorCodes.INTERNAL_ERROR.message
        : "An error occurred in the proxy.");
    errorDesc = err.errorDescription || errorMessage;
  } else {
    // Handle other generic errors that might reach here (e.g. from http-proxy-middleware itself before onProxyRes/onError)
    status = (err as any).status || (err as any).statusCode || 500;
    errorCode =
      (err as any).code ||
      (status >= 500 ? ErrorCodes.INTERNAL_ERROR.code : status);
    errorMessage =
      err.message ||
      (status >= 500
        ? ErrorCodes.INTERNAL_ERROR.message
        : "A gateway proxy error occurred.");
    errorDesc = errorMessage;
  }

  if (isInvalidStatus(status)) {
    status = 500;
    errorCode = ErrorCodes.INTERNAL_ERROR.code;
    errorMessage = ErrorCodes.INTERNAL_ERROR.message;
    errorDesc = ErrorCodes.INTERNAL_ERROR.message;
  }

  return res.status(status).json(
    ErrorResponse({
      status: status,
      code: errorCode,
      message: errorMessage,
      errorDescription: errorDesc,
    })
  );
};
