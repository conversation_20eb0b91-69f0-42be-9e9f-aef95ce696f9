# Default values for account-service
replicaCount: 1

image:
  repository: shivraj77/account-service
  tag: ""  # Uses global.imageTag from umbrella chart
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 8003

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# Environment variables specific to account service
env:
  PORT: "8003"
  # QBO settings will be inherited from global values
  # Secrets will be injected via CI/CD

# Health checks
livenessProbe:
  httpGet:
    path: /health
    port: 8003
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /ready
    port: 8003
  initialDelaySeconds: 5
  periodSeconds: 5

# Service account
serviceAccount:
  create: false  # Use umbrella chart service account
  name: ""

# Pod annotations
podAnnotations: {}

# Pod security context
podSecurityContext: {}

# Security context
securityContext: {}

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
