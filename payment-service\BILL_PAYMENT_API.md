# Bill Payment API Documentation

## Overview

The Payment Service creates bill payments through the QuickBooks Online (QBO) API. When you use the "payment" entity, it automatically calls the QBO BillPayment API to pay vendor bills.

## Endpoints

### Create Bill Payment

**POST** `/api/payment/service?service=qbo&entity=payment`

#### Headers

- `accesstoken`: QuickBooks OAuth access token
- `companyid`: QuickBooks company ID
- `Content-Type`: application/json

#### Request Body

```json
{
  "data": {
    "VendorRef": {
      "value": "56"
    },
    "PayType": "Check",
    "CheckPayment": {
      "BankAccountRef": {
        "value": "35"
      },
      "PrintStatus": "NeedToPrint"
    },
    "TotalAmt": 200.0,
    "Line": [
      {
        "Amount": 200.0,
        "LinkedTxn": [
          {
            "TxnId": "145",
            "TxnType": "Bill"
          }
        ]
      }
    ]
  }
}
```

#### Response

```json
{
  "message": "Successfully created BillPayment with Id: 123",
  "data": {
    "BillPayment": {
      "Id": "123",
      "VendorRef": {
        "value": "56",
        "name": "Vendor Name"
      },
      "PayType": "Check",
      "TotalAmt": 200.0
      "Line": [
        {
          "Amount": 200.00,
          "LinkedTxn": [
            {
              "TxnId": "145",
              "TxnType": "Bill"
            }
          ]
        }
      ]
    }
  }
}
```

## Bill Payment Types

### Check Payment

```json
{
  "PayType": "Check",
  "CheckPayment": {
    "BankAccountRef": {
      "value": "35"
    },
    "PrintStatus": "NeedToPrint"
  }
}
```

### Credit Card Payment

```json
{
  "PayType": "CreditCard",
  "CreditCardPayment": {
    "CreditCardAccountRef": {
      "value": "41"
    }
  }
}
```

## Required Fields

- `VendorRef`: Reference to the vendor being paid
- `PayType`: Payment method (Check, CreditCard, Cash)
- `TotalAmt`: Total payment amount
- `Line`: Array of line items linking to bills being paid

## Usage Examples

### Pay a Single Bill

```bash
curl -X POST "http://localhost:8000/api/payment/service?service=qbo&entity=payment" \
  -H "accesstoken: YOUR_ACCESS_TOKEN" \
  -H "companyid: YOUR_COMPANY_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "VendorRef": {"value": "56"},
      "PayType": "Check",
      "CheckPayment": {
        "BankAccountRef": {"value": "35"},
        "PrintStatus": "NeedToPrint"
      },
      "TotalAmt": 200.00,
      "Line": [{
        "Amount": 200.00,
        "LinkedTxn": [{
          "TxnId": "145",
          "TxnType": "Bill"
        }]
      }]
    }
  }'
```

### Pay Multiple Bills

```bash
curl -X POST "http://localhost:8000/api/payment/service?service=qbo&entity=payment" \
  -H "accesstoken: YOUR_ACCESS_TOKEN" \
  -H "companyid: YOUR_COMPANY_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "VendorRef": {"value": "56"},
      "PayType": "Check",
      "CheckPayment": {
        "BankAccountRef": {"value": "35"},
        "PrintStatus": "NeedToPrint"
      },
      "TotalAmt": 500.00,
      "Line": [
        {
          "Amount": 200.00,
          "LinkedTxn": [{"TxnId": "145", "TxnType": "Bill"}]
        },
        {
          "Amount": 300.00,
          "LinkedTxn": [{"TxnId": "146", "TxnType": "Bill"}]
        }
      ]
    }
  }'
```

## Error Handling

The API will return appropriate error messages for:

- Invalid entity types
- Missing required fields
- Authentication failures
- QuickBooks API errors

## Notes

- Bill payments are processed through the existing payment service infrastructure
- All QuickBooks Online bill payment rules and validations apply
- The service maintains the same authentication and error handling patterns as other entities
