import axios, { AxiosInstance, AxiosRequestConfig } from "axios";
import ApiException from "../utils/api-exception";
import { ErrorCodes } from "../utils/response";
import { logger } from "../utils/logger";

// Base interfaces
export interface ApiService {
  post: (entity: string, data: any) => Promise<any>;
}

/**
 * Creates an Axios client with error handling
 */
export const createApiClient = (baseUrl: string): AxiosInstance => {
  const client = axios.create({
    baseURL: baseUrl,
  });

  return client;
};

/**
 * Creates a generic API service with standardized POST method
 */
export const createApiService = (
  client: AxiosInstance,
  getAuthHeaders: () => Record<string, string>
): ApiService => {
  return {
    async post(entity: string, data: any) {
      logger.info(`POST request to ${entity}`);
      try {
        const response = await client.post(`/${entity}`, data, {
          headers: getAuthHeaders(),
        });
        return response.data;
      } catch (error) {
        // Error is already handled by interceptor
        throw error;
      }
    },
  };
};
