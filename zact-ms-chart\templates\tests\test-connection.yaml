apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "zact-ms-chart.fullname" . }}-test"
  labels:
    {{- include "zact-ms-chart.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  restartPolicy: Never
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ .Values.services.apiGateway.name }}:{{ .Values.services.apiGateway.port }}']
  nodeSelector:
    {{- toYaml .Values.nodeSelector | nindent 4 }}
  tolerations:
    {{- toYaml .Values.tolerations | nindent 4 }}
  affinity:
    {{- toYaml .Values.affinity | nindent 4 }}