// src/middlewares/logging.middleware.ts
import { Request, Response, NextFunction } from "express";
import { logger } from "../utils/logger";

// Optional: Unique request ID generator (could use UUID or nanoid)
import { randomUUID } from "crypto";

export const loggingMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const start = process.hrtime(); // High-resolution time
  const requestId = randomUUID();
  req.headers["x-request-id"] = requestId;

  // Attach logId to req for later use in error handlers
  (req as any).logId = requestId;

  logger.http(`[${requestId}] --> ${req.method} ${req.originalUrl}`);

  res.on("finish", () => {
    const [seconds, nanoseconds] = process.hrtime(start);
    const durationMs = (seconds * 1e3 + nanoseconds / 1e6).toFixed(2);

    logger.http(
      `[${requestId}] <-- ${req.method} ${req.originalUrl} ${res.statusCode} [${durationMs} ms]`
    );
  });

  next();
};
