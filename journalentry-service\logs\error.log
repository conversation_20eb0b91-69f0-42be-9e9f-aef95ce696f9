{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"POST /api/service?service=qbo&entity=bill: Bad request","stack":"Error: Bad request\n    at validateAuthParams (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:40:11)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:57:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:51:52\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 11:42:34:4234"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 12:15:09:159"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 14:42:46:4246"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=journalentry: Required parameter Line.DetailType is missing in the request","stack":"Error: Required parameter Line.DetailType is missing in the request\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 15:23:26:2326"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'journalentry' or 'JOURNALENTRY'","level":"error","message":"GET /api/service?service=qbo&entity=journal: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:02:21:221"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:27:53:2753"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:27:58:2758"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:28:04:284"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:28:15:2815"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:30:00:300"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:30:13:3013"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:30:36:3036"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:31:33:3133"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:32:05:325"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:32:33:3233"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:33:30:3330"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:34:06:346"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:34:49:3449"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:35:03:353"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:35:13:3513"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:35:21:3521"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:36:23:3623"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:36:43:3643"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:36:59:3659"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:38:01:381"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:38:25:3825"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:39:24:3924"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:39:57:3957"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:40:44:4044"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:41:00:410"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:41:21:4121"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:41:28:4128"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:55:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:55:32:5532"}
{"code":105,"errorDescription":"Request failed with status code 500","level":"error","message":"GET /api/service?service=qbo&entity=journalentry: System Failure Error: {0}","stack":"Error: System Failure Error: {0}\n    at C:\\Users\\<USER>\\zact\\journalentry-service\\app\\services\\qboApiServices\\index.ts:55:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\journalentry-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:56:44:5644"}
