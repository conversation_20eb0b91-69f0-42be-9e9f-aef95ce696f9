# ZACT Microservices Deployment Guide

## 🎉 Umbrella Chart Conversion Complete!

Your ZACT microservices have been successfully converted from a single Helm chart to a modern **umbrella chart architecture** with proper **environment bifurcation** and **secrets management**.

## 📁 New Structure

```
zact-umbrella-chart/
├── Chart.yaml                     # Umbrella chart definition
├── values.yaml                    # Default values (safe for Git)
├── environments/                  # Environment-specific configurations
│   ├── development.yaml           # Development settings
│   ├── staging.yaml              # Staging settings
│   └── production.yaml           # Production settings
├── charts/                       # Individual service charts
│   ├── account-service/
│   ├── bill-service/
│   ├── class-service/
│   ├── journalentry-service/
│   ├── payment-service/
│   ├── vendor-service/
│   └── api-gateway/
├── templates/                    # Shared templates
│   ├── _helpers.tpl
│   └── secrets.yaml
├── scripts/                      # Deployment utilities
│   ├── deploy.sh
│   ├── validate.sh
│   └── validate.ps1
├── ci-cd/                       # CI/CD configurations
│   ├── github-actions-deploy.yml
│   └── argocd-application.yaml
└── README.md                    # Comprehensive documentation
```

## 🚀 Quick Start Deployment

### 1. **Development Environment**

```bash
# Navigate to the umbrella chart
cd zact-umbrella-chart

# Build dependencies
helm dependency build

# Deploy to development
helm install zact-dev . \
  -f environments/development.yaml \
  --namespace zact-dev \
  --create-namespace
```

### 2. **Production Environment**

```bash
# Deploy to production
helm install zact-prod . \
  -f environments/production.yaml \
  --namespace zact-prod \
  --create-namespace
```

### 3. **Selective Service Deployment**

```bash
# Deploy only specific services
helm install zact-partial . \
  -f environments/development.yaml \
  --set account-service.enabled=true \
  --set bill-service.enabled=false \
  --set api-gateway.enabled=true \
  --namespace zact-dev
```

## 📊 Service Configuration

| Service                   | Port | Image                                   | Type         |
| ------------------------- | ---- | --------------------------------------- | ------------ |
| **API Gateway**           | 8000 | `shivraj77/api-gateway:latest`          | LoadBalancer |
| **Account Service**       | 8003 | `shivraj77/account-service:latest`      | ClusterIP    |
| **Bill Service**          | 8006 | `shivraj77/bill-service:latest`         | ClusterIP    |
| **Class Service**         | 8002 | `shivraj77/class-service:latest`        | ClusterIP    |
| **Journal Entry Service** | 8005 | `shivraj77/journalentry-service:latest` | ClusterIP    |
| **Payment Service**       | 8004 | `shivraj77/payment-service:latest`      | ClusterIP    |
| **Vendor Service**        | 8001 | `shivraj77/vendor-service:latest`       | ClusterIP    |

### **Common Commands**

```bash
# Check deployment status
kubectl get pods -n zact-dev

# View service logs
kubectl logs -f deployment/api-gateway -n zact-dev

# Check service connectivity
kubectl exec -it deployment/api-gateway -n zact-dev -- curl http://account-service:8003/health

# Update deployment
helm upgrade zact-dev . -f environments/development.yaml
```

### **Validation Commands**

```bash
# Test template generation
helm template test-release . -f environments/development.yaml

# Dry run deployment
helm install test-release . -f environments/development.yaml --dry-run

# Lint charts
helm lint .
```

## 🎉 Success!

Your ZACT microservices are now ready for production deployment with:

- ✅ Modern umbrella chart architecture
- ✅ Environment bifurcation
- ✅ Secure secrets management
- ✅ Production-ready configurations
- ✅ CI/CD pipeline support

**Happy deploying! 🚀**
