# API Gateway Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.services.apiGateway.name }}
  labels:
    app: {{ .Values.services.apiGateway.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.services.apiGateway.name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Values.services.apiGateway.name }}
        release: {{ .Release.Name }}
    spec:
      containers:
        - name: api-gateway
          image: "{{ .Values.services.apiGateway.image }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.apiGateway.port }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.services.apiGateway.port }}"
            - name: NODE_ENV
              value: "{{ .Values.global.nodeEnv }}"
            - name: ACCOUNT_SERVICE_URL
              value: "{{ .Values.services.apiGateway.env.ACCOUNT_SERVICE_URL }}"
            - name: BILL_SERVICE_URL
              value: "{{ .Values.services.apiGateway.env.BILL_SERVICE_URL }}"
            - name: CLASS_SERVICE_URL
              value: "{{ .Values.services.apiGateway.env.CLASS_SERVICE_URL }}"
            - name: JOURNALENTRY_SERVICE_URL
              value: "{{ .Values.services.apiGateway.env.JOURNALENTRY_SERVICE_URL }}"
            - name: PAYMENT_SERVICE_URL
              value: "{{ .Values.services.apiGateway.env.PAYMENT_SERVICE_URL }}"
            - name: VENDOR_SERVICE_URL
              value: "{{ .Values.services.apiGateway.env.VENDOR_SERVICE_URL }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}

---

# Account Service Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.services.accountService.name }}
  labels:
    app: {{ .Values.services.accountService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.services.accountService.name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Values.services.accountService.name }}
        release: {{ .Release.Name }}
    spec:
      containers:
        - name: account-service
          image: "{{ .Values.services.accountService.image }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.accountService.port }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.services.accountService.port }}"
            - name: QBO_API_URL
              value: "{{ .Values.global.qboApiUrl }}"
            - name: QBO_MINOR_VERSION
              value: "{{ .Values.global.qboMinorVersion }}"
            - name: NODE_ENV
              value: "{{ .Values.global.nodeEnv }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}

---

# Bill Service Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.services.billService.name }}
  labels:
    app: {{ .Values.services.billService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.services.billService.name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Values.services.billService.name }}
        release: {{ .Release.Name }}
    spec:
      containers:
        - name: bill-service
          image: "{{ .Values.services.billService.image }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.billService.port }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.services.billService.port }}"
            - name: QBO_API_URL
              value: "{{ .Values.global.qboApiUrl }}"
            - name: QBO_MINOR_VERSION
              value: "{{ .Values.global.qboMinorVersion }}"
            - name: NODE_ENV
              value: "{{ .Values.global.nodeEnv }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}

---

# Class Service Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.services.classService.name }}
  labels:
    app: {{ .Values.services.classService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.services.classService.name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Values.services.classService.name }}
        release: {{ .Release.Name }}
    spec:
      containers:
        - name: class-service
          image: "{{ .Values.services.classService.image }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.classService.port }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.services.classService.port }}"
            - name: QBO_API_URL
              value: "{{ .Values.global.qboApiUrl }}"
            - name: QBO_MINOR_VERSION
              value: "{{ .Values.global.qboMinorVersion }}"
            - name: NODE_ENV
              value: "{{ .Values.global.nodeEnv }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}

---

# Journal Entry Service Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.services.journalentryService.name }}
  labels:
    app: {{ .Values.services.journalentryService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.services.journalentryService.name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Values.services.journalentryService.name }}
        release: {{ .Release.Name }}
    spec:
      containers:
        - name: journalentry-service
          image: "{{ .Values.services.journalentryService.image }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.journalentryService.port }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.services.journalentryService.port }}"
            - name: QBO_API_URL
              value: "{{ .Values.global.qboApiUrl }}"
            - name: QBO_MINOR_VERSION
              value: "{{ .Values.global.qboMinorVersion }}"
            - name: NODE_ENV
              value: "{{ .Values.global.nodeEnv }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}

---

# Payment Service Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.services.paymentService.name }}
  labels:
    app: {{ .Values.services.paymentService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.services.paymentService.name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Values.services.paymentService.name }}
        release: {{ .Release.Name }}
    spec:
      containers:
        - name: payment-service
          image: "{{ .Values.services.paymentService.image }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.paymentService.port }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.services.paymentService.port }}"
            - name: QBO_API_URL
              value: "{{ .Values.global.qboApiUrl }}"
            - name: QBO_MINOR_VERSION
              value: "{{ .Values.global.qboMinorVersion }}"
            - name: NODE_ENV
              value: "{{ .Values.global.nodeEnv }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}

---

# Vendor Service Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.services.vendorService.name }}
  labels:
    app: {{ .Values.services.vendorService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.services.vendorService.name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Values.services.vendorService.name }}
        release: {{ .Release.Name }}
    spec:
      containers:
        - name: vendor-service
          image: "{{ .Values.services.vendorService.image }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.services.vendorService.port }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.services.vendorService.port }}"
            - name: QBO_API_URL
              value: "{{ .Values.global.qboApiUrl }}"
            - name: QBO_MINOR_VERSION
              value: "{{ .Values.global.qboMinorVersion }}"
            - name: NODE_ENV
              value: "{{ .Values.global.nodeEnv }}"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
            