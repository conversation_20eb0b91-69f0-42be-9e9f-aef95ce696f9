# Development environment configuration
# Non-sensitive environment-specific values

global:
  nodeEnv: "development"
  qboApiUrl: "https://sandbox-quickbooks.api.intuit.com/v3/company"
  imageTag: "latest"
  imagePullPolicy: "Always"

# Development resource limits (lower for cost savings)
defaultResources:
  limits:
    cpu: 250m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Service configurations for development
account-service:
  replicaCount: 1

bill-service:
  replicaCount: 1

class-service:
  replicaCount: 1

journalentry-service:
  replicaCount: 1

payment-service:
  replicaCount: 1

vendor-service:
  replicaCount: 1

api-gateway:
  replicaCount: 1
  service:
    type: LoadBalancer # For easy access in development
  env:
    ACCOUNT_SERVICE_URL: "http://account-service:8003"
    BILL_SERVICE_URL: "http://bill-service:8006"
    CLASS_SERVICE_URL: "http://class-service:8002"
    JOURNALENTRY_SERVICE_URL: "http://journalentry-service:8005"
    PAYMENT_SERVICE_URL: "http://payment-service:8004"
    VENDOR_SERVICE_URL: "http://vendor-service:8001"

# Autoscaling disabled in development
autoscaling:
  enabled: false

# Development-specific annotations
serviceAccount:
  annotations:
    environment: "development"
# No secrets required for development
