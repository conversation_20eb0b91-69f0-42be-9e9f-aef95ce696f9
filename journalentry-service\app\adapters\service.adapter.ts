import { qboService } from "../services/qboApiServices";
import ApiException from "../utils/api-exception";
import { ErrorCodes } from "../utils/response";

/**
 * Interface defining the structure of service requests for POST operations
 */
interface ServiceRequest {
  service: string; // The service to use (e.g., 'qbo')
  entity: string; // The entity type to operate on (e.g., 'journalentry')
  requestType: "post"; // HTTP method to use
  accessToken: string; // OAuth token for authentication
  companyId: string; // Target company identifier
  bodyData?: any; // POST request body data
  queryParams?: any; // Additional query parameters
}

/**
 * Routes and processes service requests to appropriate service handlers
 * @param request ServiceRequest object containing all request parameters
 * @throws ApiException for unsupported services or request types
 */
export const processServiceRequest = async (request: ServiceRequest) => {
  const {
    service,
    entity,
    requestType,
    bodyData,
    queryParams,
    accessToken,
    companyId,
  } = request;

  let serviceHandler;

  // Route to appropriate service implementation
  switch (service.toLowerCase()) {
    case "qbo":
      serviceHandler = qboService;
      break;
    default:
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: `Unsupported service: ${service}`,
      });
  }

  // Handle request based on HTTP method
  if (requestType === "post") {
    return await serviceHandler.post(entity, {
      accessToken,
      companyId,
      bodyData,
      queryParams,
    });
  } else {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: `Unsupported request type: ${requestType}`,
    });
  }
};
