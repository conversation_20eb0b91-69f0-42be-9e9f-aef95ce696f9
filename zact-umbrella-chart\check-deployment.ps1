# ZACT Deployment Checker - For Beginners
# This script checks if your deployment is working correctly

Write-Host "🔍 ZACT Deployment Health Check" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Check if kubectl is available
Write-Host "1. Checking if kubectl is installed..." -ForegroundColor Yellow
try {
    $kubectlVersion = kubectl version --client --short 2>$null
    Write-Host "   ✅ kubectl is installed: $kubectlVersion" -ForegroundColor Green
} catch {
    Write-Host "   ❌ kubectl not found. Please install Docker Desktop with Kubernetes enabled." -ForegroundColor Red
    exit 1
}

# Check if He<PERSON> is available
Write-Host "2. Checking if <PERSON><PERSON> is installed..." -ForegroundColor Yellow
try {
    $helmVersion = helm version --short 2>$null
    Write-Host "   ✅ Helm is installed: $helmVersion" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Helm not found. Please install He<PERSON> first." -ForegroundColor Red
    exit 1
}

# Check if Kubernetes cluster is running
Write-Host "3. Checking Kubernetes cluster..." -ForegroundColor Yellow
try {
    $nodes = kubectl get nodes --no-headers 2>$null
    if ($nodes) {
        Write-Host "   ✅ Kubernetes cluster is running" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Kubernetes cluster not accessible. Start Docker Desktop." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "   ❌ Cannot connect to Kubernetes cluster" -ForegroundColor Red
    exit 1
}

# Check if zact-dev namespace exists
Write-Host "4. Checking ZACT deployment..." -ForegroundColor Yellow
try {
    $namespace = kubectl get namespace zact-dev --no-headers 2>$null
    if ($namespace) {
        Write-Host "   ✅ zact-dev namespace exists" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  zact-dev namespace not found. Run deployment first." -ForegroundColor Yellow
        Write-Host "      Command: helm install zact-dev . -f environments/development.yaml --namespace zact-dev --create-namespace" -ForegroundColor Gray
        exit 0
    }
} catch {
    Write-Host "   ❌ Error checking namespace" -ForegroundColor Red
    exit 1
}

# Check Helm deployment
Write-Host "5. Checking Helm deployment status..." -ForegroundColor Yellow
try {
    $helmStatus = helm status zact-dev -n zact-dev --short 2>$null
    if ($helmStatus -match "deployed") {
        Write-Host "   ✅ Helm deployment is active" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Helm deployment not found or failed" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Error checking Helm deployment" -ForegroundColor Red
}

# Check pod status
Write-Host "6. Checking pod status..." -ForegroundColor Yellow
try {
    $pods = kubectl get pods -n zact-dev --no-headers 2>$null
    if ($pods) {
        $runningPods = ($pods | Where-Object { $_ -match "Running" }).Count
        $totalPods = ($pods | Measure-Object).Count
        
        Write-Host "   📊 Pod Status: $runningPods/$totalPods running" -ForegroundColor Cyan
        
        if ($runningPods -eq $totalPods -and $totalPods -eq 7) {
            Write-Host "   ✅ All 7 services are running!" -ForegroundColor Green
        } elseif ($runningPods -eq $totalPods) {
            Write-Host "   ✅ All pods are running (expected 7, found $totalPods)" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  Some pods are not ready yet. Wait a few minutes and try again." -ForegroundColor Yellow
            Write-Host ""
            Write-Host "   Pod Details:" -ForegroundColor Gray
            kubectl get pods -n zact-dev
        }
    } else {
        Write-Host "   ❌ No pods found in zact-dev namespace" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Error checking pods" -ForegroundColor Red
}

# Check services
Write-Host "7. Checking services..." -ForegroundColor Yellow
try {
    $services = kubectl get services -n zact-dev --no-headers 2>$null
    if ($services) {
        $serviceCount = ($services | Measure-Object).Count
        Write-Host "   ✅ Found $serviceCount services" -ForegroundColor Green
        
        # Check API Gateway specifically
        $apiGateway = kubectl get service zact-dev-api-gateway -n zact-dev --no-headers 2>$null
        if ($apiGateway) {
            Write-Host "   ✅ API Gateway service is available" -ForegroundColor Green
        }
    } else {
        Write-Host "   ❌ No services found" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Error checking services" -ForegroundColor Red
}

# Test API Gateway connectivity
Write-Host "8. Testing API Gateway..." -ForegroundColor Yellow
try {
    # Try to test the API Gateway
    $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✅ API Gateway is responding at http://localhost:8000" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  API Gateway not responding. Try port forwarding:" -ForegroundColor Yellow
        Write-Host "      kubectl port-forward service/zact-dev-api-gateway 8000:8000 -n zact-dev" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ⚠️  Cannot reach API Gateway at localhost:8000" -ForegroundColor Yellow
    Write-Host "      Try: kubectl port-forward service/zact-dev-api-gateway 8000:8000 -n zact-dev" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🎉 Health Check Complete!" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Quick Commands:" -ForegroundColor White
Write-Host "   View pods:     kubectl get pods -n zact-dev" -ForegroundColor Gray
Write-Host "   View services: kubectl get services -n zact-dev" -ForegroundColor Gray
Write-Host "   View logs:     kubectl logs <pod-name> -n zact-dev" -ForegroundColor Gray
Write-Host "   Port forward:  kubectl port-forward service/zact-dev-api-gateway 8000:8000 -n zact-dev" -ForegroundColor Gray
Write-Host ""
