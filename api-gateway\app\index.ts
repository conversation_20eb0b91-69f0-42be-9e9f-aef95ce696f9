// index.ts - Main gateway entry point
import express from "express";
import cors from "cors";
import helmet from "helmet";
import { setupProxy } from "./proxy";
import { errorHandler, proxyError<PERSON>and<PERSON> } from "./utils/error-handler";
import { logger } from "./utils/logger";
import { RequestExtended } from "./interfaces/globalInterface";

const app = express();
const PORT = process.env.PORT || 8000;

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: false, // Disable CSP for API gateway
  })
);

// CORS configuration
// app.use(
//   cors({
//     origin: process.env.ALLOWED_ORIGINS?.split(",") || [
//       "http://localhost:3000",
//       "http://localhost:3001",
//     ],
//     credentials: true,
//     methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
//     allowedHeaders: [
//       "Content-Type",
//       "Authorization",
//       "accesstoken",
//       "companyid",
//       "x-*",
//       "custom-*",
//     ],
//   })
// );

app.use(
  cors({
    origin: "*",
  })
);

// Request logging middleware
app.use((req: RequestExtended, res, next) => {
  // Log incoming request using your existing logger
  logger.info("📥 Incoming request", {
    method: req.method,
    path: req.path,
    query: req.query,
    hasBody: !!Object.keys(req.body || {}).length,
    headers: {
      accesstoken: req.headers["accesstoken"] ? "[PROVIDED]" : undefined,
      companyid: req.headers["companyid"],
      contentType: req.headers["content-type"],
    },
    requestId: req.headers["x-request-id"] || "unknown",
    userAgent: req.headers["user-agent"],
  });

  next();
});

// Setup proxy routes BEFORE other routes
setupProxy(app);

// Gateway info endpoint
app.get("/", (req, res) => {
  res.json({
    name: "API Gateway",
    version: process.env.npm_package_version || "1.0.0",
    status: "healthy",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
    services: ["vendor", "class", "account", "bill", "payment"],
  });
});

// Gateway health check
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || "1.0.0",
  });
});

// 404 handler for unknown routes
app.use("*", (req: RequestExtended, res) => {
  logger.info("🔍 Route not found", {
    method: req.method,
    path: req.path,
  });
  res.status(404).json({
    responseStatus: 404,
    message: "Route Not found",
    error: {
      status: 404,
      code: 104,
      errorDescription: `The requested route ${req.method} ${req.path} was not found`,
    },
  });
});

// Error handling middleware (must be last)
app.use(proxyErrorHandler);
app.use(errorHandler);

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("🛑 SIGTERM received, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGINT", () => {
  console.log("🛑 SIGINT received, shutting down gracefully...");
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 API Gateway running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log("🎯 Available services:");
  console.log("   - /api/vendor");
  console.log("   - /api/class");
  console.log("   - /api/account");
  console.log("   - /api/bill");
  console.log("   - /api/journalentry");
  console.log("   - /api/payment");
});

export default app;
