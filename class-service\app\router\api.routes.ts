import express from "express";
import { processServiceRequest } from "../adapters";
import { RequestExtended } from "../interfaces/globalInterface";
import ApiException from "../utils/api-exception";
import asyncHandler from "../utils/async-handler";
import { errorHandler, notFoundHandler } from "../utils/error-handler";
import { ErrorCodes } from "../utils/response";

const router = express.Router();

// === Validation Helpers ===
const validateServiceAndEntity = (service?: string, entity?: string) => {
  if (!service || !["qbo"].includes(service.toLowerCase())) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: `Invalid service: ${service}. Supported: qbo`,
    });
  }

  if (!entity) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "Entity is required",
    });
  }

  // Updated to allow 'class'
  if (!["class", "CLASS"].includes(entity)) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "Invalid entity. Allowed: 'class' or 'CLASS'",
    });
  }
};

const validateAuthParams = (
  accessToken?: string | string[],
  companyId?: string | string[]
) => {
  if (!accessToken || !companyId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription:
        "Missing required authentication: accessToken and companyId",
    });
  }
};

const validateDateParams = (dateField?: string, startDate?: string) => {
  if (dateField && !startDate) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "startDate is required when dateField is provided",
    });
  }

  if (startDate && !dateField) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "dateField is required when startDate is provided",
    });
  }

  if (startDate && isNaN(Date.parse(startDate))) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription:
        "Invalid startDate format. Use ISO date format (YYYY-MM-DD)",
    });
  }
};

// === GET ===
router.get(
  "/service",
  asyncHandler(async (req: RequestExtended, res) => {
    const {
      service,
      entity,
      totalCount,
      startPosition,
      maxResults,
      ...queryParamsRaw
    } = req.query; // Destructure parameters
    const accessToken = req.headers["accesstoken"];
    const companyId = req.headers["companyid"];

    validateServiceAndEntity(service as string, entity as string);
    validateAuthParams(accessToken as string, companyId as string);

    req.log?.(`GET ${service} ${entity}`);

    const queryParams: Record<string, any> = { ...queryParamsRaw };

    // Add totalCount to queryParams if present in request
    if (totalCount !== undefined) {
      queryParams.totalCount = totalCount === "true";
    }

    // Add pagination parameters
    if (startPosition !== undefined) {
      queryParams.startPosition = startPosition;
    }

    // Add maxResults with default value of 1000 if not provided
    queryParams.maxResults = maxResults || "1000";

    const { dateField, startDate, endDate } = queryParams;

    if (dateField || startDate) {
      validateDateParams(dateField as string, startDate as string);
      if (endDate && isNaN(Date.parse(endDate as string))) {
        throw new ApiException({
          ...ErrorCodes.BAD_REQUEST,
          errorDescription:
            "Invalid endDate format. Use ISO date format (YYYY-MM-DD)",
        });
      }
    }

    if (dateField && startDate) {
      const dateFilter = {
        fieldName: dateField as string,
        startDate: startDate as string,
        ...(endDate && { endDate: endDate as string }),
      };
      queryParams.dateFilter = dateFilter;
    }

    delete queryParams.dateField;
    delete queryParams.startDate;
    delete queryParams.endDate;

    const result = await processServiceRequest({
      service: service as string,
      entity: entity as string,
      requestType: "get",
      accessToken: accessToken as string,
      companyId: companyId as string,
      queryParams,
    });

    return res.json(result);
  })
);

// === Error Handling Middleware ===
router.use(notFoundHandler);
router.use(errorHandler);

export default router;
