import axios, { AxiosInstance } from "axios";
import { logger } from "../utils/logger";
import qs from "qs";

// Base interfaces
export interface ApiService {
  get: (entity: string, data: any) => Promise<any>;
}

/**
 * Creates an Axios client with error handling
 */
export const createApiClient = (baseUrl: string): AxiosInstance => {
  const client = axios.create({
    baseURL: baseUrl,
  });

  return client;
};

/**
 * Creates a generic API service with standardized GET methods
 */
export const createApiService = (
  client: AxiosInstance,
  getAuthHeaders: () => Record<string, string>
): ApiService => {
  return {
    async get(entity: string, params?: any) {
      logger.info(`GET request to ${entity}`);
      try {
        const response = await client.get(`/${entity}`, {
          params,
          headers: getAuthHeaders(),
        });
        return response.data;
      } catch (error) {
        // Error is already handled by interceptor
        throw error;
      }
    },
  };
};
