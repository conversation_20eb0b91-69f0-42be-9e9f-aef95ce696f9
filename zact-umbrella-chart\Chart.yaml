apiVersion: v2
name: zact-umbrella
description: Umbrella chart for ZACT microservices architecture
type: application
version: 1.0.0
appVersion: "1.0"

dependencies:
  - name: account-service
    version: "1.0.0"
    repository: "file://charts/account-service"
    condition: account-service.enabled
  
  - name: bill-service
    version: "1.0.0"
    repository: "file://charts/bill-service"
    condition: bill-service.enabled
  
  - name: class-service
    version: "1.0.0"
    repository: "file://charts/class-service"
    condition: class-service.enabled
  
  - name: journalentry-service
    version: "1.0.0"
    repository: "file://charts/journalentry-service"
    condition: journalentry-service.enabled
  
  - name: payment-service
    version: "1.0.0"
    repository: "file://charts/payment-service"
    condition: payment-service.enabled
  
  - name: vendor-service
    version: "1.0.0"
    repository: "file://charts/vendor-service"
    condition: vendor-service.enabled
  
  - name: api-gateway
    version: "1.0.0"
    repository: "file://charts/api-gateway"
    condition: api-gateway.enabled

maintainers:
  - name: ZACT Team
    email: <EMAIL>

keywords:
  - microservices
  - quickbooks
  - accounting
  - api-gateway
