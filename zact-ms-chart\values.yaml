# Default values for zact-ms-chart.
# This is a YAML-formatted file.
# Declare variables to be substituted into your templates.

replicaCount: 1

image:
  tag: "latest"
  pullPolicy: "Always"

# Global environment variables
global:
  qboApiUrl: "https://sandbox-quickbooks.api.intuit.com/v3/company"
  qboMinorVersion: "75"
  nodeEnv: "development"

services:
  # API Gateway
  apiGateway:
    name: "api-gateway"
    image: "shivraj77/api-gateway"
    port: 8000
    type: LoadBalancer
    env:
      ACCOUNT_SERVICE_URL: "http://account-service:8003"
      BILL_SERVICE_URL: "http://bill-service:8006"
      CLASS_SERVICE_URL: "http://class-service:8002"
      JOURNALENTRY_SERVICE_URL: "http://journalentry-service:8005"
      PAYMENT_SERVICE_URL: "http://payment-service:8004"
      VENDOR_SERVICE_URL: "http://vendor-service:8001"

  # Account Service
  accountService:
    name: "account-service"
    image: "shivraj77/account-service"
    port: 8003
    type: ClusterIP

  # Bill Service
  billService:
    name: "bill-service"
    image: "shivraj77/bill-service"
    port: 8006
    type: ClusterIP

  # Class Service
  classService:
    name: "class-service"
    image: "shivraj77/class-service"
    port: 8002
    type: ClusterIP

  # Journal Entry Service
  journalentryService:
    name: "journalentry-service"
    image: "shivraj77/journalentry-service"
    port: 8005
    type: ClusterIP

  # Payment Service
  paymentService:
    name: "payment-service"
    image: "shivraj77/payment-service"
    port: 8004
    type: ClusterIP

  # Vendor Service
  vendorService:
    name: "vendor-service"
    image: "shivraj77/vendor-service"
    port: 8001
    type: ClusterIP

# Resource limits and requests
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Service Account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod Security Context
podSecurityContext: {}

# Security Context
securityContext: {}

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
