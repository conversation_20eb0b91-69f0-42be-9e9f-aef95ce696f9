{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/account","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:22:57:2257"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/account","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:25:29:2529"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/account","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:25:49:2549"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:31:11:3111"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:31:19:3119"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:31:31:3131"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/api/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:32:35:3235"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:33:13:3313"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:34:21:3421"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:34:36:3436"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:35:32:3532"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/health","query":{},"requestId":"unknown","timestamp":"2025-06-03 13:35:54:3554","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 13:36:22:3622","userAgent":"PostmanRuntime/7.44.0"}
{"level":"info","message":"🔍 Route not found","method":"GET","path":"/","timestamp":"2025-06-03 13:36:22:3622"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 13:36:32:3632","userAgent":"PostmanRuntime/7.44.0"}
{"level":"info","message":"🔍 Route not found","method":"GET","path":"/","timestamp":"2025-06-03 13:36:32:3632"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 13:37:03:373","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to account","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","target":"http://localhost:8003","timestamp":"2025-06-03 13:37:03:373"}
{"contentLength":46748,"level":"info","message":"✅ Response from account","path":"api/service","status":200,"timestamp":"2025-06-03 13:37:07:377"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:211:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:203:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 13:37:07:377"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 13:37:07:377"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/account","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:37:38:3738"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:37:57:3757"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/health","query":{},"requestId":"unknown","timestamp":"2025-06-03 13:38:00:380","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 13:43:51:4351","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to account","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","target":"http://localhost:8003","timestamp":"2025-06-03 13:43:51:4351"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 13:44:45:4445","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to account","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","target":"http://localhost:8003","timestamp":"2025-06-03 13:44:45:4445"}
{"contentLength":46748,"level":"info","message":"✅ Response from account","path":"api/service","status":200,"timestamp":"2025-06-03 13:44:48:4448"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:211:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:203:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 13:44:48:4448"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 13:44:48:4448"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:06:25:625","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to account","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","target":"http://localhost:8003","timestamp":"2025-06-03 14:06:25:625"}
{"contentLength":46748,"level":"info","message":"✅ Response from account","path":"api/service","status":200,"timestamp":"2025-06-03 14:06:28:628"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:213:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:205:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 14:06:29:629"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:225:20)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 14:06:29:629"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:07:07:77","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to account","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","target":"http://localhost:8003","timestamp":"2025-06-03 14:07:07:77"}
{"contentLength":46748,"level":"info","message":"✅ Response from account","path":"api/service","status":200,"timestamp":"2025-06-03 14:07:10:710"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:211:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:203:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 14:07:10:710"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 14:07:10:710"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:20:32:2032","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to account","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","target":"http://localhost:8003","timestamp":"2025-06-03 14:20:32:2032"}
{"contentType":"application/json; charset=utf-8","level":"info","message":"✅ Response from account","path":"api/service","status":200,"timestamp":"2025-06-03 14:20:36:2036"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/accoun","query":{"entity":"accoun","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:24:09:249","userAgent":"PostmanRuntime/7.44.0"}
{"level":"info","message":"🔍 Route not found","method":"GET","path":"/","timestamp":"2025-06-03 14:24:09:249"}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 14:37:53:3753"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 14:37:53:3753"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 14:37:53:3753"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 14:37:53:3753"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 14:37:53:3753"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 14:37:53:3753"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 14:37:53:3753","totalServices":6}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/accoun","query":{"entity":"accoun","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:38:01:381","userAgent":"PostmanRuntime/7.44.0"}
{"level":"info","message":"🔍 Route not found","method":"GET","path":"/","timestamp":"2025-06-03 14:38:01:381"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/accoun","query":{"entity":"accoun","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:38:03:383","userAgent":"PostmanRuntime/7.44.0"}
{"level":"info","message":"🔍 Route not found","method":"GET","path":"/","timestamp":"2025-06-03 14:38:03:383"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:38:22:3822","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"ACCOUNT","timestamp":"2025-06-03 14:38:22:3822"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","queryParams":{"entity":"account","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"ACCOUNT","target":{"fullProxyUrl":"http://localhost:8003/api/service?service=qbo&entity=account","url":"http://localhost:8003"},"timestamp":"2025-06-03 14:38:22:3822"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Authentication required","Invalid token"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"2814ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=account","path":"api/service"},"response":{"cacheControl":"no-store, no-cache, must-revalidate, max-age=0","contentLength":"159","contentType":"application/json; charset=utf-8","etag":"W/\"9f-mQGejeD2TqsA5GMqYMuMcgryey0\"","statusCode":401,"statusMessage":"Unauthorized"},"service":"ACCOUNT","target":{"service":"account","url":"http://localhost:8003"},"timestamp":"2025-06-03 14:38:24:3824","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8003api/service","checkService":"curl -X GET http://localhost:8003/health","serviceStatus":"Check if target service is running"}}
{"level":"warn","message":"⏱️ MODERATE RESPONSE TIME","path":"api/service","responseTime":"2814ms","service":"ACCOUNT","status":"Monitor closely","threshold":"2000ms","timestamp":"2025-06-03 14:38:24:3824"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:38:54:3854","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"ACCOUNT","timestamp":"2025-06-03 14:38:54:3854"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","queryParams":{"entity":"account","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"ACCOUNT","target":{"fullProxyUrl":"http://localhost:8003/api/service?service=qbo&entity=account","url":"http://localhost:8003"},"timestamp":"2025-06-03 14:38:54:3854"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Authentication required","Invalid token"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"2802ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=account","path":"api/service"},"response":{"cacheControl":"no-store, no-cache, must-revalidate, max-age=0","contentLength":"159","contentType":"application/json; charset=utf-8","etag":"W/\"9f-mQGejeD2TqsA5GMqYMuMcgryey0\"","statusCode":401,"statusMessage":"Unauthorized"},"service":"ACCOUNT","target":{"service":"account","url":"http://localhost:8003"},"timestamp":"2025-06-03 14:38:57:3857","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8003api/service","checkService":"curl -X GET http://localhost:8003/health","serviceStatus":"Check if target service is running"}}
{"level":"warn","message":"⏱️ MODERATE RESPONSE TIME","path":"api/service","responseTime":"2802ms","service":"ACCOUNT","status":"Monitor closely","threshold":"2000ms","timestamp":"2025-06-03 14:38:57:3857"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/account","query":{"entity":"account","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 14:43:21:4321","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"ACCOUNT","timestamp":"2025-06-03 14:43:21:4321"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=account","proxyPath":"/api/service?service=qbo&entity=account","queryParams":{"entity":"account","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"ACCOUNT","target":{"fullProxyUrl":"http://localhost:8003/api/service?service=qbo&entity=account","url":"http://localhost:8003"},"timestamp":"2025-06-03 14:43:21:4321"}
{"level":"info","message":"✅ PROXY RESPONSE SUCCESS","method":"GET","performance":{"responseTime":"3530ms","status":"SUCCESS"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=account","path":"api/service"},"response":{"cacheControl":"no-store, no-cache, must-revalidate, max-age=0","contentLength":"46748","contentType":"application/json; charset=utf-8","etag":"W/\"b69c-ivChr5BMISZaT9uGutc5r8L64ZQ\"","statusCode":200,"statusMessage":"OK"},"service":"ACCOUNT","target":{"service":"account","url":"http://localhost:8003"},"timestamp":"2025-06-03 14:43:25:4325"}
{"level":"warn","message":"⏱️ MODERATE RESPONSE TIME","path":"api/service","responseTime":"3530ms","service":"ACCOUNT","status":"Monitor closely","threshold":"2000ms","timestamp":"2025-06-03 14:43:25:4325"}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 15:36:27:3627"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 15:36:27:3627"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 15:36:27:3627"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 15:36:27:3627"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 15:36:27:3627"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 15:36:27:3627"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 15:36:27:3627","totalServices":6}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:37:39:3739","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:37:39:3739"}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 15:38:41:3841"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 15:38:41:3841"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 15:38:41:3841"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 15:38:41:3841"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 15:38:41:3841"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 15:38:41:3841"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 15:38:41:3841","totalServices":6}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 15:39:09:399"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 15:39:09:399"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 15:39:09:399"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 15:39:09:399"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 15:39:09:399"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 15:39:09:399"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 15:39:09:399","totalServices":6}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:39:12:3912","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:39:12:3912"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:39:15:3915","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:39:15:3915"}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 15:39:32:3932"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 15:39:32:3932"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 15:39:32:3932"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 15:39:32:3932"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 15:39:32:3932"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 15:39:32:3932"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 15:39:32:3932","totalServices":6}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 15:39:37:3937"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 15:39:37:3937"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 15:39:37:3937"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 15:39:37:3937"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 15:39:37:3937"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 15:39:37:3937"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 15:39:37:3937","totalServices":6}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:39:42:3942","userAgent":"PostmanRuntime/7.44.0"}
{"clientIp":"::1","errorResponse":{"error":{"code":102,"errorDescription":"Request body is required for POST requests to vendor service","status":400},"message":"Bad request: Empty request body","responseStatus":400},"level":"error","message":"🚫 EMPTY REQUEST BODY","method":"POST","path":"/","service":"VENDOR","timestamp":"2025-06-03 15:39:42:3942","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:40:05:405","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET","POST"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"VENDOR","timestamp":"2025-06-03 15:40:05:405"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"contentType":"application/json","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","queryParams":{"entity":"vendor","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"VENDOR","target":{"fullProxyUrl":"http://localhost:8001/api/service?service=qbo&entity=vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:05:405"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"9ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:05:405","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:40:20:4020","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET","POST"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"VENDOR","timestamp":"2025-06-03 15:40:20:4020"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"contentType":"application/json","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","queryParams":{"entity":"vendor","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"VENDOR","target":{"fullProxyUrl":"http://localhost:8001/api/service?service=qbo&entity=vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:20:4020"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"3ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:20:4020","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 15:40:46:4046"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 15:40:46:4046"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 15:40:46:4046"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 15:40:46:4046"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 15:40:46:4046"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 15:40:46:4046"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 15:40:46:4046","totalServices":6}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 15:40:57:4057"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 15:40:57:4057"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 15:40:57:4057"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 15:40:57:4057"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 15:40:57:4057"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 15:40:57:4057"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 15:40:57:4057","totalServices":6}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:40:59:4059","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET","POST"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"VENDOR","timestamp":"2025-06-03 15:40:59:4059"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"contentType":"application/json","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","queryParams":{"entity":"vendor","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"VENDOR","target":{"fullProxyUrl":"http://localhost:8001/api/service?service=qbo&entity=vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:59:4059"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"7ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:59:4059","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 15:41:27:4127"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 15:41:27:4127"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 15:41:27:4127"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 15:41:27:4127"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 15:41:27:4127"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 15:41:27:4127"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 15:41:27:4127","totalServices":6}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:41:28:4128","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET","POST"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"VENDOR","timestamp":"2025-06-03 15:41:28:4128"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"contentType":"application/json","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","queryParams":{"entity":"vendor","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"VENDOR","target":{"fullProxyUrl":"http://localhost:8001/api/service?service=qbo&entity=vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:41:28:4128"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"10ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:41:28:4128","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:42:06:426","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:42:06:426"}
{"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:06:426"}
{"level":"error","message":"❌ Error response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:06:426"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:42:08:428","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:42:08:428"}
{"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:08:428"}
{"level":"error","message":"❌ Error response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:08:428"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:42:23:4223","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:42:23:4223"}
{"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:23:4223"}
{"level":"error","message":"❌ Error response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:23:4223"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:42:32:4232","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:42:32:4232"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:32:4232"}
{"error":{"error":{"code":105,"errorDescription":"Error from vendor service","message":"Internal server error","status":500},"responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 15:42:32:4232"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:42:36:4236","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:42:36:4236"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:36:4236"}
{"error":{"error":{"code":105,"errorDescription":"Error from vendor service","message":"Internal server error","status":500},"responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 15:42:36:4236"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:43:01:431","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:43:01:431"}
{"level":"warn","message":"⚠️ GET request to vendor had body data - ignoring to prevent socket errors","timestamp":"2025-06-03 15:43:01:431"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 15:43:04:434"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:43:04:434"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:43:04:434"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:43:44:4344","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:43:44:4344"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:43:45:4345"}
{"error":{"error":{"code":105,"errorDescription":"Error from vendor service","message":"Internal server error","status":500},"responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 15:43:45:4345"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:43:56:4356","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:43:56:4356"}
{"level":"warn","message":"⚠️ GET request to vendor had body data - ignoring to prevent socket errors","timestamp":"2025-06-03 15:43:56:4356"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 15:43:59:4359"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:43:59:4359"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:43:59:4359"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:44:12:4412"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:44:44:4444"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:44:53:4453","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:44:53:4453"}
{"level":"warn","message":"⚠️ GET request to vendor had body data - ignoring to prevent socket errors","timestamp":"2025-06-03 15:44:53:4453"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 15:44:56:4456"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:44:56:4456"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:44:56:4456"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:45:11:4511","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:45:11:4511"}
{"level":"warn","message":"⚠️ GET request to vendor had body data - ignoring to prevent socket errors","timestamp":"2025-06-03 15:45:11:4511"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 15:45:13:4513"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:45:13:4513"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:45:14:4514"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:45:18:4518"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:48:03:483","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 15:48:03:483"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 15:48:06:486"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:48:06:486"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:192:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:48:06:486"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:48:21:4821","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:48:21:4821"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:50:58:5058","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:50:58:5058"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:51:00:510","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:51:00:510"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:51:32:5132","userAgent":"PostmanRuntime/7.44.0"}
{"ip":"::1","level":"error","message":"🚫 EMPTY BODY ERROR","method":"POST","path":"/","service":"VENDOR","timestamp":"2025-06-03 15:51:32:5132"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 15:54:47:5447","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:54:47:5447"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:03:50:350","userAgent":"PostmanRuntime/7.44.0"}
{"ip":"::1","level":"error","message":"🚫 EMPTY BODY ERROR","method":"POST","path":"/","service":"VENDOR","timestamp":"2025-06-03 16:03:50:350"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:05:20:520","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:05:20:520"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:05:20:520"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:05:21:521"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:05:59:559","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:05:59:559"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:05:59:559"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:05:59:559"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:07:53:753","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:07:53:753"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:07:53:753"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:07:53:753"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:08:28:828","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:08:28:828"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:08:28:828"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:08:28:828"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:11:51:1151","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:11:51:1151"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:11:51:1151"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:11:51:1151"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:13:55:1355","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:13:55:1355"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:13:55:1355"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:13:55:1355"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:14:25:1425","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:14:25:1425"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:14:25:1425"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:14:25:1425"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:15:01:151","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:15:01:151"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:15:01:151"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:15:01:151"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:15:50:1550","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:15:50:1550"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:15:50:1550"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:15:50:1550"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:24:19:2419","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:24:19:2419"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:24:19:2419"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service (unparseable)","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:24:19:2419"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:27:13:2713","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:27:13:2713"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:27:13:2713"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service (unparseable)","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:27:13:2713"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:27:15:2715","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:27:15:2715"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:27:15:2715"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service (unparseable)","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:27:15:2715"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:28:19:2819","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:28:19:2819"}
{"contentLength":0,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:28:19:2819"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:28:19:2819"}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 16:28:42:2842"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 16:28:42:2842"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 16:28:42:2842"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 16:28:42:2842"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 16:28:42:2842"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 16:28:42:2842"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 16:28:42:2842","totalServices":6}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:28:42:2842","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET","POST"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"VENDOR","timestamp":"2025-06-03 16:28:42:2842"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"contentType":"application/json","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","queryParams":{"entity":"vendor","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"VENDOR","target":{"fullProxyUrl":"http://localhost:8001/api/service?service=qbo&entity=vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 16:28:42:2842"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"44ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 16:28:43:2843","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:28:44:2844","userAgent":"PostmanRuntime/7.44.0"}
{"allowedMethods":["GET","POST"],"level":"debug","message":"✅ METHOD VALIDATION PASSED","method":"GET","path":"/","service":"VENDOR","timestamp":"2025-06-03 16:28:44:2844"}
{"bodySize":2,"client":{"ip":"::1"},"hasBody":false,"headers":{"auth":{"accesstoken":"[MASKED]","companyid":"4620816365356807410"},"forwarded":2},"level":"info","message":"🚀 PROXY REQUEST INITIATED","method":"GET","request":{"contentType":"application/json","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","queryParams":{"entity":"vendor","service":"qbo"},"userAgent":"PostmanRuntime/7.44.0"},"service":"VENDOR","target":{"fullProxyUrl":"http://localhost:8001/api/service?service=qbo&entity=vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 16:28:44:2844"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"2ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 16:28:44:2844","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"allowedMethods":["GET","POST"],"envVar":"VENDOR_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/vendor":""},"route":"/api/vendor","service":"VENDOR","target":"http://localhost:8001","timestamp":"2025-06-03 16:30:37:3037"}
{"allowedMethods":["GET","POST"],"envVar":"BILL_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/bill":""},"route":"/api/bill","service":"BILL","target":"http://localhost:8006","timestamp":"2025-06-03 16:30:37:3037"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/journalentry":""},"route":"/api/journalentry","service":"JOURNALENTRY","target":"http://localhost:8005","timestamp":"2025-06-03 16:30:37:3037"}
{"allowedMethods":["POST"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/payment":""},"route":"/api/payment","service":"PAYMENT","target":"http://localhost:8004","timestamp":"2025-06-03 16:30:37:3037"}
{"allowedMethods":["GET"],"envVar":"ACCOUNT_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/account":""},"route":"/api/account","service":"ACCOUNT","target":"http://localhost:8003","timestamp":"2025-06-03 16:30:37:3037"}
{"allowedMethods":["GET"],"envVar":"CLASS_SERVICE_URL","level":"info","message":"🔗 PROXY SERVICE REGISTERED","pathRewrite":{"^/api/class":""},"route":"/api/class","service":"CLASS","target":"http://localhost:8002","timestamp":"2025-06-03 16:30:37:3037"}
{"environment":"development","level":"info","message":"🎯 PROXY SETUP COMPLETED","services":["VENDOR","BILL","JOURNALENTRY","PAYMENT","ACCOUNT","CLASS"],"timestamp":"2025-06-03 16:30:37:3037","totalServices":6}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:30:56:3056","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":true,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:30:56:3056"}
{"level":"warn","message":"⚠️ GET request to vendor had body data - ignoring to prevent socket errors","timestamp":"2025-06-03 16:30:56:3056"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 16:30:59:3059"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:30:59:3059"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:30:59:3059"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:31:16:3116"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:32:05:325"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:32:39:3239","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:32:39:3239"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 16:32:42:3242"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:32:42:3242"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:32:42:3242"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:32:51:3251","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying POST to vendor","method":"POST","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:32:51:3251"}
{"contentLength":1068,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:32:51:3251"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:32:51:3251"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:32:51:3251"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:32:57:3257","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:32:57:3257"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 16:33:00:330"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:33:00:330"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:33:00:330"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:33:17:3317","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:33:17:3317"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 16:33:19:3319"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:33:19:3319"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:33:19:3319"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:33:25:3325","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying POST to vendor","method":"POST","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:33:25:3325"}
{"contentLength":1068,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:33:25:3325"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:33:25:3325"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:33:25:3325"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:33:47:3347","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying POST to vendor","method":"POST","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:33:47:3347"}
{"contentLength":159,"level":"info","message":"✅ Response from vendor","path":"api/service","status":401,"timestamp":"2025-06-03 16:33:50:3350"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:33:50:3350"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:33:50:3350"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:34:39:3439","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying POST to vendor","method":"POST","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:34:39:3439"}
{"contentLength":74,"level":"info","message":"✅ Response from vendor","path":"api/service","status":200,"timestamp":"2025-06-03 16:34:42:3442"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:211:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:203:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:34:42:3442"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:34:42:3442"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:35:16:3516","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"POST","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:16:3516"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:35:22:3522","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:22:3522"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:35:23:3523","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:23:3523"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:35:25:3525","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:25:3525"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:35:25:3525","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:25:3525"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:35:26:3526","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:26:3526"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:36:22:3622"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:36:23:3623"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:36:24:3624"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:36:39:3639","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:36:39:3639"}
{"contentLength":18415,"level":"info","message":"✅ Response from vendor","path":"api/service","status":200,"timestamp":"2025-06-03 16:36:42:3642"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:211:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:203:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:36:42:3642"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:36:42:3642"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:38:00:380","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:38:00:380"}
{"contentLength":18415,"level":"info","message":"✅ Response from vendor","path":"api/service","status":200,"timestamp":"2025-06-03 16:38:03:383"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:211:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:203:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:38:03:383"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:38:03:383"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:39:03:393","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:39:03:393"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:39:24:3924","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:39:24:3924"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:40:19:4019","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:40:19:4019"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:40:27:4027","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:40:27:4027"}
{"contentLength":18415,"level":"info","message":"✅ Response from vendor","path":"api/service","status":200,"timestamp":"2025-06-03 16:40:30:4030"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:211:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:203:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:40:30:4030"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:40:30:4030"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:40:45:4045","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying POST to vendor","method":"POST","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:40:45:4045"}
{"contentLength":1068,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:40:45:4045"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:40:45:4045"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:40:45:4045"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:41:03:413","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"POST","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:41:03:413"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:51:18:5118","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying POST to vendor","method":"POST","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:51:18:5118"}
{"contentLength":1068,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:51:18:5118"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:51:18:5118"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:51:18:5118"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:51:32:5132","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying POST to vendor","method":"POST","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:51:32:5132"}
{"contentLength":1068,"level":"info","message":"✅ Response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 16:51:32:5132"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:51:32:5132"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:51:32:5132"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:55:46:5546","userAgent":"PostmanRuntime/7.44.0"}
{"error":"Unexpected end of JSON input","ip":"::1","level":"error","message":"🚫 JSON PARSE ERROR for vendor","method":"POST","path":"/","service":"VENDOR","timestamp":"2025-06-03 16:55:46:5546"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:56:05:565","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 16:56:05:565"}
{"contentLength":18415,"level":"info","message":"✅ Response from vendor","path":"api/service","status":200,"timestamp":"2025-06-03 16:56:08:568"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:212:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:204:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:56:08:568"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:222:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:56:08:568"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"POST","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 16:56:32:5632","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410","contentType":"application/json"},"level":"info","message":"📥 Incoming request","method":"GET","path":"/api/vendor","query":{"entity":"vendor","service":"qbo"},"requestId":"unknown","timestamp":"2025-06-03 17:03:16:316","userAgent":"PostmanRuntime/7.44.0"}
{"hasBody":false,"headers":{"accesstoken":"[PROVIDED]","companyid":"4620816365356807410"},"level":"info","message":"🔄 Proxying GET to vendor","method":"GET","originalPath":"api/service","originalUrl":"api/service?service=qbo&entity=vendor","proxyPath":"/api/service?service=qbo&entity=vendor","target":"http://localhost:8001","timestamp":"2025-06-03 17:03:16:316"}
{"contentLength":18415,"level":"info","message":"✅ Response from vendor","path":"api/service","status":200,"timestamp":"2025-06-03 17:03:19:319"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"warn","message":"⚠️ JSON parse error from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:212:25\n    at Array.forEach (<anonymous>)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:204:47)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 17:03:19:319"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:222:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 17:03:19:319"}
