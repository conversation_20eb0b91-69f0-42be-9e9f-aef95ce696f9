/**
 * Test script for Bill Payment API
 * This script demonstrates how to create bill payments using the payment service
 */

const axios = require("axios");

// Configuration
const config = {
  baseUrl: "http://localhost:8000", // API Gateway URL
  accessToken: "YOUR_ACCESS_TOKEN", // Replace with actual access token
  companyId: "YOUR_COMPANY_ID", // Replace with actual company ID
};

// Test data for bill payment
const billPaymentData = {
  data: {
    VendorRef: {
      value: "56", // Replace with actual vendor ID
    },
    PayType: "Check",
    CheckPayment: {
      BankAccountRef: {
        value: "35", // Replace with actual bank account ID
      },
      PrintStatus: "NeedToPrint",
    },
    TotalAmt: 200.0,
    Line: [
      {
        Amount: 200.0,
        LinkedTxn: [
          {
            TxnId: "145", // Replace with actual bill ID
            TxnType: "Bill",
          },
        ],
      },
    ],
  },
};

// Test function to create a bill payment
async function testCreateBillPayment() {
  try {
    console.log("🧪 Testing Bill Payment Creation...");
    console.log("📤 Request Data:", JSON.stringify(billPaymentData, null, 2));

    const response = await axios.post(
      `${config.baseUrl}/api/payment/service?service=qbo&entity=payment`,
      billPaymentData,
      {
        headers: {
          accesstoken: config.accessToken,
          companyid: config.companyId,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("✅ Bill Payment Created Successfully!");
    console.log("📥 Response:", JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error("❌ Error creating bill payment:");
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", JSON.stringify(error.response.data, null, 2));
    } else {
      console.error("Error:", error.message);
    }
    throw error;
  }
}

// Test function for multiple bills payment
async function testMultipleBillsPayment() {
  const multipleBillsData = {
    data: {
      VendorRef: {
        value: "56",
      },
      PayType: "Check",
      CheckPayment: {
        BankAccountRef: {
          value: "35",
        },
        PrintStatus: "NeedToPrint",
      },
      TotalAmt: 500.0,
      Line: [
        {
          Amount: 200.0,
          LinkedTxn: [
            {
              TxnId: "145",
              TxnType: "Bill",
            },
          ],
        },
        {
          Amount: 300.0,
          LinkedTxn: [
            {
              TxnId: "146",
              TxnType: "Bill",
            },
          ],
        },
      ],
    },
  };

  try {
    console.log("\n🧪 Testing Multiple Bills Payment...");
    console.log("📤 Request Data:", JSON.stringify(multipleBillsData, null, 2));

    const response = await axios.post(
      `${config.baseUrl}/api/payment/service?service=qbo&entity=payment`,
      multipleBillsData,
      {
        headers: {
          accesstoken: config.accessToken,
          companyid: config.companyId,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("✅ Multiple Bills Payment Created Successfully!");
    console.log("📥 Response:", JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error("❌ Error creating multiple bills payment:");
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", JSON.stringify(error.response.data, null, 2));
    } else {
      console.error("Error:", error.message);
    }
    throw error;
  }
}

// Test function for credit card payment
async function testCreditCardBillPayment() {
  const creditCardData = {
    data: {
      VendorRef: {
        value: "56",
      },
      PayType: "CreditCard",
      CreditCardPayment: {
        CreditCardAccountRef: {
          value: "41", // Replace with actual credit card account ID
        },
      },
      TotalAmt: 150.0,
      Line: [
        {
          Amount: 150.0,
          LinkedTxn: [
            {
              TxnId: "147",
              TxnType: "Bill",
            },
          ],
        },
      ],
    },
  };

  try {
    console.log("\n🧪 Testing Credit Card Bill Payment...");
    console.log("📤 Request Data:", JSON.stringify(creditCardData, null, 2));

    const response = await axios.post(
      `${config.baseUrl}/api/payment/service?service=qbo&entity=payment`,
      creditCardData,
      {
        headers: {
          accesstoken: config.accessToken,
          companyid: config.companyId,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("✅ Credit Card Bill Payment Created Successfully!");
    console.log("📥 Response:", JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error("❌ Error creating credit card bill payment:");
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", JSON.stringify(error.response.data, null, 2));
    } else {
      console.error("Error:", error.message);
    }
    throw error;
  }
}

// Main test runner
async function runTests() {
  console.log("🚀 Starting Bill Payment API Tests...\n");

  // Check configuration
  if (
    config.accessToken === "YOUR_ACCESS_TOKEN" ||
    config.companyId === "YOUR_COMPANY_ID"
  ) {
    console.warn(
      "⚠️  Please update the configuration with actual access token and company ID"
    );
    console.warn(
      "⚠️  Update the vendor IDs, bank account IDs, and bill IDs in the test data"
    );
    return;
  }

  try {
    // Test 1: Single bill payment
    await testCreateBillPayment();

    // Test 2: Multiple bills payment
    await testMultipleBillsPayment();

    // Test 3: Credit card payment
    await testCreditCardBillPayment();

    console.log("\n🎉 All tests completed successfully!");
  } catch (error) {
    console.log("\n💥 Tests failed. Please check the error messages above.");
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testCreateBillPayment,
  testMultipleBillsPayment,
  testCreditCardBillPayment,
  runTests,
};
