{"compilerOptions": {"target": "es6", "lib": ["es2018", "DOM"], "module": "commonjs", "moduleResolution": "node", "typeRoots": ["./types", "./node_modules/@types"], "types": [], "resolveJsonModule": true, "outDir": "./dist", "rootDir": "./app", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipDefaultLibCheck": true, "skipLibCheck": true}, "include": ["app/**/*", "/.env"]}