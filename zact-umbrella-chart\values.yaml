# Default values for zact-umbrella chart
# This file contains defaults only - environment-specific values should be in separate files

# Global settings shared across all services
global:
  # Image settings
  imageTag: "latest"
  imagePullPolicy: "Always"

  # Environment (will be overridden by environment-specific files)
  nodeEnv: "production"

  # QuickBooks API settings (defaults - will be overridden)
  qboApiUrl: ""
  qboMinorVersion: "75"

# Default resource settings for all services
defaultResources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# Individual service configurations
account-service:
  enabled: true
  replicaCount: 1
  image:
    repository: "shivraj77/account-service"
    tag: "" # Uses global.imageTag
  service:
    port: 8003
    type: ClusterIP
  resources: {} # Uses defaultResources

bill-service:
  enabled: true
  replicaCount: 1
  image:
    repository: "shivraj77/bill-service"
    tag: "" # Uses global.imageTag
  service:
    port: 8006
    type: ClusterIP
  resources: {} # Uses defaultResources

class-service:
  enabled: true
  replicaCount: 1
  image:
    repository: "shivraj77/class-service"
    tag: "" # Uses global.imageTag
  service:
    port: 8002
    type: ClusterIP
  resources: {} # Uses defaultResources

journalentry-service:
  enabled: true
  replicaCount: 1
  image:
    repository: "shivraj77/journalentry-service"
    tag: "" # Uses global.imageTag
  service:
    port: 8005
    type: ClusterIP
  resources: {} # Uses defaultResources

payment-service:
  enabled: true
  replicaCount: 1
  image:
    repository: "shivraj77/payment-service"
    tag: "" # Uses global.imageTag
  service:
    port: 8004
    type: ClusterIP
  resources: {} # Uses defaultResources

vendor-service:
  enabled: true
  replicaCount: 1
  image:
    repository: "shivraj77/vendor-service"
    tag: "" # Uses global.imageTag
  service:
    port: 8001
    type: ClusterIP
  resources: {} # Uses defaultResources

api-gateway:
  enabled: true
  replicaCount: 1
  image:
    repository: "shivraj77/api-gateway"
    tag: "" # Uses global.imageTag
  service:
    port: 8000
    type: LoadBalancer
  resources: {} # Uses defaultResources
  env:
    ACCOUNT_SERVICE_URL: "" # Will be set by environment files
    BILL_SERVICE_URL: ""
    CLASS_SERVICE_URL: ""
    JOURNALENTRY_SERVICE_URL: ""
    PAYMENT_SERVICE_URL: ""
    VENDOR_SERVICE_URL: ""

# Autoscaling (disabled by default)
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80

# Service Account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod Security Context
podSecurityContext: {}

# Security Context
securityContext: {}

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}
