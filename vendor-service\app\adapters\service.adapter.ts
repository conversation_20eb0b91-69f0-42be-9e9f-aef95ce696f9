import { qboService } from "../services/qboApiServices";
import ApiException from "../utils/api-exception";
import { ErrorCodes } from "../utils/response";

interface ServiceRequest {
  service: string;
  entity: string;
  requestType: "get" | "post";
  accessToken: string;
  companyId: string;
  bodyData?: any; // for POST body
  queryParams?: any; // for GET or POST query params
}

export const processServiceRequest = async (request: ServiceRequest) => {
  const {
    service,
    entity,
    requestType,
    bodyData,
    queryParams,
    accessToken,
    companyId,
  } = request;

  let serviceHandler;

  switch (service.toLowerCase()) {
    case "qbo":
      serviceHandler = qboService;
      break;
    default:
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: `Unsupported service: ${service}`,
      });
  }

  if (requestType === "get") {
    return await serviceHandler.get(entity, {
      accessToken,
      companyId,
      queryParams,
    });
  } else if (requestType === "post") {
    return await serviceHandler.post(entity, {
      accessToken,
      companyId,
      bodyData,
      queryParams,
    });
  } else {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: `Unsupported request type: ${requestType}`,
    });
  }
};
