0{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 11:35:37:3537"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: bcd74e14f27155c8fb4388314af0fbcb ","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"2025-05-27T06:08:20.541Z  GET qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"Response sent with status: 200 - MessageID: bcd74e14f27155c8fb4388314af0fbcb","timestamp":"2025-05-27 11:38:23:3823"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: 1df82a54c249516e5c981c84874caace ","timestamp":"2025-05-27 11:42:34:4234"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"POST /api/service?service=qbo&entity=bill: Bad request","stack":"Error: Bad request\n    at validateAuthParams (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:40:11)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:57:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:51:52\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1df82a54c249516e5c981c84874caace","timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:11:37:1137"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313 ","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"2025-05-27T06:42:11.889Z  POST qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"QBO POST request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313","timestamp":"2025-05-27 12:12:15:1215"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: 68e79fd371f2326cb390a307c4cbf901 ","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"2025-05-27T06:45:07.909Z  GET qbo bill | companyId=4620816365356807410","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: 4620816365356807410","timestamp":"2025-05-27 12:15:07:157"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 68e79fd371f2326cb390a307c4cbf901","timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:50:36:5036"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 86fc654fe31165db6c7e6d29964b475d ","timestamp":"2025-05-27 12:56:31:5631"}
{"level":"info","message":"2025-05-27T07:26:31.447Z  POST qbo payment","timestamp":"2025-05-27 12:56:31:5631"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 12:56:31:5631"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 12:56:33:5633"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 86fc654fe31165db6c7e6d29964b475d","timestamp":"2025-05-27 12:56:33:5633"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:19:5819"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:33:5833"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:44:5844"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:58:58:5858"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:59:18:5918"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:59:53:5953"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:01:01"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:10:010"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:15:015"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:19:019"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:00:33:033"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 8a9ad1acefbcd60126b61b9037eb0504 ","timestamp":"2025-05-27 13:00:36:036"}
{"level":"info","message":"2025-05-27T07:30:36.811Z  POST qbo payment","timestamp":"2025-05-27 13:00:36:036"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:00:36:036"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:00:38:038"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 8a9ad1acefbcd60126b61b9037eb0504","timestamp":"2025-05-27 13:00:38:038"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:00:10"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:13:113"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:28:128"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:32:132"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:01:38:138"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: d3d64d2c14101b5a4b10f3e39137c9ee ","timestamp":"2025-05-27 13:01:41:141"}
{"level":"info","message":"2025-05-27T07:31:41.524Z  POST qbo payment","timestamp":"2025-05-27 13:01:41:141"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:01:41:141"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:01:43:143"}
{"level":"info","message":"Response sent with status: 400 - MessageID: d3d64d2c14101b5a4b10f3e39137c9ee","timestamp":"2025-05-27 13:01:43:143"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:02:06:26"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:02:39:239"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: e0df83fa570e7d661885f3e1090358f8 ","timestamp":"2025-05-27 13:02:40:240"}
{"level":"info","message":"2025-05-27T07:32:40.059Z  POST qbo payment","timestamp":"2025-05-27 13:02:40:240"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:02:40:240"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:02:41:241"}
{"level":"info","message":"Response sent with status: 400 - MessageID: e0df83fa570e7d661885f3e1090358f8","timestamp":"2025-05-27 13:02:41:241"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:03:01:31"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 7cce08f2d12576f829d33795181942b6 ","timestamp":"2025-05-27 13:03:04:34"}
{"level":"info","message":"2025-05-27T07:33:04.320Z  POST qbo payment","timestamp":"2025-05-27 13:03:04:34"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:03:04:34"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:03:05:35"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 7cce08f2d12576f829d33795181942b6","timestamp":"2025-05-27 13:03:05:35"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:39:439"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:48:448"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:04:53:453"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:05:02:52"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: bfb2a6c14503df03aa1b86c070bfb61d ","timestamp":"2025-05-27 13:05:07:57"}
{"level":"info","message":"2025-05-27T07:35:07.944Z  POST qbo payment","timestamp":"2025-05-27 13:05:07:57"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:05:07:57"}
{"code":105,"errorDescription":"Invalid Reference Id","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:05:10:510"}
{"level":"info","message":"Response sent with status: 400 - MessageID: bfb2a6c14503df03aa1b86c070bfb61d","timestamp":"2025-05-27 13:05:10:510"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 64c5f9d1ac24384f220e0703339ad95a ","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"2025-05-27T07:36:11.388Z  POST qbo payment","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:06:11:611"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 64c5f9d1ac24384f220e0703339ad95a","timestamp":"2025-05-27 13:06:12:612"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:07:16:716"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=payment - MessageID: 38e8ceac9138f359fdc1fc533d225ee8 ","timestamp":"2025-05-27 13:07:20:720"}
{"level":"info","message":"2025-05-27T07:37:20.537Z  POST qbo payment","timestamp":"2025-05-27 13:07:20:720"}
{"level":"info","message":"QBO POST request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:07:20:720"}
{"code":105,"errorDescription":"Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id","stack":"Error: Invalid Reference Id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:07:22:722"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 38e8ceac9138f359fdc1fc533d225ee8","timestamp":"2025-05-27 13:07:22:722"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:08:06:86"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=payment - MessageID: a43a4ed744be7fd43a959ee93717decd ","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"2025-05-27T07:40:21.277Z  GET qbo payment","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"QBO GET request for entity: payment | companyId: 9341454441737033","timestamp":"2025-05-27 13:10:21:1021"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a43a4ed744be7fd43a959ee93717decd","timestamp":"2025-05-27 13:10:23:1023"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:13:52:1352"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 38bc260d576e0ba386b3e2fa02e4c337 ","timestamp":"2025-05-27 14:16:25:1625"}
{"level":"info","message":"2025-05-27T08:46:25.815Z  GET qbo vendor","timestamp":"2025-05-27 14:16:25:1625"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:16:25:1625"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 14:16:27:1627"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 38bc260d576e0ba386b3e2fa02e4c337","timestamp":"2025-05-27 14:16:27:1627"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 20168854bde7307e9d40ca6d2254fcbe ","timestamp":"2025-05-27 14:18:16:1816"}
{"level":"info","message":"2025-05-27T08:48:16.437Z  GET qbo vendor","timestamp":"2025-05-27 14:18:16:1816"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:18:16:1816"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 20168854bde7307e9d40ca6d2254fcbe","timestamp":"2025-05-27 14:18:17:1817"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: a67ea6c897964f55703e4dda17765c5f ","timestamp":"2025-05-27 14:19:14:1914"}
{"level":"info","message":"2025-05-27T08:49:14.675Z  GET qbo vendor","timestamp":"2025-05-27 14:19:14:1914"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:19:14:1914"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a67ea6c897964f55703e4dda17765c5f","timestamp":"2025-05-27 14:19:16:1916"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 398617914d778f960c950532a52f3844 ","timestamp":"2025-05-27 14:20:22:2022"}
{"level":"info","message":"2025-05-27T08:50:22.745Z  GET qbo vendor","timestamp":"2025-05-27 14:20:22:2022"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:20:22:2022"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 398617914d778f960c950532a52f3844","timestamp":"2025-05-27 14:20:24:2024"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:23:02:232"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 184d23ff38b09028368c2e2de432bc43 ","timestamp":"2025-05-27 14:23:03:233"}
{"level":"info","message":"2025-05-27T08:53:03.720Z  GET qbo vendor","timestamp":"2025-05-27 14:23:03:233"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:23:03:233"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 184d23ff38b09028368c2e2de432bc43","timestamp":"2025-05-27 14:23:05:235"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 9c742ed63d9611cc4c31c889859760c8 ","timestamp":"2025-05-27 14:34:44:3444"}
{"level":"info","message":"2025-05-27T09:04:44.092Z  POST qbo vendor","timestamp":"2025-05-27 14:34:44:3444"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:34:44:3444"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 9c742ed63d9611cc4c31c889859760c8","timestamp":"2025-05-27 14:34:46:3446"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: c9dff688391090979ad8ec5f41f7518a ","timestamp":"2025-05-27 14:35:29:3529"}
{"level":"info","message":"2025-05-27T09:05:29.605Z  POST qbo vendor","timestamp":"2025-05-27 14:35:29:3529"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:35:29:3529"}
{"code":105,"errorDescription":"Title, GivenName, MiddleName, FamilyName, DisplayName, Suffix - one of these must be non-empty.","level":"error","message":"POST /api/service?service=qbo&entity=vendor: No name provided","stack":"Error: No name provided\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 14:35:31:3531"}
{"level":"info","message":"Response sent with status: 400 - MessageID: c9dff688391090979ad8ec5f41f7518a","timestamp":"2025-05-27 14:35:31:3531"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-05-27 14:40:11:4011"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 4c6acdd3bbc052a67f51a3c66eb4cc37 ","timestamp":"2025-05-27 14:47:12:4712"}
{"level":"info","message":"2025-05-27T09:17:12.906Z  POST qbo vendor","timestamp":"2025-05-27 14:47:12:4712"}
{"level":"info","message":"QBO POST request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:47:12:4712"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 4c6acdd3bbc052a67f51a3c66eb4cc37","timestamp":"2025-05-27 14:47:15:4715"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 0a7013b50485f3499bab5f9b36d77d98 ","timestamp":"2025-05-27 14:47:48:4748"}
{"level":"info","message":"2025-05-27T09:17:48.206Z  GET qbo vendor","timestamp":"2025-05-27 14:47:48:4748"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 14:47:48:4748"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 0a7013b50485f3499bab5f9b36d77d98","timestamp":"2025-05-27 14:47:49:4749"}
{"level":"info","message":"Server is listening on port 8001","timestamp":"2025-05-27 15:13:35:1335"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: b01c93c30cd56a3b9c9f4a820c564df2 ","timestamp":"2025-05-27 15:13:50:1350"}
{"level":"info","message":"2025-05-27T09:43:50.886Z  GET qbo vendor","timestamp":"2025-05-27 15:13:50:1350"}
{"level":"info","message":"QBO GET request for entity: vendor | companyId: 9341454441737033","timestamp":"2025-05-27 15:13:50:1350"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b01c93c30cd56a3b9c9f4a820c564df2","timestamp":"2025-05-27 15:13:52:1352"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:28:10:2810"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=class - MessageID: 24e16fe8ecda2be11828d1445a815dc6 ","timestamp":"2025-05-27 15:30:39:3039"}
{"level":"info","message":"2025-05-27T10:00:39.509Z  GET qbo class","timestamp":"2025-05-27 15:30:39:3039"}
{"level":"info","message":"QBO GET request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:30:39:3039"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 24e16fe8ecda2be11828d1445a815dc6","timestamp":"2025-05-27 15:30:42:3042"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=class - MessageID: b0d5708b4e1b62e1f0a6f911eaf30a97 ","timestamp":"2025-05-27 15:31:29:3129"}
{"level":"info","message":"2025-05-27T10:01:29.709Z  GET qbo class","timestamp":"2025-05-27 15:31:29:3129"}
{"level":"info","message":"QBO GET request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:31:29:3129"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b0d5708b4e1b62e1f0a6f911eaf30a97","timestamp":"2025-05-27 15:31:31:3131"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=class - MessageID: 0a00745a43ec421b453dccbd58084e63 ","timestamp":"2025-05-27 15:32:28:3228"}
{"level":"info","message":"2025-05-27T10:02:28.801Z  GET qbo class","timestamp":"2025-05-27 15:32:28:3228"}
{"level":"info","message":"QBO GET request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:32:28:3228"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 0a00745a43ec421b453dccbd58084e63","timestamp":"2025-05-27 15:32:30:3230"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=class - MessageID: 3f4efb2f91a15c21dde1e9ae502b8edf ","timestamp":"2025-05-27 15:34:34:3434"}
{"level":"info","message":"2025-05-27T10:04:34.043Z  POST qbo class","timestamp":"2025-05-27 15:34:34:3434"}
{"level":"info","message":"QBO POST request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:34:34:3434"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 3f4efb2f91a15c21dde1e9ae502b8edf","timestamp":"2025-05-27 15:34:35:3435"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=class - MessageID: 363c82ac080ffe2ae04bdb3541ebc1c3 ","timestamp":"2025-05-27 15:34:40:3440"}
{"level":"info","message":"2025-05-27T10:04:40.495Z  GET qbo class","timestamp":"2025-05-27 15:34:40:3440"}
{"level":"info","message":"QBO GET request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:34:40:3440"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 363c82ac080ffe2ae04bdb3541ebc1c3","timestamp":"2025-05-27 15:34:40:3440"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=class - MessageID: 4f61457929bb53cadea7609bf0f49d16 ","timestamp":"2025-05-27 15:35:15:3515"}
{"level":"info","message":"2025-05-27T10:05:15.693Z  POST qbo class","timestamp":"2025-05-27 15:35:15:3515"}
{"level":"info","message":"QBO POST request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:35:15:3515"}
{"code":105,"errorDescription":"The name supplied already exists. : Id=562017","level":"error","message":"POST /api/service?service=qbo&entity=class: Duplicate Name Exists Error","stack":"Error: Duplicate Name Exists Error\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 15:35:16:3516"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 4f61457929bb53cadea7609bf0f49d16","timestamp":"2025-05-27 15:35:16:3516"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=class - MessageID: 94d96a08d4281326397efcd0d778932c ","timestamp":"2025-05-27 15:35:26:3526"}
{"level":"info","message":"2025-05-27T10:05:26.196Z  POST qbo class","timestamp":"2025-05-27 15:35:26:3526"}
{"level":"info","message":"QBO POST request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:35:26:3526"}
{"code":105,"errorDescription":"The name supplied already exists. : Id=562017","level":"error","message":"POST /api/service?service=qbo&entity=class: Duplicate Name Exists Error","stack":"Error: Duplicate Name Exists Error\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 15:35:27:3527"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 94d96a08d4281326397efcd0d778932c","timestamp":"2025-05-27 15:35:27:3527"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=class - MessageID: 8927493822761e4aa085497ab2ef1c00 ","timestamp":"2025-05-27 15:36:02:362"}
{"level":"info","message":"2025-05-27T10:06:02.962Z  GET qbo class","timestamp":"2025-05-27 15:36:02:362"}
{"level":"info","message":"QBO GET request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:36:02:362"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8927493822761e4aa085497ab2ef1c00","timestamp":"2025-05-27 15:36:04:364"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=class - MessageID: ee329cf35167162ad2dc2a9f0569f63b ","timestamp":"2025-05-27 15:36:29:3629"}
{"level":"info","message":"2025-05-27T10:06:29.351Z  POST qbo class","timestamp":"2025-05-27 15:36:29:3629"}
{"level":"info","message":"QBO POST request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:36:29:3629"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ee329cf35167162ad2dc2a9f0569f63b","timestamp":"2025-05-27 15:36:30:3630"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=class - MessageID: b04645da4cb68231b137deacf8e1b400 ","timestamp":"2025-05-27 15:36:35:3635"}
{"level":"info","message":"2025-05-27T10:06:35.163Z  GET qbo class","timestamp":"2025-05-27 15:36:35:3635"}
{"level":"info","message":"QBO GET request for entity: class | companyId: 9341454441737033","timestamp":"2025-05-27 15:36:35:3635"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b04645da4cb68231b137deacf8e1b400","timestamp":"2025-05-27 15:36:35:3635"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=cla - MessageID: d148503cea82fa75a7268716367f6aef ","timestamp":"2025-05-27 15:39:10:3910"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'class' or 'CLASS'","level":"error","message":"GET /api/service?service=qbo&entity=cla: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:39:10:3910"}
{"level":"info","message":"Response sent with status: 400 - MessageID: d148503cea82fa75a7268716367f6aef","timestamp":"2025-05-27 15:39:10:3910"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=CLASS - MessageID: df657aa07c1cd83154d9a3e306424ad4 ","timestamp":"2025-05-27 15:39:19:3919"}
{"level":"info","message":"2025-05-27T10:09:19.390Z  GET qbo CLASS","timestamp":"2025-05-27 15:39:19:3919"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:39:19:3919"}
{"level":"info","message":"Response sent with status: 200 - MessageID: df657aa07c1cd83154d9a3e306424ad4","timestamp":"2025-05-27 15:39:21:3921"}
{"level":"info","message":"Request received: GET /api/service?service=qb&entity=CLASS - MessageID: 92efaf2ea0537d679f2f33938abfb6ed ","timestamp":"2025-05-27 15:39:27:3927"}
{"code":102,"errorDescription":"Invalid service: qb. Supported: qbo","level":"error","message":"GET /api/service?service=qb&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:14:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:39:27:3927"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 92efaf2ea0537d679f2f33938abfb6ed","timestamp":"2025-05-27 15:39:27:3927"}
{"level":"info","message":"Request received: GET /api/service?service=qbO&entity=CLASS - MessageID: f43c449467e9ebe99609a7476bbdb859 ","timestamp":"2025-05-27 15:39:32:3932"}
{"level":"info","message":"2025-05-27T10:09:32.886Z  GET qbO CLASS","timestamp":"2025-05-27 15:39:32:3932"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:39:32:3932"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f43c449467e9ebe99609a7476bbdb859","timestamp":"2025-05-27 15:39:34:3934"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 75fd37719b690c5655177ab5374da31e ","timestamp":"2025-05-27 15:39:40:3940"}
{"level":"info","message":"2025-05-27T10:09:40.955Z  GET QBO CLASS","timestamp":"2025-05-27 15:39:40:3940"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:39:40:3940"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 75fd37719b690c5655177ab5374da31e","timestamp":"2025-05-27 15:39:42:3942"}
{"level":"info","message":"Request received: GET /api/service?service=&entity=CLASS - MessageID: b962ad0266e5ad14f949a32fd176d767 ","timestamp":"2025-05-27 15:39:45:3945"}
{"code":102,"errorDescription":"Invalid service: . Supported: qbo","level":"error","message":"GET /api/service?service=&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:14:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:39:45:3945"}
{"level":"info","message":"Response sent with status: 400 - MessageID: b962ad0266e5ad14f949a32fd176d767","timestamp":"2025-05-27 15:39:45:3945"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: ea17015be746b938c9747f2b74a28c9c ","timestamp":"2025-05-27 15:39:51:3951"}
{"level":"info","message":"2025-05-27T10:09:51.132Z  GET QBO CLASS","timestamp":"2025-05-27 15:39:51:3951"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:39:51:3951"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ea17015be746b938c9747f2b74a28c9c","timestamp":"2025-05-27 15:39:52:3952"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 034cb10cb09b936fa0955846348f46eb ","timestamp":"2025-05-27 15:40:01:401"}
{"level":"info","message":"2025-05-27T10:10:01.673Z  GET QBO CLASS","timestamp":"2025-05-27 15:40:01:401"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:40:01:401"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:40:02:402"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 034cb10cb09b936fa0955846348f46eb","timestamp":"2025-05-27 15:40:02:402"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 0a9099b639472269d2f1d9b24452e7ea ","timestamp":"2025-05-27 15:40:06:406"}
{"level":"info","message":"2025-05-27T10:10:06.882Z  GET QBO CLASS","timestamp":"2025-05-27 15:40:06:406"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:40:06:406"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 0a9099b639472269d2f1d9b24452e7ea","timestamp":"2025-05-27 15:40:07:407"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 2a3ac7fa85e7deb68fd51e3d704cc9ee ","timestamp":"2025-05-27 15:40:11:4011"}
{"level":"info","message":"2025-05-27T10:10:11.115Z  GET QBO CLASS","timestamp":"2025-05-27 15:40:11:4011"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 93414544417370","timestamp":"2025-05-27 15:40:11:4011"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:40:11:4011"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 2a3ac7fa85e7deb68fd51e3d704cc9ee","timestamp":"2025-05-27 15:40:11:4011"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: ef7eb3d43d2c27c3c4e3a66b3e91985f ","timestamp":"2025-05-27 15:40:47:4047"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:40:47:4047"}
{"level":"info","message":"Response sent with status: 400 - MessageID: ef7eb3d43d2c27c3c4e3a66b3e91985f","timestamp":"2025-05-27 15:40:47:4047"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: c5981e1e28c0245ed1e2d3092eb2b4d3 ","timestamp":"2025-05-27 15:40:51:4051"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:40:51:4051"}
{"level":"info","message":"Response sent with status: 400 - MessageID: c5981e1e28c0245ed1e2d3092eb2b4d3","timestamp":"2025-05-27 15:40:51:4051"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 113bb631b8393ee8a4162df60a77334d ","timestamp":"2025-05-27 15:40:52:4052"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:40:52:4052"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 113bb631b8393ee8a4162df60a77334d","timestamp":"2025-05-27 15:40:52:4052"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: ad95feec93be5933932b6045570e89ad ","timestamp":"2025-05-27 15:41:01:411"}
{"level":"info","message":"2025-05-27T10:11:01.879Z  GET QBO CLASS","timestamp":"2025-05-27 15:41:01:411"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 93414544417370","timestamp":"2025-05-27 15:41:01:411"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:41:03:413"}
{"level":"info","message":"Response sent with status: 401 - MessageID: ad95feec93be5933932b6045570e89ad","timestamp":"2025-05-27 15:41:03:413"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:42:06:426"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:42:11:4211"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:42:16:4216"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 903dee9247d147f9d0765812af4109b4 ","timestamp":"2025-05-27 15:42:20:4220"}
{"level":"info","message":"2025-05-27T10:12:20.939Z  GET QBO CLASS","timestamp":"2025-05-27 15:42:20:4220"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 934145444173704544565656","timestamp":"2025-05-27 15:42:20:4220"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:42:22:4222"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 903dee9247d147f9d0765812af4109b4","timestamp":"2025-05-27 15:42:22:4222"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:42:56:4256"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: a4e75750a34057c372785ee651f9c0cd ","timestamp":"2025-05-27 15:42:59:4259"}
{"level":"info","message":"2025-05-27T10:12:59.083Z  GET QBO CLASS","timestamp":"2025-05-27 15:42:59:4259"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 934145444173704544565656","timestamp":"2025-05-27 15:42:59:4259"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:43:00:430"}
{"level":"info","message":"Response sent with status: 401 - MessageID: a4e75750a34057c372785ee651f9c0cd","timestamp":"2025-05-27 15:43:00:430"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:43:50:4350"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:43:57:4357"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:44:01:441"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:44:17:4417"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:44:21:4421"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 4726d16beaa6a64648cd898e5b405552 ","timestamp":"2025-05-27 15:44:38:4438"}
{"level":"info","message":"2025-05-27T10:14:38.626Z  GET QBO CLASS","timestamp":"2025-05-27 15:44:38:4438"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:44:38:4438"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:41:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:44:40:4440"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 4726d16beaa6a64648cd898e5b405552","timestamp":"2025-05-27 15:44:40:4440"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 86b2f7b0d28afd04fbe4f78daf567776 ","timestamp":"2025-05-27 15:45:16:4516"}
{"level":"info","message":"2025-05-27T10:15:16.862Z  GET QBO CLASS","timestamp":"2025-05-27 15:45:16:4516"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737","timestamp":"2025-05-27 15:45:16:4516"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:41:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:45:17:4517"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 86b2f7b0d28afd04fbe4f78daf567776","timestamp":"2025-05-27 15:45:17:4517"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: ef91e283b5d228f130150a2f23fd40b6 ","timestamp":"2025-05-27 15:45:42:4542"}
{"level":"info","message":"2025-05-27T10:15:42.481Z  GET QBO CLASS","timestamp":"2025-05-27 15:45:42:4542"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737","timestamp":"2025-05-27 15:45:42:4542"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:45:44:4544"}
{"level":"info","message":"Response sent with status: 401 - MessageID: ef91e283b5d228f130150a2f23fd40b6","timestamp":"2025-05-27 15:45:44:4544"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 6191d2551b059516c121cd580e75d044 ","timestamp":"2025-05-27 15:45:59:4559"}
{"level":"info","message":"2025-05-27T10:15:59.205Z  GET QBO CLASS","timestamp":"2025-05-27 15:45:59:4559"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:45:59:4559"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6191d2551b059516c121cd580e75d044","timestamp":"2025-05-27 15:46:00:460"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 7851c613b28e0a431da70516491e3333 ","timestamp":"2025-05-27 15:46:10:4610"}
{"level":"info","message":"2025-05-27T10:16:10.144Z  GET QBO CLASS","timestamp":"2025-05-27 15:46:10:4610"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:46:10:4610"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:41:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:46:11:4611"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 7851c613b28e0a431da70516491e3333","timestamp":"2025-05-27 15:46:11:4611"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 0374ce6d4956b03dbe524089735a1485 ","timestamp":"2025-05-27 15:46:16:4616"}
{"level":"info","message":"2025-05-27T10:16:16.329Z  GET QBO CLASS","timestamp":"2025-05-27 15:46:16:4616"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737","timestamp":"2025-05-27 15:46:16:4616"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:41:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:46:16:4616"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 0374ce6d4956b03dbe524089735a1485","timestamp":"2025-05-27 15:46:16:4616"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 508927037c0f9faa700631244c70151b ","timestamp":"2025-05-27 15:46:23:4623"}
{"level":"info","message":"2025-05-27T10:16:23.994Z  GET QBO CLASS","timestamp":"2025-05-27 15:46:23:4623"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737","timestamp":"2025-05-27 15:46:23:4623"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:46:25:4625"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 508927037c0f9faa700631244c70151b","timestamp":"2025-05-27 15:46:25:4625"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: ce6bd3edcb38f1815693430f06c04d9f ","timestamp":"2025-05-27 15:47:00:470"}
{"level":"info","message":"2025-05-27T10:17:00.659Z  GET QBO CLASS","timestamp":"2025-05-27 15:47:00:470"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:47:00:470"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ce6bd3edcb38f1815693430f06c04d9f","timestamp":"2025-05-27 15:47:02:472"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:47:22:4722"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:47:27:4727"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:47:40:4740"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: b6c75b25b7baa7374ae7fb105833f2aa ","timestamp":"2025-05-27 15:47:51:4751"}
{"level":"info","message":"2025-05-27T10:17:51.558Z  GET QBO CLASS","timestamp":"2025-05-27 15:47:51:4751"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737","timestamp":"2025-05-27 15:47:51:4751"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:47:52:4752"}
{"level":"info","message":"Response sent with status: 401 - MessageID: b6c75b25b7baa7374ae7fb105833f2aa","timestamp":"2025-05-27 15:47:52:4752"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:48:26:4826"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:49:03:493"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:49:41:4941"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:50:16:5016"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:50:20:5020"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:50:47:5047"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:50:51:5051"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:50:58:5058"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:51:05:515"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:51:09:519"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:51:20:5120"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:52:28:5228"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:52:42:5242"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:52:49:5249"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:52:52:5252"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 410208e854f40056913f4838836e6cb2 ","timestamp":"2025-05-27 15:52:56:5256"}
{"level":"info","message":"2025-05-27T10:22:56.479Z  GET QBO CLASS","timestamp":"2025-05-27 15:52:56:5256"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737","timestamp":"2025-05-27 15:52:56:5256"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:46:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:52:58:5258"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 410208e854f40056913f4838836e6cb2","timestamp":"2025-05-27 15:52:58:5258"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 3bdb92ea8e0763a9752641852cc268db ","timestamp":"2025-05-27 15:53:21:5321"}
{"level":"info","message":"2025-05-27T10:23:21.476Z  GET QBO CLASS","timestamp":"2025-05-27 15:53:21:5321"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:53:21:5321"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:46:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:53:23:5323"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 3bdb92ea8e0763a9752641852cc268db","timestamp":"2025-05-27 15:53:23:5323"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 15:53:45:5345"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 1fc476716cb96e9ede0f1c63e3824cd9 ","timestamp":"2025-05-27 15:55:02:552"}
{"level":"info","message":"2025-05-27T10:25:02.342Z  GET QBO CLASS","timestamp":"2025-05-27 15:55:02:552"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 15:55:02:552"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:55:04:554"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 1fc476716cb96e9ede0f1c63e3824cd9","timestamp":"2025-05-27 15:55:04:554"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 3e1bc731e8f53ac999ea82c51d902baf ","timestamp":"2025-05-27 16:01:20:120"}
{"level":"info","message":"2025-05-27T10:31:20.870Z  GET QBO CLASS","timestamp":"2025-05-27 16:01:20:120"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 16:01:20:120"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 3e1bc731e8f53ac999ea82c51d902baf","timestamp":"2025-05-27 16:01:23:123"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 17:59:04:594"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 85438d38ec0acf7d3523483ffce9973b ","timestamp":"2025-05-27 17:59:26:5926"}
{"level":"info","message":"2025-05-27T12:29:26.292Z  GET QBO CLASS","timestamp":"2025-05-27 17:59:26:5926"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033","timestamp":"2025-05-27 17:59:26:5926"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 85438d38ec0acf7d3523483ffce9973b","timestamp":"2025-05-27 17:59:28:5928"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 18867378f0c4a3841ae0466d99f4c742 ","timestamp":"2025-05-27 17:59:32:5932"}
{"level":"info","message":"2025-05-27T12:29:32.076Z  GET QBO CLASS","timestamp":"2025-05-27 17:59:32:5932"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: 9341454441737033sdsdsdsd","timestamp":"2025-05-27 17:59:32:5932"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:59:32:5932"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 18867378f0c4a3841ae0466d99f4c742","timestamp":"2025-05-27 17:59:32:5932"}
{"level":"info","message":"Request received: GET /api/service?service=QBO&entity=CLASS - MessageID: 603d0f7b1e854037b49080b7922842c2 ","timestamp":"2025-05-27 17:59:37:5937"}
{"level":"info","message":"2025-05-27T12:29:37.779Z  GET QBO CLASS","timestamp":"2025-05-27 17:59:37:5937"}
{"level":"info","message":"QBO GET request for entity: CLASS | companyId: sdasdasdasdsdasdasdsd","timestamp":"2025-05-27 17:59:37:5937"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:59:39:5939"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 603d0f7b1e854037b49080b7922842c2","timestamp":"2025-05-27 17:59:39:5939"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 18:01:18:118"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-05-27 18:02:19:219"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:33:07:337"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:33:35:3335"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:34:07:347"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:35:27:3527"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:35:30:3530"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class - MessageID: 8878a175f19d54839846433236acc0a0 ","timestamp":"2025-06-02 17:47:50:4750"}
{"level":"info","message":"2025-06-02T12:17:50.962Z  GET qbo class","timestamp":"2025-06-02 17:47:50:4750"}
{"level":"info","message":"QBO GET request | entity: class | companyId: 4620816365356807410","timestamp":"2025-06-02 17:47:50:4750"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8878a175f19d54839846433236acc0a0","timestamp":"2025-06-02 17:47:54:4754"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:49:47:4947"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class - MessageID: b6c7413ffcdf6d9b0b615085f6831095 ","timestamp":"2025-06-02 17:49:49:4949"}
{"level":"info","message":"2025-06-02T12:19:49.694Z  GET qbo class","timestamp":"2025-06-02 17:49:49:4949"}
{"level":"info","message":"QBO GET request | entity: class | companyId: 4620816365356807410","timestamp":"2025-06-02 17:49:49:4949"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b6c7413ffcdf6d9b0b615085f6831095","timestamp":"2025-06-02 17:49:52:4952"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:50:26:5026"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class - MessageID: f7cd4c95fbf1c770095cfbb4a6dc5780 ","timestamp":"2025-06-02 17:50:29:5029"}
{"level":"info","message":"2025-06-02T12:20:29.251Z  GET qbo class","timestamp":"2025-06-02 17:50:29:5029"}
{"level":"info","message":"QBO GET request | entity: class | companyId: 4620816365356807410","timestamp":"2025-06-02 17:50:29:5029"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f7cd4c95fbf1c770095cfbb4a6dc5780","timestamp":"2025-06-02 17:50:32:5032"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:50:50:5050"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:50:55:5055"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class - MessageID: 980128bddb26e5128dc443d4fe52eb0d ","timestamp":"2025-06-02 17:50:56:5056"}
{"level":"info","message":"2025-06-02T12:20:56.207Z  GET qbo class","timestamp":"2025-06-02 17:50:56:5056"}
{"level":"info","message":"QBO GET request | entity: class | companyId: 4620816365356807410","timestamp":"2025-06-02 17:50:56:5056"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 980128bddb26e5128dc443d4fe52eb0d","timestamp":"2025-06-02 17:50:59:5059"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:51:19:5119"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:51:23:5123"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class - MessageID: dbc664a8aa2d033cc019e17d5385c128 ","timestamp":"2025-06-02 17:51:28:5128"}
{"level":"info","message":"2025-06-02T12:21:28.374Z  GET qbo class","timestamp":"2025-06-02 17:51:28:5128"}
{"level":"info","message":"QBO GET request | entity: class | companyId: 4620816365356807410","timestamp":"2025-06-02 17:51:28:5128"}
{"code":105,"errorDescription":"QueryValidationError: Property createdDate not found for Entity Class","level":"error","message":"GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class: Invalid query","stack":"Error: Invalid query\n    at D:\\Zact_New_Arch\\class-service\\app\\services\\qboApiServices\\index.ts:107:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 17:51:31:5131"}
{"level":"info","message":"Response sent with status: 400 - MessageID: dbc664a8aa2d033cc019e17d5385c128","timestamp":"2025-06-02 17:51:31:5131"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:52:11:5211"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class - MessageID: 6019fc1f7d4403cadfe152d68b48ebb8 ","timestamp":"2025-06-02 17:52:13:5213"}
{"level":"info","message":"2025-06-02T12:22:13.924Z  GET qbo class","timestamp":"2025-06-02 17:52:13:5213"}
{"level":"info","message":"QBO GET request | entity: class | companyId: 4620816365356807410","timestamp":"2025-06-02 17:52:13:5213"}
{"code":105,"errorDescription":"QueryValidationError: Property createdDate not found for Entity Class","level":"error","message":"GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class: Invalid query","stack":"Error: Invalid query\n    at D:\\Zact_New_Arch\\class-service\\app\\services\\qboApiServices\\index.ts:107:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 17:52:16:5216"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 6019fc1f7d4403cadfe152d68b48ebb8","timestamp":"2025-06-02 17:52:16:5216"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 17:52:41:5241"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class - MessageID: b3157a2606091a99bcdb2a4929e9cf11 ","timestamp":"2025-06-02 17:52:42:5242"}
{"level":"info","message":"2025-06-02T12:22:42.067Z  GET qbo class","timestamp":"2025-06-02 17:52:42:5242"}
{"level":"info","message":"QBO GET request | entity: class | companyId: 4620816365356807410","timestamp":"2025-06-02 17:52:42:5242"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b3157a2606091a99bcdb2a4929e9cf11","timestamp":"2025-06-02 17:52:44:5244"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2025-08-12&entity=class - MessageID: 266dab1205b51eeed8ad84fcf149e2e2 ","timestamp":"2025-06-02 17:52:53:5253"}
{"level":"info","message":"2025-06-02T12:22:53.662Z  GET qbo class","timestamp":"2025-06-02 17:52:53:5253"}
{"level":"info","message":"QBO GET request | entity: class | companyId: 4620816365356807410","timestamp":"2025-06-02 17:52:53:5253"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 266dab1205b51eeed8ad84fcf149e2e2","timestamp":"2025-06-02 17:52:56:5256"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 18:01:13:113"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 18:01:20:120"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 18:01:24:124"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 18:03:10:310"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 18:03:44:344"}
{"level":"info","message":"Server is listening on port 8002","timestamp":"2025-06-02 18:03:48:348"}
