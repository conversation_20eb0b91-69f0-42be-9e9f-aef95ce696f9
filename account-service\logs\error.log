{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"POST /api/service?service=qbo&entity=bill: Bad request","stack":"Error: Bad request\n    at validateAuthParams (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:40:11)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:57:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:51:52\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 11:42:34:4234"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 12:15:09:159"}
{"level":"error","message":"GET /api/services/service?service=qbo&entity=account: Path not found","stack":"Error: Path not found\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\account-service\\app\\utils\\error-handler.ts:54:22)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)","status":404,"timestamp":"2025-05-27 13:12:47:1247"}
{"level":"error","message":"GET /api/services?service=qbo&entity=account: Path not found","stack":"Error: Path not found\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\account-service\\app\\utils\\error-handler.ts:54:22)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)","status":404,"timestamp":"2025-05-27 13:13:14:1314"}
{"level":"error","message":"GET /api/services/service?service=qbo&entity=account: Path not found","stack":"Error: Path not found\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\account-service\\app\\utils\\error-handler.ts:54:22)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)","status":404,"timestamp":"2025-05-27 13:13:24:1324"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 13:14:18:1418"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=qbo&entity=account: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:34:39:3439"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:35:21:3521"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:35:39:3539"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=acc: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:36:26:3626"}
{"code":102,"errorDescription":"Invalid service: q. Supported: qbo","level":"error","message":"GET /api/service?service=q&entity=account: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:14:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:36:38:3638"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:40:47:4047"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:41:41:4141"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:55:55:5555"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:56:08:568"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:56:13:5613"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:56:21:5621"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:56:46:5646"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:57:06:576"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:10:21:1021"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:11:26:1126"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:16:44:1644"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:51:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:21:15:2115"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:22:01:221"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:53:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:25:53:2553"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:03:36:336"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:05:51:551"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:06:00:60"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 17:10:09:109"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:14:41:1441"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:17:57:1757"}
{"code":102,"errorDescription":"Invalid service: q. Supported: qbo","level":"error","message":"GET /api/service?service=q&entity=account: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:14:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 17:20:12:2012"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=acc: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 17:22:18:2218"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:30:44:3044"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:37:17:3717"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:38:09:389"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:39:11:3911"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:55:39:5539"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:56:16:5616"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:56:25:5625"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:57:12:5712"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:58:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 18:05:13:513"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:234:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-30 18:29:03:293"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:234:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-30 18:35:03:353"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator==: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:210:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-30 19:44:03:443"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator==: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:210:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 11:23:14:2314"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 11:25:26:2526"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 11:27:02:272"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"filter":{"fieldName":"Active","fieldValue":"false","fieldValue2":"true","operator":"="},"minorversion":"75","query":"SELECT * FROM account MAXRESULTS 1000"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/************6807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&fieldValue2=true&operator==: Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","pathname":"/v3/company/************6807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 02 Jun 2025 06:02:31 GMT","Content-Type","text/html;charset=utf-8","Content-Length","435","Connection","keep-alive","x-spanid","e1c2ba5c-2c04-6a97-38c6-08c532f464e7","x-amzn-trace-id","Root=1-683d3e76-6143511a49f1b7a15c7349f6","X-Content-Type-Options","nosniff","Content-Language","en","x-envoy-upstream-service-time","16","Server","istio-envoy","x-envoy-decorator-operation","v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","Strict-Transport-Security","max-age=********","intuit_tid","1-683d3e76-6143511a49f1b7a15c7349f6","x-request-id","1-683d3e76-6143511a49f1b7a15c7349f6"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"filter":{"fieldName":"Active","fieldValue":"false","fieldValue2":"true","operator":"="},"minorversion":"75","query":"SELECT * FROM account MAXRESULTS 1000"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/************6807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":"<!doctype html><html lang=\"en\"><head><title>HTTP Status 400 – Bad Request</title><style type=\"text/css\">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 400 – Bad Request</h1></body></html>","headers":{"connection":"keep-alive","content-language":"en","content-length":"435","content-type":"text/html;charset=utf-8","date":"Mon, 02 Jun 2025 06:02:31 GMT","intuit_tid":"1-683d3e76-6143511a49f1b7a15c7349f6","server":"istio-envoy","strict-transport-security":"max-age=********","x-amzn-trace-id":"Root=1-683d3e76-6143511a49f1b7a15c7349f6","x-content-type-options":"nosniff","x-envoy-decorator-operation":"v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","x-envoy-upstream-service-time":"16","x-request-id":"1-683d3e76-6143511a49f1b7a15c7349f6","x-spanid":"e1c2ba5c-2c04-6a97-38c6-08c532f464e7"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","pathname":"/v3/company/************6807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 02 Jun 2025 06:02:31 GMT","Content-Type","text/html;charset=utf-8","Content-Length","435","Connection","keep-alive","x-spanid","e1c2ba5c-2c04-6a97-38c6-08c532f464e7","x-amzn-trace-id","Root=1-683d3e76-6143511a49f1b7a15c7349f6","X-Content-Type-Options","nosniff","Content-Language","en","x-envoy-upstream-service-time","16","Server","istio-envoy","x-envoy-decorator-operation","v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","Strict-Transport-Security","max-age=********","intuit_tid","1-683d3e76-6143511a49f1b7a15c7349f6","x-request-id","1-683d3e76-6143511a49f1b7a15c7349f6"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"stack":"AxiosError: Request failed with status code 400\n    at settle (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:599:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 11:32:31:3231"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"filter":{"fieldName":"Active","fieldValue":"false","fieldValue2":"true","operator":"="},"minorversion":"75","query":"SELECT * FROM account WHERE Active = false MAXRESULTS 1000"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/************6807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&fieldValue2=true&operator==: Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","pathname":"/v3/company/************6807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 02 Jun 2025 06:04:13 GMT","Content-Type","text/html;charset=utf-8","Content-Length","435","Connection","keep-alive","x-spanid","7ec0560e-e8dc-dfd2-55fc-3c55f1ef0904","x-amzn-trace-id","Root=1-683d3edd-42384e556336fa923a46ca4e","X-Content-Type-Options","nosniff","Content-Language","en","x-envoy-upstream-service-time","60","Server","istio-envoy","x-envoy-decorator-operation","v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","Strict-Transport-Security","max-age=********","intuit_tid","1-683d3edd-42384e556336fa923a46ca4e","x-request-id","1-683d3edd-42384e556336fa923a46ca4e"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"filter":{"fieldName":"Active","fieldValue":"false","fieldValue2":"true","operator":"="},"minorversion":"75","query":"SELECT * FROM account WHERE Active = false MAXRESULTS 1000"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/************6807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":"<!doctype html><html lang=\"en\"><head><title>HTTP Status 400 – Bad Request</title><style type=\"text/css\">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 400 – Bad Request</h1></body></html>","headers":{"connection":"keep-alive","content-language":"en","content-length":"435","content-type":"text/html;charset=utf-8","date":"Mon, 02 Jun 2025 06:04:13 GMT","intuit_tid":"1-683d3edd-42384e556336fa923a46ca4e","server":"istio-envoy","strict-transport-security":"max-age=********","x-amzn-trace-id":"Root=1-683d3edd-42384e556336fa923a46ca4e","x-content-type-options":"nosniff","x-envoy-decorator-operation":"v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","x-envoy-upstream-service-time":"60","x-request-id":"1-683d3edd-42384e556336fa923a46ca4e","x-spanid":"7ec0560e-e8dc-dfd2-55fc-3c55f1ef0904"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","pathname":"/v3/company/************6807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 02 Jun 2025 06:04:13 GMT","Content-Type","text/html;charset=utf-8","Content-Length","435","Connection","keep-alive","x-spanid","7ec0560e-e8dc-dfd2-55fc-3c55f1ef0904","x-amzn-trace-id","Root=1-683d3edd-42384e556336fa923a46ca4e","X-Content-Type-Options","nosniff","Content-Language","en","x-envoy-upstream-service-time","60","Server","istio-envoy","x-envoy-decorator-operation","v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","Strict-Transport-Security","max-age=********","intuit_tid","1-683d3edd-42384e556336fa923a46ca4e","x-request-id","1-683d3edd-42384e556336fa923a46ca4e"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"stack":"AxiosError: Request failed with status code 400\n    at settle (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:599:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 11:34:13:3413"}
{"code":105,"errorDescription":"QueryParserError: Encountered \"between\" \"between\" at line 1, column 49","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&operator=between: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:220:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 12:11:03:113"}
{"code":102,"errorDescription":"Operator 'between' requires a second value (fieldValue2)","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&operator=between: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:138:15)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:319:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:20:51:2051"}
{"code":105,"errorDescription":"QueryValidationError: value 2023-09-31 is not valid for property 'MetaData.CreateTime'","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-31&fieldValue2=2023-08-11&operator=between: Invalid query","stack":"Error: Invalid query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:287:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 12:21:11:2111"}
{"code":102,"errorDescription":"Operator 'between' requires a second value (fieldValue2)","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&operator=between: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:143:15)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:326:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:25:48:2548"}
{"code":102,"errorDescription":"Invalid operator '!!' provided. Allowed operators are: >, <, =, >=, <=, LIKE","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!!: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:89:13)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:326:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:25:58:2558"}
{"code":102,"errorDescription":"Invalid operator '!!' provided. Allowed operators are: >, <, =, >=, <=, LIKE, Between","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!!: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:89:13)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:326:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:26:24:2624"}
{"code":102,"errorDescription":"Invalid operator '!!' provided. Allowed operators are: >, <, =, >=, <=, LIKE, between","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!!: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:89:13)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:326:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:26:46:2646"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=between: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:281:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 12:29:41:2941"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator==: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:266:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 13:32:52:3252"}
{"code":102,"errorDescription":"Invalid operator 'LIKE ' provided. Allowed operators are: >, <, =, >=, <=, LIKE, between","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46&operator=LIKE%20: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:82:13)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:311:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:219:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 13:53:57:5357"}
{"code":105,"errorDescription":"QueryValidationError: value 2023-08-11T23:58 is not valid for property 'MetaData.CreateTime'","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58&operator==: Invalid query","stack":"Error: Invalid query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:320:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:08:16:816"}
{"code":102,"errorDescription":"Operator 'between' requires a second value (fieldValue2)","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T06:58:43Z&operator=between: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:136:15)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:311:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:216:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 14:34:30:3430"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T06:58:43Z&operator==: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:266:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 14:34:40:3440"}
{"code":"ERR_UNESCAPED_CHARACTERS","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Request path contains unescaped characters","stack":"TypeError: Request path contains unescaped characters\n    at new ClientRequest (node:_http_client:185:13)\n    at Object.request (node:https:381:10)\n    at RedirectableRequest._performRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:337:24)\n    at new RedirectableRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:111:8)\n    at Object.request (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:543:14)\n    at dispatchHttpRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:464:21)\n    at D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:152:5\n    at new Promise (<anonymous>)\n    at wrapAsync (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:132:10)\n    at http (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:170:10)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-06-02 14:52:24:5224"}
{"code":"ERR_UNESCAPED_CHARACTERS","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Request path contains unescaped characters","stack":"TypeError: Request path contains unescaped characters\n    at new ClientRequest (node:_http_client:185:13)\n    at Object.request (node:https:381:10)\n    at RedirectableRequest._performRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:337:24)\n    at new RedirectableRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:111:8)\n    at Object.request (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:543:14)\n    at dispatchHttpRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:464:21)\n    at D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:152:5\n    at new Promise (<anonymous>)\n    at wrapAsync (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:132:10)\n    at http (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:170:10)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-06-02 14:53:17:5317"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <IDENTIFIER> \"FROMaccountWHEREMetaData \"\" at line 1, column 8.\nWas expecting one of:\n    <EOF> \n    \",\" ...\n    \"from\" ...\n    \"iterator\" ...\n    \"maxresults\" ...\n    \"order\" ...\n    \"orderby\" ...\n    \"startposition\" ...\n    \"where\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:55:01:551"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <IDENTIFIER> \"FROMaccountWHEREMetaData \"\" at line 1, column 8.\nWas expecting one of:\n    <EOF> \n    \",\" ...\n    \"from\" ...\n    \"iterator\" ...\n    \"maxresults\" ...\n    \"order\" ...\n    \"orderby\" ...\n    \"startposition\" ...\n    \"where\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:55:25:5525"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:55:47:5547"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:56:10:5610"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" \"=\" \"= \"\" at line 1, column 1.\nWas expecting:\n    \"select\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:56:50:5650"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" \"=\" \"= \"\" at line 1, column 1.\nWas expecting:\n    \"select\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:59:35:5935"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:01:11:111"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:01:31:131"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:02:08:28"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <INTEGER> \"2023 \"\" at line 1, column 51.\nWas expecting one of:\n    \"false\" ...\n    \"true\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:03:05:35"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 74.  Encountered: \" \" (32), after : \"&\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:04:02:42"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 74.  Encountered: \" \" (32), after : \"&\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:04:21:421"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:05:47:547"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:07:25:725"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <INTEGER> \"2023 \"\" at line 1, column 51.\nWas expecting one of:\n    \"false\" ...\n    \"true\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:07:40:740"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <INTEGER> \"2023 \"\" at line 1, column 51.\nWas expecting one of:\n    \"false\" ...\n    \"true\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:08:07:87"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:280:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:24:34:2434"}
{"code":102,"errorDescription":"startDate is required when dateField is provided","level":"error","message":"GET /api/service?service=qbo&entity=account&dateField=createdDate&fieldValue=2023-07-31: Bad request","stack":"Error: Bad request\n    at validateDateParams (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:50:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:93:7\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-02 17:18:11:1811"}
{"code":"ERR_UNESCAPED_CHARACTERS","level":"error","message":"GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-07-31: Request path contains unescaped characters","stack":"TypeError: Request path contains unescaped characters\n    at new ClientRequest (node:_http_client:185:13)\n    at Object.request (node:https:381:10)\n    at RedirectableRequest._performRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:337:24)\n    at new RedirectableRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:111:8)\n    at Object.request (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:543:14)\n    at dispatchHttpRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:464:21)\n    at D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:152:5\n    at new Promise (<anonymous>)\n    at wrapAsync (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:132:10)\n    at http (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:170:10)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-06-02 17:18:22:1822"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-07-31: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:99:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 17:19:04:194"}
{"level":"error","message":"POST /api/service?service=qbo&entity=vendor: Path not found","stack":"Error: Path not found\n    at notFoundHandler (D:\\Zact_New_Arch\\account-service\\app\\utils\\error-handler.ts:67:22)\n    at Layer.handleRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:342:13)\n    at D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:297:9\n    at processParams (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:582:12)\n    at next (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:291:5)\n    at Function.handle (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:186:3)\n    at router (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:342:13)","status":404,"timestamp":"2025-06-03 13:21:00:210"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 13:21:08:218"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:84:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 13:21:21:2121"}
{"cause":{"code":"ECONNRESET","errno":-4077,"syscall":"read"},"code":"ECONNRESET","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..h9g10dQBotDUXJ10WFiwqQ.wVb4kqNNMCyXTIwQfcWHwQouEq_Ksg1nwFCgy87MjlaINjwQx7DYN2ebJbZFdAkCHlZl7xn_X8xiF_1JQBNhjjcdRF_cI9Cxjo-yaFuPtgLEH2W0HbBisFtnKy5zvhissFVxOzyZCek-rygYpnzw2kiQHvOnOGGx59lhlOQ9mHzbWsH-hAgLmQi97HtMZ4ZpTUwM32L1in3m30ZgGt9Wde6jSc_TizwvzuTZ-JOQtJf42XMtifxv68jV2AUsCBhbF1RHJr2aI5wmll_YEv5zOJkhqxXsEK7MUDqwXbeNT8CBtoKJNMQtTBQvf2kKjDZxqegesI3xAZUTrSuSnO-dv1MlTPjud_qN8fT_AKRpQiLwlvC7n215w6ygYh0RHvBWyeobwzMvYoEEPTXx5dgTlOBnqjJf7WQ4cDMnttxoXfjDQg3cWJYyFfnfgq3aXPxaIaqIUrKZ1CZTCl1MoJA2pmzUmBEjasrZP3cX65T7ZtI.VbT6vLGvgTLgGGr-erxbiw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"minorversion":"75","query":"SELECT * FROM account"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/************6807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errno":-4077,"level":"error","message":"GET /api/service?service=qbo&entity=account: read ECONNRESET","name":"Error","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/************6807410/query?query=SELECT+*+FROM+account&minorversion=75 HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..h9g10dQBotDUXJ10WFiwqQ.wVb4kqNNMCyXTIwQfcWHwQouEq_Ksg1nwFCgy87MjlaINjwQx7DYN2ebJbZFdAkCHlZl7xn_X8xiF_1JQBNhjjcdRF_cI9Cxjo-yaFuPtgLEH2W0HbBisFtnKy5zvhissFVxOzyZCek-rygYpnzw2kiQHvOnOGGx59lhlOQ9mHzbWsH-hAgLmQi97HtMZ4ZpTUwM32L1in3m30ZgGt9Wde6jSc_TizwvzuTZ-JOQtJf42XMtifxv68jV2AUsCBhbF1RHJr2aI5wmll_YEv5zOJkhqxXsEK7MUDqwXbeNT8CBtoKJNMQtTBQvf2kKjDZxqegesI3xAZUTrSuSnO-dv1MlTPjud_qN8fT_AKRpQiLwlvC7n215w6ygYh0RHvBWyeobwzMvYoEEPTXx5dgTlOBnqjJf7WQ4cDMnttxoXfjDQg3cWJYyFfnfgq3aXPxaIaqIUrKZ1CZTCl1MoJA2pmzUmBEjasrZP3cX65T7ZtI.VbT6vLGvgTLgGGr-erxbiw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null],"end":[null,null],"newListener":[null,null]},"_eventsCount":10,"_hadError":true,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":"[Circular]","_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":939,"pendingcb":1,"writelen":939},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["*************:443","************:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":5000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account&minorversion=75","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/************6807410/query?query=SELECT+*+FROM+account&minorversion=75","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..h9g10dQBotDUXJ10WFiwqQ.wVb4kqNNMCyXTIwQfcWHwQouEq_Ksg1nwFCgy87MjlaINjwQx7DYN2ebJbZFdAkCHlZl7xn_X8xiF_1JQBNhjjcdRF_cI9Cxjo-yaFuPtgLEH2W0HbBisFtnKy5zvhissFVxOzyZCek-rygYpnzw2kiQHvOnOGGx59lhlOQ9mHzbWsH-hAgLmQi97HtMZ4ZpTUwM32L1in3m30ZgGt9Wde6jSc_TizwvzuTZ-JOQtJf42XMtifxv68jV2AUsCBhbF1RHJr2aI5wmll_YEv5zOJkhqxXsEK7MUDqwXbeNT8CBtoKJNMQtTBQvf2kKjDZxqegesI3xAZUTrSuSnO-dv1MlTPjud_qN8fT_AKRpQiLwlvC7n215w6ygYh0RHvBWyeobwzMvYoEEPTXx5dgTlOBnqjJf7WQ4cDMnttxoXfjDQg3cWJYyFfnfgq3aXPxaIaqIUrKZ1CZTCl1MoJA2pmzUmBEjasrZP3cX65T7ZtI.VbT6vLGvgTLgGGr-erxbiw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null],"end":[null,null],"newListener":[null,null]},"_eventsCount":10,"_hadError":true,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/************6807410/query?query=SELECT+*+FROM+account&minorversion=75 HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..h9g10dQBotDUXJ10WFiwqQ.wVb4kqNNMCyXTIwQfcWHwQouEq_Ksg1nwFCgy87MjlaINjwQx7DYN2ebJbZFdAkCHlZl7xn_X8xiF_1JQBNhjjcdRF_cI9Cxjo-yaFuPtgLEH2W0HbBisFtnKy5zvhissFVxOzyZCek-rygYpnzw2kiQHvOnOGGx59lhlOQ9mHzbWsH-hAgLmQi97HtMZ4ZpTUwM32L1in3m30ZgGt9Wde6jSc_TizwvzuTZ-JOQtJf42XMtifxv68jV2AUsCBhbF1RHJr2aI5wmll_YEv5zOJkhqxXsEK7MUDqwXbeNT8CBtoKJNMQtTBQvf2kKjDZxqegesI3xAZUTrSuSnO-dv1MlTPjud_qN8fT_AKRpQiLwlvC7n215w6ygYh0RHvBWyeobwzMvYoEEPTXx5dgTlOBnqjJf7WQ4cDMnttxoXfjDQg3cWJYyFfnfgq3aXPxaIaqIUrKZ1CZTCl1MoJA2pmzUmBEjasrZP3cX65T7ZtI.VbT6vLGvgTLgGGr-erxbiw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account&minorversion=75","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":939,"pendingcb":1,"writelen":939},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["*************:443","************:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":5000}]},"totalSocketCount":1}}},"path":"/v3/company/************6807410/query?query=SELECT+*+FROM+account&minorversion=75","pathname":"/v3/company/************6807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account&minorversion=75"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"stack":"Error: read ECONNRESET\n    at Function.AxiosError.from (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:620:25)\n    at RedirectableRequest.emit (node:events:518:28)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at ClientRequest.eventHandlers.<computed> (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at ClientRequest.emit (node:domain:489:12)\n    at emitErrorEvent (node:_http_client:104:11)\n    at TLSSocket.socketErrorListener (node:_http_client:518:5)\n    at TLSSocket.emit (node:events:518:28)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"syscall":"read","timestamp":"2025-06-03 13:44:32:4432"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=accoun: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 14:25:43:2543"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:84:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 14:38:24:3824"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:84:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 14:38:57:3857"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=accoun: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 14:43:42:4342"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=vendor&totalCount=true: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 18:42:04:424"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=vendor&totalCount=true: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 18:42:14:4214"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&totalCount=true&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:87:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 18:42:24:4224"}
{"code":102,"errorDescription":"dateField is required when startDate is provided","level":"error","message":"GET /api/service?service=qbo&startDate=2025-04-11&endDate=2025-08-12&totalCount=true&entity=account: Bad request","stack":"Error: Bad request\n    at validateDateParams (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:57:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:98:7\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 18:44:47:4447"}
