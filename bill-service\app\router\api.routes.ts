import express from "express";
import { processServiceRequest } from "../adapters";
import { RequestExtended } from "../interfaces/globalInterface";
import ApiException from "../utils/api-exception";
import asyncHandler from "../utils/async-handler";
import { errorHandler, notFoundHandler } from "../utils/error-handler";
import { ErrorCodes } from "../utils/response";

const router = express.Router();

// === Validation Helpers ===
const validateServiceAndEntity = (service?: string, entity?: string) => {
  if (!service || !["qbo"].includes(service.toLowerCase())) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: `Invalid service: ${service}. Supported: qbo`,
    });
  }

  if (!entity) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "Entity is required",
    });
  }

  if (!["bill", "BILL"].includes(entity)) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "Invalid entity. Allowed: 'bill' or 'BILL'",
    });
  }
};

const validateAuthParams = (
  accessToken?: string | string[],
  companyId?: string | string[]
) => {
  if (!accessToken || !companyId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription:
        "Missing required authentication: accessToken and companyId",
    });
  }
};

const validateDateParams = (dateField?: string, startDate?: string) => {
  if (dateField && !startDate) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "startDate is required when dateField is provided",
    });
  }

  if (startDate && !dateField) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "dateField is required when startDate is provided",
    });
  }

  if (startDate && isNaN(Date.parse(startDate))) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription:
        "Invalid startDate format. Use ISO date format (YYYY-MM-DD)",
    });
  }
};

// === POST ===
router.post(
  "/service",
  asyncHandler(async (req: RequestExtended, res) => {
    const { data } = req.body;
    const { service, entity, ...queryParams } = req.query;
    const accessToken = req.headers["accesstoken"];
    const companyId = req.headers["companyid"];

    validateServiceAndEntity(service as string, entity as string);
    validateAuthParams(accessToken as string, companyId as string);

    if (!data) {
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: "POST body must include 'data'",
      });
    }

    req.log?.(`POST ${service} ${entity}`);

    const result = await processServiceRequest({
      service: service as string,
      entity: entity as string,
      requestType: "post",
      accessToken: accessToken as string,
      companyId: companyId as string,
      bodyData: data,
      queryParams,
    });

    return result;
  })
);

// === GET ===
router.get(
  "/service",
  asyncHandler(async (req: RequestExtended, res) => {
    const { service, entity, totalCount, ...queryParamsRaw } = req.query; // Destructure totalCount
    const accessToken = req.headers["accesstoken"];
    const companyId = req.headers["companyid"];

    validateServiceAndEntity(service as string, entity as string);
    validateAuthParams(accessToken as string, companyId as string);

    req.log?.(`GET ${service} ${entity}`);

    // Clone queryParamsRaw for safe mutation
    const queryParams: Record<string, any> = { ...queryParamsRaw };

    // Add totalCount to queryParams if present in request
    if (totalCount !== undefined) {
      queryParams.totalCount = totalCount === "true"; // Convert string 'true' to boolean true
    }

    // === Build date filter object ===
    const { dateField, startDate, endDate } = queryParams;

    // Validate date parameters if provided
    if (dateField || startDate) {
      validateDateParams(dateField as string, startDate as string);

      if (endDate && isNaN(Date.parse(endDate as string))) {
        throw new ApiException({
          ...ErrorCodes.BAD_REQUEST,
          errorDescription:
            "Invalid endDate format. Use ISO date format (YYYY-MM-DD)",
        });
      }
    }

    // Create dateFilter object if date parameters are provided
    if (dateField && startDate) {
      const dateFilter = {
        fieldName: dateField as string,
        startDate: startDate as string,
        ...(endDate && { endDate: endDate as string }),
      };

      // Add dateFilter to queryParams
      queryParams.dateFilter = dateFilter;
    }

    // === Clean up queryParams ===
    delete queryParams.dateField;
    delete queryParams.startDate;
    delete queryParams.endDate;
    // totalCount is already handled and placed in queryParams if it was in queryParamsRaw

    // === Final forwarding ===
    const result = await processServiceRequest({
      service: service as string,
      entity: entity as string,
      requestType: "get",
      accessToken: accessToken as string,
      companyId: companyId as string,
      queryParams,
    });

    return res.json(result);
  })
);

// === Error Handling Middleware ===
router.use(notFoundHandler);
router.use(errorHandler);

export default router;
