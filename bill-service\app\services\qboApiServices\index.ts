import { createApiClient, ApiService } from "../api.service";
import { logger } from "../../utils/logger";
import ApiException from "../../utils/api-exception";
import { ErrorCodes } from "../../utils/response";

export interface QboApiService extends ApiService {
  get(entity: string, options: QboRequestOptions): Promise<any>;
  post(entity: string, options: QboPostRequestOptions): Promise<any>;
}

export interface DateFilterOptions {
  fieldName: string;
  startDate: string;
  endDate?: string;
}

interface QboRequestOptions {
  accessToken: string;
  companyId: string;
  queryParams?: Record<string, any>; // Ensure this can hold dateFilter and totalCount
}

interface QboPostRequestOptions extends QboRequestOptions {
  bodyData: any;
}

export const createQboService = (): QboApiService => {
  const QBO_API_BASE_URL =
    process.env.QBO_API_URL ||
    "https://sandbox-quickbooks.api.intuit.com/v3/company";
  const defaultMinorVersion = process.env.QBO_MINOR_VERSION || "75";

  const getQboAuthHeaders = (accessToken: string): Record<string, string> => ({
    Authorization: `Bearer ${accessToken}`,
    Accept: "application/json",
    "Content-Type": "application/json",
  });

  // Helper: Build date query
  const buildDateQuery = (
    entity: string,
    dateFilter?: DateFilterOptions,
    getTotalCount: boolean = false // Added parameter for total count
  ): string => {
    let query = getTotalCount
      ? `SELECT COUNT(*) FROM ${entity}`
      : `SELECT * FROM ${entity}`; // Modified query based on getTotalCount

    if (dateFilter?.fieldName && dateFilter?.startDate) {
      let fieldName = dateFilter.fieldName;

      // Field transformation for known types
      if (entity.toLowerCase() === "bill") {
        if (fieldName === "createdDate") fieldName = "MetaData.CreateTime";
        if (["updatedDate", "modifiedDate"].includes(fieldName))
          fieldName = "MetaData.LastUpdatedTime";
      }

      if (isNaN(Date.parse(dateFilter.startDate))) {
        throw new ApiException(ErrorCodes.INVALID_DATE);
      }

      if (dateFilter.endDate) {
        if (isNaN(Date.parse(dateFilter.endDate))) {
          throw new ApiException(ErrorCodes.INVALID_DATE);
        }
        query += ` WHERE ${fieldName} >= '${dateFilter.startDate}' AND ${fieldName} <= '${dateFilter.endDate}'`;
      } else {
        query += ` WHERE ${fieldName} >= '${dateFilter.startDate}'`;
      }
    }

    if (!getTotalCount) {
      // Append MAXRESULTS if it's not a count query and not already part of the query
      if (!query.toUpperCase().includes("MAXRESULTS")) {
        query += " MAXRESULTS 1000";
      }
    }

    return query;
  };

  const qboClient = createApiClient(QBO_API_BASE_URL);

  qboClient.interceptors.response.use(
    (response) => response,
    (error) => {
      const resData = error.response?.data;

      const faultType =
        resData?.Fault?.type || resData?.fault?.type || resData?.Fault?.Type;

      if (
        faultType === "AUTHENTICATION" ||
        faultType === "AuthorizationFault"
      ) {
        throw new ApiException(ErrorCodes.INVALID_TOKEN);
      }

      const qboFaultError =
        resData?.Fault?.Error?.[0] || resData?.fault?.error?.[0];

      if (qboFaultError) {
        throw new ApiException({
          status: error.response.status,
          code: resData?.code || ErrorCodes.INTERNAL_ERROR.code,
          errorDescription:
            qboFaultError.detail ||
            qboFaultError.Detail ||
            qboFaultError.Message ||
            qboFaultError.message ||
            "QBO service request failed",

          message:
            qboFaultError.Message ||
            qboFaultError.message ||
            resData?.error ||
            error.message,
        });
      }

      return Promise.reject(error);
    }
  );

  const qboService: QboApiService = {
    async get(
      entity: string,
      { accessToken, companyId, queryParams = {} }: QboRequestOptions
    ) {
      logger.info(
        `QBO GET request | entity: ${entity} | companyId: ${companyId}`
      );

      const getTotalCount = queryParams.totalCount === true; // Check for totalCount

      const query = buildDateQuery(
        entity,
        queryParams.dateFilter,
        getTotalCount
      );
      logger.info(`QBO query: ${query}`); // Changed console.log to logger.info
      const url = `/${companyId}/query`;

      // Removed duplicate console.log("query: ", query);
      const response = await qboClient.get(url, {
        params: {
          query,
          minorversion: defaultMinorVersion,
        },
        headers: getQboAuthHeaders(accessToken),
      });

      if (getTotalCount) {
        const totalCount = response.data?.QueryResponse?.totalCount;
        if (typeof totalCount === "number") {
          return {
            data: { totalCount: totalCount },
            message: `Successfully fetched total count for ${entity}`,
          };
        } else {
          logger.warn(
            `Could not find totalCount in QBO response for entity: ${entity}`
          );
          return {
            data: { totalCount: 0 },
            message: `Total count not found for ${entity}`,
          };
        }
      } else {
        const entityNameInResponse = Object.keys(
          response.data?.QueryResponse || {}
        ).find((key) => key.toLowerCase() === entity.toLowerCase());
        const records = entityNameInResponse
          ? response.data?.QueryResponse?.[entityNameInResponse] || []
          : [];

        if (records.length === 0) {
          return {
            data: [],
            message: `No ${entity} records found`,
          };
        }

        return {
          data: records,
          message: `Successfully fetched ${records.length} ${entity} record(s)`,
        };
      }
    },

    async post(
      entity: string,
      {
        accessToken,
        companyId,
        bodyData,
        queryParams = {},
      }: QboPostRequestOptions
    ) {
      logger.info(
        `QBO POST request for entity: ${entity} | companyId: ${companyId}`
      );

      const url = `/${companyId}/${entity}`;
      const response = await qboClient.post(url, bodyData, {
        params: {
          minorversion: defaultMinorVersion,
          ...queryParams,
        },
        headers: getQboAuthHeaders(accessToken),
      });

      return {
        message: `Successfully created ${entity} with Id: ${response.data?.Bill?.Id}`,
        data: response.data,
      };
    },
  };

  return qboService;
};

export const qboService = createQboService();
