import { createApiClient, ApiService } from "../api.service";
import { logger } from "../../utils/logger";
import ApiException from "../../utils/api-exception";
import { ErrorCodes } from "../../utils/response";

/** QuickBooks Online API service interface */
export interface QboApiService extends ApiService {
  post(entity: string, options: QboPostRequestOptions): Promise<any>;
}

/** Base options for QBO API requests */
interface QboRequestOptions {
  accessToken: string; // OAuth access token
  companyId: string; // QBO company ID
  queryParams?: Record<string, any>;
}

/** Extended options for POST requests */
interface QboPostRequestOptions extends QboRequestOptions {
  bodyData: any; // Request body payload
}

/** Creates and configures QBO service instance */
export const createQboService = (): QboApiService => {
  const QBO_API_BASE_URL =
    "https://sandbox-quickbooks.api.intuit.com/v3/company";
  const defaultMinorVersion = process.env.QBO_MINOR_VERSION || "75";

  // Generate QBO API headers
  const getQboAuthHeaders = (accessToken: string): Record<string, string> => ({
    Authorization: `Bearer ${accessToken}`,
    Accept: "application/json",
    "Content-Type": "application/json",
  });

  const qboClient = createApiClient(QBO_API_BASE_URL);

  // Handle API response errors
  qboClient.interceptors.response.use(
    (response) => response,
    (error) => {
      const resData = error.response?.data;

      const faultType = resData?.Fault?.type || resData?.fault?.type || resData?.Fault?.Type;

      if (faultType === "AUTHENTICATION" || faultType === "AuthorizationFault") {
        throw new ApiException(ErrorCodes.INVALID_TOKEN);
      }

      const qboFaultError =
        resData?.Fault?.Error?.[0] || resData?.fault?.error?.[0];

      if (qboFaultError) {
        throw new ApiException({
          status: error.response.status,
          code: resData?.code || ErrorCodes.INTERNAL_ERROR.code,
          message:
            qboFaultError.detail ||
            qboFaultError.Detail ||
            qboFaultError.Message ||
            qboFaultError.message ||
            "QBO service request failed",
          errorDescription: resData?.error || error.message,
        });
      }

      return Promise.reject(error);
    }
  );

  // QBO service implementation
  const qboService: QboApiService = {
    // POST request handler
    async post(
      entity: string,
      {
        accessToken,
        companyId,
        bodyData,
        queryParams = {},
      }: QboPostRequestOptions
    ) {
      logger.info(
        `QBO POST request for entity: ${entity} | companyId: ${companyId}`
      );

      const url = `/${companyId}/${entity}`;
      const response = await qboClient.post(url, bodyData, {
        params: {
          minorversion: defaultMinorVersion,
          ...queryParams,
        },
        headers: getQboAuthHeaders(accessToken),
      });

      return {
        message: `Successfully created ${entity} with Id: ${response.data?.JournalEntry?.Id}`,
        data: response.data
      };
    },
  };

  return qboService;
};

// Create singleton QBO service instance
export const qboService = createQboService();
