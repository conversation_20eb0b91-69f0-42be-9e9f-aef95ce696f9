// QBO API Service: Provides methods to interact with QuickBooks Online API for GET operations.
import { createApiClient, ApiService } from "../api.service";
import { logger } from "../../utils/logger";
import ApiException from "../../utils/api-exception";
import { ErrorCodes } from "../../utils/response";

// Interfaces
export interface DateFilterOptions {
  fieldName: string;
  startDate: string;
  endDate?: string;
}

export interface QboRequestOptions {
  accessToken: string;
  companyId: string;
  queryParams?: Record<string, any>; // Ensure this can hold dateFilter and totalCount
  dateFilter?: DateFilterOptions; // This seems redundant if queryParams holds it. Consolidate if possible.
}

export interface QboPostRequestOptions extends QboRequestOptions {
  bodyData: any;
}

export interface QboApiService extends ApiService {
  get(entity: string, options: QboRequestOptions): Promise<any>;
}

// Constants
const QBO_API_BASE_URL =
  process.env.QBO_API_URL ||
  "https://sandbox-quickbooks.api.intuit.com/v3/company";
const defaultMinorVersion = process.env.QBO_MINOR_VERSION || "75";

// Helper: Auth headers
const getQboAuthHeaders = (accessToken: string): Record<string, string> => ({
  Authorization: `Bearer ${accessToken}`,
  Accept: "application/json",
  "Content-Type": "application/json",
  "User-Agent": "QBOV3-OAuth2-Client",
});

// Helper: Build date query
const buildDateQuery = (
  entity: string,
  dateFilter?: DateFilterOptions,
  getTotalCount: boolean = false, // Added parameter for total count
  startPosition?: number,
  maxResults: number = 1000 // Default to 1000 if not specified
): string => {
  let query = getTotalCount
    ? `SELECT COUNT(*) FROM ${entity}`
    : `SELECT * FROM ${entity}`; // Modified query based on getTotalCount

  if (dateFilter?.fieldName && dateFilter?.startDate) {
    let fieldName = dateFilter.fieldName;

    // Field transformation for known types
    if (entity.toLowerCase() === "class") {
      if (fieldName === "createdDate") fieldName = "MetaData.CreateTime";
      if (["updatedDate", "modifiedDate"].includes(fieldName))
        fieldName = "MetaData.LastUpdatedTime";
    }

    if (isNaN(Date.parse(dateFilter.startDate))) {
      throw new ApiException(ErrorCodes.INVALID_DATE);
    }

    if (dateFilter.endDate) {
      if (isNaN(Date.parse(dateFilter.endDate))) {
        throw new ApiException(ErrorCodes.INVALID_DATE);
      }
      query += ` WHERE ${fieldName} >= '${dateFilter.startDate}' AND ${fieldName} <= '${dateFilter.endDate}'`;
    } else {
      query += ` WHERE ${fieldName} >= '${dateFilter.startDate}'`;
    }
  }

  // Add pagination parameters if not getting total count
  if (!getTotalCount) {
    // Add STARTPOSITION if provided
    if (startPosition !== undefined) {
      query += ` STARTPOSITION ${startPosition}`;
    }

    // Add MAXRESULTS (either provided value or default 1000)
    query += ` MAXRESULTS ${maxResults}`;
  }

  return query;
};

// Factory function to create a QBO API service instance
export const createQboService = (): QboApiService => {
  const qboClient = createApiClient(QBO_API_BASE_URL);

  // Error interceptor
  qboClient.interceptors.response.use(
    (response: any) => response,
    (error: any) => {
      const resData = error.response?.data;
      const faultType =
        resData?.Fault?.type ||
        resData?.fault?.type ||
        resData?.Fault?.Type ||
        resData?.fault?.Type;

      if (
        faultType === "AUTHENTICATION" ||
        faultType === "AuthorizationFault"
      ) {
        throw new ApiException(ErrorCodes.INVALID_TOKEN);
      }

      const qboFaultError =
        resData?.Fault?.Error?.[0] ||
        resData?.fault?.error?.[0] ||
        resData?.Fault?.Error ||
        resData?.fault?.Error;

      if (qboFaultError) {
        const errorDetail = Array.isArray(qboFaultError)
          ? qboFaultError[0]
          : qboFaultError;

        throw new ApiException({
          status: error.response?.status || 500,
          code: resData?.code || ErrorCodes.INTERNAL_ERROR.code,
          errorDescription:
            errorDetail.detail ||
            errorDetail.Detail ||
            errorDetail.Message ||
            errorDetail.message ||
            "QBO service request failed",
          message:
            errorDetail.Message ||
            errorDetail.message ||
            resData?.error ||
            error.message,
        });
      }

      return Promise.reject(error);
    }
  );

  // API methods
  const qboService: QboApiService = {
    async get(
      entity: string,
      { accessToken, companyId, queryParams = {} }: QboRequestOptions
    ) {
      logger.info(
        `QBO GET request | entity: ${entity} | companyId: ${companyId}`
      );

      const getTotalCount = queryParams.totalCount === true; // Check for totalCount

      // Parse pagination parameters
      let startPosition: number | undefined = undefined;
      if (queryParams.startPosition !== undefined) {
        startPosition = parseInt(queryParams.startPosition as string, 10);
        if (isNaN(startPosition) || startPosition < 1) {
          throw new ApiException({
            ...ErrorCodes.BAD_REQUEST,
            errorDescription: "startPosition must be a positive integer",
          });
        }
      }

      // Set default maxResults to 1000 if not provided
      let maxResults: number = 1000; // Default value
      if (queryParams.maxResults !== undefined) {
        const parsedMaxResults = parseInt(queryParams.maxResults as string, 10);
        if (isNaN(parsedMaxResults) || parsedMaxResults < 1) {
          throw new ApiException({
            ...ErrorCodes.BAD_REQUEST,
            errorDescription: "maxResults must be a positiv3e integer",
          });
        }
        maxResults = parsedMaxResults;
      }

      const query = buildDateQuery( 
        entity,
        queryParams.dateFilter, // Assuming dateFilter is passed within queryParams
        getTotalCount,
        startPosition,
        maxResults
      );
      const url = `/${companyId}/query`;
      logger.info(`QBO query: ${query}`);

      const response = await qboClient.get(url, {
        params: {
          query,
          minorversion: defaultMinorVersion,
        },
        headers: getQboAuthHeaders(accessToken),
      });

      if (getTotalCount) {
        const totalCount = response.data?.QueryResponse?.totalCount;
        if (typeof totalCount === "number") {
          return {
            data: { totalCount: totalCount },
            message: `Successfully fetched total count for ${entity}`,
          };
        } else {
          logger.warn(
            `Could not find totalCount in QBO response for entity: ${entity}`
          );
          return {
            data: { totalCount: 0 },
            message: `Total count not found for ${entity}`,
          };
        }
      } else {
        const responseDataObj = response.data?.QueryResponse;

        if (!responseDataObj) {
          return {
            data: {
              Class: [],
            },
            message: `No ${entity} records found`,
          };
        }

        if (!responseDataObj.Class) {
          responseDataObj.Class = [];
        }

        return {
          data: responseDataObj,
          message: `Successfully fetched ${
            responseDataObj?.Class?.length || 0
          } ${entity} record(s)`,
        };
      }
    },
  };

  return qboService;
};

// Singleton instance
export const qboService = createQboService();
