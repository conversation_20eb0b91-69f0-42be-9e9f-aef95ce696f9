// proxy.ts - Using your existing utilities
import { Express, NextFunction, Request, Response } from "express";
import express from "express";
import { createProxyMiddleware, Options } from "http-proxy-middleware";
import { RequestExtended } from "./interfaces/globalInterface";
import asyncHandler from "./utils/async-handler";
import { logger } from "./utils/logger";
import { ErrorCodes, ErrorResponse } from "./utils/response";
import ApiException from "./utils/api-exception";

// Service configuration interface
export interface ServiceConfig {
  target: string;
  pathRewrite: Record<string, string>;
  methods: {
    GET?: string;
    POST?: string;
    PUT?: string;
    DELETE?: string;
  };
  envVar?: string;
}

// Service routes configuration
export const services: Record<string, ServiceConfig> = {
  vendor: {
    target: process.env.VENDOR_SERVICE_URL || "http://localhost:8001",
    pathRewrite: { "^/api/vendor": "" },
    methods: { GET: "api/service", POST: "api/service" },
    envVar: "VENDOR_SERVICE_URL",
  },
  bill: {
    target: process.env.BILL_SERVICE_URL || "http://localhost:8006",
    pathRewrite: { "^/api/bill": "" },
    methods: { GET: "api/service", POST: "api/service" },
    envVar: "BILL_SERVICE_URL",
  },
  journalentry: {
    target: process.env.JOURNALENTRY_SERVICE_URL || "http://localhost:8005",
    pathRewrite: { "^/api/journalentry": "" },
    methods: { POST: "api/service" }, // ✅ Only POST
    envVar: "JOURNALENTRY_SERVICE_URL",
  },
  payment: {
    target: process.env.PAYMENT_SERVICE_URL || "http://localhost:8004",
    pathRewrite: { "^/api/payment": "" },
    methods: { POST: "api/service" }, // ✅ Only POST
    envVar: "PAYMENT_SERVICE_URL",
  },
  account: {
    target: process.env.ACCOUNT_SERVICE_URL || "http://localhost:8003",
    pathRewrite: { "^/api/account": "" },
    methods: { GET: "api/service" }, // ✅ Only GET
    envVar: "ACCOUNT_SERVICE_URL",
  },
  class: {
    target: process.env.CLASS_SERVICE_URL || "http://localhost:8002",
    pathRewrite: { "^/api/class": "" },
    methods: { GET: "api/service" }, // ✅ Only GET
    envVar: "CLASS_SERVICE_URL",
  },
};

// Enhanced proxy middleware setup
export const setupProxy = (app: Express) => {
  Object.entries(services).forEach(([serviceName, config]) => {
    const proxyOptions: Options = {
      target: config.target,
      changeOrigin: true,

      // Custom path rewriting
      pathRewrite: (path: string, req: Request) => {
        const rewriteKey = Object.keys(config.pathRewrite)[0];
        const baseRewrite = config.pathRewrite[rewriteKey];
        const methodPath =
          config.methods[req.method as keyof typeof config.methods] || "";

        // Split path and query string
        const [pathOnly, queryString] = path.split("?");

        // Remove the service prefix from path only
        const cleanPath = pathOnly.replace(new RegExp(rewriteKey), "");

        // Build the new path with method path
        const newPath = `${baseRewrite}${methodPath}${cleanPath}`;

        // Add query string back if it exists
        return queryString ? `${newPath}?${queryString}` : newPath;
      },

      // Handle request forwarding
      onProxyReq: (proxyReq: any, req: RequestExtended) => {
        try {
          // Forward authentication headers
          const authHeaders = ["accesstoken", "companyid", "authorization"];
          authHeaders.forEach((header) => {
            if (req.headers[header]) {
              proxyReq.setHeader(header, req.headers[header]);
            }
          });

          // Forward custom headers
          Object.keys(req.headers).forEach((header) => {
            if (header.startsWith("x-") || header.startsWith("custom-")) {
              proxyReq.setHeader(header, req.headers[header]);
            }
          });

          // Handle query parameters - DON'T duplicate them
          // pathRewrite already handles query params, so we don't need to modify proxyReq.path here

          // Log the proxied request using your logger
          logger.info(`🔄 Proxying ${req.method} to ${serviceName}`, {
            originalPath: req.path,
            originalUrl: req.url,
            proxyPath: proxyReq.path,
            target: config.target,
            hasBody: !!req.body && Object.keys(req.body || {}).length > 0,
            method: req.method,
            headers: {
              accesstoken: req.headers["accesstoken"]
                ? "[PROVIDED]"
                : undefined,
              companyid: req.headers["companyid"] || undefined,
            },
          });

          // Handle body data based on HTTP method
          if (["POST", "PUT", "PATCH"].includes(req.method)) {
            if (req.body && Object.keys(req.body).length > 0) {
              const bodyData = JSON.stringify(req.body);
              proxyReq.setHeader("Content-Type", "application/json");
              proxyReq.setHeader("Content-Length", Buffer.byteLength(bodyData));
              proxyReq.write(bodyData);
              logger.debug(`📤 Sent body data to ${serviceName}`, {
                bodySize: bodyData.length,
              });
            }
          } else if (req.method === "GET") {
            // Ensure GET requests don't have body-related headers
            proxyReq.removeHeader("Content-Length");
            proxyReq.removeHeader("Transfer-Encoding");
            proxyReq.removeHeader("Content-Type");

            if (req.body && Object.keys(req.body || {}).length > 0) {
              logger.warn(
                `⚠️ GET request to ${serviceName} had body data - ignoring to prevent socket errors`
              );
            }
          }
        } catch (error) {
          logger.error(`💥 Error in onProxyReq for ${serviceName}`, error);
        }
      },

      // Handle successful responses
      onProxyRes: (proxyRes: any, req: RequestExtended, res: Response) => {
        let responseBody = "";

        // Collect response data
        proxyRes.on("data", (chunk: Buffer) => {
          responseBody += chunk.toString();
        });

        proxyRes.on("end", () => {
          try {
            logger.info(`✅ Response from ${serviceName}`, {
              status: proxyRes.statusCode,
              path: req.path,
              contentLength: responseBody.length,
            });

            // Handle error responses (4xx, 5xx)
            if (proxyRes.statusCode >= 400) {
              let errorData;
              try {
                errorData = JSON.parse(responseBody);
              } catch (parseError) {
                // If can't parse JSON, create standard error format using your ErrorResponse
                errorData = ErrorResponse({
                  ...ErrorCodes.INTERNAL_ERROR,
                  errorDescription:
                    responseBody || `Error from ${serviceName} service`,
                });
              }

              logger.error(`❌ Error from ${serviceName}`, {
                status: proxyRes.statusCode,
                error: errorData,
              });

              return res.status(proxyRes.statusCode).json(errorData);
            }

            // Handle successful responses
            if (responseBody) {
              try {
                const responseData = JSON.parse(responseBody);

                // Forward the response as-is
                res.status(proxyRes.statusCode);

                // Copy response headers
                Object.keys(proxyRes.headers).forEach((header) => {
                  if (
                    ![
                      "content-length",
                      "transfer-encoding",
                      "connection",
                    ].includes(header.toLowerCase())
                  ) {
                    res.setHeader(header, proxyRes.headers[header]);
                  }
                });

                return res.json(responseData);
              } catch (parseError) {
                logger.warn(
                  `⚠️ JSON parse error from ${serviceName}`,
                  parseError
                );
                return res.status(200).send(responseBody);
              }
            } else {
              // Empty response
              return res.status(proxyRes.statusCode).send();
            }
          } catch (error) {
            logger.error(
              `💥 Error processing response from ${serviceName}`,
              error
            );
            if (!res.headersSent) {
              return res.status(500).json(
                ErrorResponse({
                  ...ErrorCodes.INTERNAL_ERROR,
                  errorDescription: `Error processing response from ${serviceName} service`,
                })
              );
            }
          }
        });

        proxyRes.on("error", (error: Error) => {
          logger.error(`💥 Response stream error from ${serviceName}`, error);
          if (!res.headersSent) {
            res.status(500).json(
              ErrorResponse({
                ...ErrorCodes.INTERNAL_ERROR,
                errorDescription: `Response error from ${serviceName} service`,
              })
            );
          }
        });
      },

      // Handle connection/proxy errors
      onError: (err: Error, req: RequestExtended, res: Response) => {
        logger.error(`🚨 Proxy error for ${serviceName}`, {
          error: err.message,
          code: (err as any).code,
          target: config.target,
        });

        if (!res.headersSent) {
          res.status(503).json(
            ErrorResponse({
              ...ErrorCodes.SERVICE_UNAVAILABLE,
              errorDescription: `${
                serviceName.charAt(0).toUpperCase() + serviceName.slice(1)
              } service is unavailable: ${err.message}`,
            })
          );
        }
      },

      // Security and performance options
      secure: process.env.NODE_ENV === "production",
      timeout: 30000,
      proxyTimeout: 30000,
      logLevel: "silent",
    };

    const serviceSupportsBodyMethods = Object.keys(config.methods).some(
      (method) => ["POST", "PUT", "PATCH"].includes(method)
    );

    if (serviceSupportsBodyMethods) {
      // Apply JSON body parsing FIRST for relevant methods
      app.use(
        `/api/${serviceName}`,
        (req: Request, res: Response, next: NextFunction) => {
          if (["POST", "PUT", "PATCH"].includes(req.method)) {
            express.json({ limit: "10mb" })(req, res, (err) => {
              if (err) {
                // Handle parsing errors, e.g., malformed JSON
                logger.error(`🚫 JSON PARSE ERROR for ${serviceName}`, {
                  service: serviceName.toUpperCase(),
                  method: req.method,
                  path: req.path,
                  ip: req.ip,
                  error: err.message,
                });
                return res.status(ErrorCodes.BAD_REQUEST.status).json(
                  ErrorResponse({
                    ...ErrorCodes.BAD_REQUEST,
                    message: "Invalid JSON payload: " + err.message,
                    errorDescription:
                      "The request body contains malformed JSON.",
                  })
                );
              }
              next();
            });
          } else {
            next();
          }
        }
      );

      // THEN, add body validation for POST/PUT/PATCH requests for services that require bodies
      app.use(
        `/api/${serviceName}`,
        (req: RequestExtended, res: Response, next: NextFunction) => {
          // Skip validation for GET, DELETE, etc.
          if (!["POST", "PUT", "PATCH"].includes(req.method)) {
            return next();
          }

          // Check if the request body is empty
          const isEmptyBody = !req.body || Object.keys(req.body).length === 0;

          if (isEmptyBody) {
            return res.status(400).json(
              ErrorResponse({
                ...ErrorCodes.BAD_REQUEST,
                message: `Empty body for ${req.method} not allowed`,
                errorDescription: `Request body is required for ${req.method} operations to ${serviceName} service. Ensure the request body is not empty.`,
              })
            );
          }
          next();
        }
      );
    }

    // Validate allowed methods before proxying
    app.use(
      `/api/${serviceName}`,
      (req: RequestExtended, res: Response, next: NextFunction) => {
        const allowedMethods = Object.keys(config.methods);
        const currentMethod = req.method.toUpperCase();

        if (!allowedMethods.includes(currentMethod)) {
          throw new ApiException(
            ErrorCodes.GENERATE_METHOD_NOT_ALLOWED(
              `Method ${currentMethod} not allowed for ${serviceName} service`
            )
          );
        }

        next();
      }
    );

    // Proxy the request if method is valid
    app.use(`/api/${serviceName}`, createProxyMiddleware(proxyOptions));

    console.log(`🔗 Proxy setup for /${serviceName} → ${config.target}`);
  });
};
