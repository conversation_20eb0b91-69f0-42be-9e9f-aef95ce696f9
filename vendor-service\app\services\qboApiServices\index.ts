// QBO API Service: Handles communication with QuickBooks Online API for GET and POST operations
// Provides error handling and token validation for QBO API responses
import { createApiClient, ApiService } from "../api.service";
import { logger } from "../../utils/logger";
import ApiException from "../../utils/api-exception";
import { ErrorCodes } from "../../utils/response";

// QBO API Service interface extending base ApiService
export interface QboApiService extends ApiService {
  get(entity: string, options: QboRequestOptions): Promise<any>;
  post(entity: string, options: QboPostRequestOptions): Promise<any>;
}

export interface DateFilterOptions {
  fieldName: string;
  startDate: string;
  endDate?: string;
}

// Options for QBO GET requests
interface QboRequestOptions {
  accessToken: string;
  companyId: string;
  queryParams?: Record<string, any>; // Ensure this can hold dateFilter and totalCount
}

// Options for QBO POST requests (includes bodyData)
interface QboPostRequestOptions extends QboRequestOptions {
  bodyData: any;
}

const buildDateQuery = (
  entity: string,
  dateFilter?: DateFilterOptions,
  getTotalCount: boolean = false, // Added parameter for total count
  startPosition?: number,
  maxResults: number = 1000 // Default to 1000 if not specified
): string => {
  let query = getTotalCount
    ? `SELECT COUNT(*) FROM ${entity}`
    : `SELECT * FROM ${entity}`; // Modified query based on getTotalCount

  if (dateFilter?.fieldName && dateFilter?.startDate) {
    let fieldName = dateFilter.fieldName;

    // Field transformation for known types
    if (entity.toLowerCase() === "vendor") {
      if (fieldName === "createdDate") fieldName = "MetaData.CreateTime";
      if (["updatedDate", "modifiedDate"].includes(fieldName))
        fieldName = "MetaData.LastUpdatedTime";
    }

    if (isNaN(Date.parse(dateFilter.startDate))) {
      throw new ApiException(ErrorCodes.INVALID_DATE);
    }

    if (dateFilter.endDate) {
      if (isNaN(Date.parse(dateFilter.endDate))) {
        throw new ApiException(ErrorCodes.INVALID_DATE);
      }
      query += ` WHERE ${fieldName} >= '${dateFilter.startDate}' AND ${fieldName} <= '${dateFilter.endDate}'`;
    } else {
      query += ` WHERE ${fieldName} >= '${dateFilter.startDate}'`;
    }
  }

  // Add pagination parameters if not getting total count
  if (!getTotalCount) {
    // Add STARTPOSITION if provided
    if (startPosition !== undefined) {
      query += ` STARTPOSITION ${startPosition}`;
    }

    // Add MAXRESULTS (either provided value or default 1000)
    query += ` MAXRESULTS ${maxResults}`;
  }

  return query;
};

// Factory function to create a QBO API Service instance
export const createQboService = (): QboApiService => {
  const QBO_API_BASE_URL =
    process.env.QBO_API_URL ||
    "https://sandbox-quickbooks.api.intuit.com/v3/company";
  const defaultMinorVersion = process.env.QBO_MINOR_VERSION || "75";

  // Helper to generate QBO auth headers
  const getQboAuthHeaders = (accessToken: string): Record<string, string> => ({
    Authorization: `Bearer ${accessToken}`,
    Accept: "application/json",
    "Content-Type": "application/json",
  });

  // Create API client for QBO
  const qboClient = createApiClient(QBO_API_BASE_URL);

  // Interceptor for handling QBO API errors and token issues
  qboClient.interceptors.response.use(
    (response) => response,
    (error) => {
      const resData = error.response?.data;

      // Extract fault type for error handling
      const faultType =
        resData?.Fault?.type || resData?.fault?.type || resData?.Fault?.Type;

      // Handle authentication/token errors
      if (
        faultType === "AUTHENTICATION" ||
        faultType === "AuthorizationFault"
      ) {
        throw new ApiException(ErrorCodes.INVALID_TOKEN);
      }

      // Extract QBO fault error details
      const qboFaultError =
        resData?.Fault?.Error?.[0] || resData?.fault?.error?.[0];

      // Throw detailed API exception if QBO error present
      if (qboFaultError) {
        throw new ApiException({
          status: error.response.status,
          code: resData?.code || ErrorCodes.INTERNAL_ERROR.code,
          errorDescription:
            qboFaultError.detail ||
            qboFaultError.Detail ||
            qboFaultError.Message ||
            qboFaultError.message ||
            "QBO service request failed",

          message:
            qboFaultError.Message ||
            qboFaultError.message ||
            resData?.error ||
            error.message,
        });
      }

      // Reject promise for unhandled errors
      return Promise.reject(error);
    }
  );

  // QBO API Service implementation
  const qboService: QboApiService = {
    // GET: Fetches entities from QBO using SQL-like query
    async get(
      entity: string,
      { accessToken, companyId, queryParams = {} }: QboRequestOptions
    ) {
      logger.info(
        `QBO GET request | entity: ${entity} | companyId: ${companyId}`
      );

      const getTotalCount = queryParams.totalCount === true; // Check for totalCount

      // Parse pagination parameters
      let startPosition: number | undefined = undefined;
      if (queryParams.startPosition !== undefined) {
        startPosition = parseInt(queryParams.startPosition as string, 10);
        if (isNaN(startPosition) || startPosition < 1) {
          throw new ApiException({
            ...ErrorCodes.BAD_REQUEST,
            errorDescription: "startPosition must be a positive integer",
          });
        }
      }

      // Set default maxResults to 1000 if not provided
      let maxResults: number = 1000; // Default value
      if (queryParams.maxResults !== undefined) {
        const parsedMaxResults = parseInt(queryParams.maxResults as string, 10);
        if (isNaN(parsedMaxResults) || parsedMaxResults < 1) {
          throw new ApiException({
            ...ErrorCodes.BAD_REQUEST,
            errorDescription: "maxResults must be a positive integer",
          });
        }
        maxResults = parsedMaxResults;
      }

      // Build query with date filter, total count flag, and pagination
      const query = buildDateQuery(
        entity,
        queryParams.dateFilter,
        getTotalCount,
        startPosition,
        maxResults
      );
      const url = `/${companyId}/query`;

      logger.info(`QBO query: ${query}`);

      const response = await qboClient.get(url, {
        params: {
          query,
          minorversion: defaultMinorVersion,
        },
        headers: getQboAuthHeaders(accessToken),
      });

      if (getTotalCount) {
        const totalCount = response.data?.QueryResponse?.totalCount;
        if (typeof totalCount === "number") {
          return {
            data: { totalCount: totalCount },
            message: `Successfully fetched total count for ${entity}`,
          };
        } else {
          // Handle cases where totalCount might not be directly available or is malformed
          logger.warn(
            `Could not find totalCount in QBO response for entity: ${entity}`
          );
          return {
            data: { totalCount: 0 }, // Default to 0 if not found
            message: `Total count not found for ${entity}`,
          };
        }
      } else {
        const responseDataObj = response.data?.QueryResponse;

        if (!responseDataObj) {
          return {
            data: {
              Vendor: [],
            },
            message: `No ${entity} records found`,
          };
        }

        if (!responseDataObj.Vendor) {
          responseDataObj.Vendor = [];
        }

        return {
          data: responseDataObj,
          message: `Successfully fetched ${
            responseDataObj?.Vendor?.length || 0
          } ${entity} record(s)`,
        };
      }
    },

    // POST: Creates a new entity in QBO
    async post(
      entity: string,
      {
        accessToken,
        companyId,
        bodyData,
        queryParams = {},
      }: QboPostRequestOptions
    ) {
      logger.info(
        `QBO POST request for entity: ${entity} | companyId: ${companyId}`
      );

      const url = `/${companyId}/${entity}`;
      const response = await qboClient.post(url, bodyData, {
        params: {
          minorversion: defaultMinorVersion,
          ...queryParams,
        },
        headers: getQboAuthHeaders(accessToken),
      });

      return {
        message: `Successfully created ${entity} with Id: ${response.data?.Vendor?.Id}`,
        data: response.data,
      };
    },
  };

  return qboService;
};

// Export singleton QBO service instance
export const qboService = createQboService();
