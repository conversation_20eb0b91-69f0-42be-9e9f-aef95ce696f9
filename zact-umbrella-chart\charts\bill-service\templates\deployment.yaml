﻿apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "bill-service.fullname" . }}
  labels:
    {{- include "bill-service.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "bill-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "bill-service.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bill-service.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ include "bill-service.imageTag" . }}"
          imagePullPolicy: {{ include "bill-service.imagePullPolicy" . }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.service.port }}"
            - name: NODE_ENV
              value: "{{ if .Values.global }}{{ .Values.global.nodeEnv | default "production" }}{{ else }}production{{ end }}"
            - name: QBO_API_URL
              value: "{{ if .Values.global }}{{ .Values.global.qboApiUrl }}{{ end }}"
            - name: QBO_MINOR_VERSION
              value: "{{ if .Values.global }}{{ .Values.global.qboMinorVersion | default "75" }}{{ else }}75{{ end }}"
            {{- if and .Values.global .Values.global.jwtSecret }}
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-secrets
                  key: jwt-secret
            {{- end }}
          {{- if .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          {{- end }}
          {{- if .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml (.Values.resources | default .Values.defaultResources) | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}


