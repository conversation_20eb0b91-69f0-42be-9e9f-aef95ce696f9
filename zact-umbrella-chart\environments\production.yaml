# Production environment configuration
# Non-sensitive environment-specific values

global:
  nodeEnv: "production"
  qboApiUrl: "https://quickbooks.api.intuit.com/v3/company"  # Production QB API
  imageTag: "stable"
  imagePullPolicy: "IfNotPresent"

# Production resource limits (higher for performance)
defaultResources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

# Service configurations for production
account-service:
  replicaCount: 3

bill-service:
  replicaCount: 3

class-service:
  replicaCount: 3

journalentry-service:
  replicaCount: 3

payment-service:
  replicaCount: 3

vendor-service:
  replicaCount: 3

api-gateway:
  replicaCount: 3
  service:
    type: LoadBalancer
  env:
    ACCOUNT_SERVICE_URL: "http://account-service:8003"
    BILL_SERVICE_URL: "http://bill-service:8006"
    CLASS_SERVICE_URL: "http://class-service:8002"
    JOURNALENTRY_SERVICE_URL: "http://journalentry-service:8005"
    PAYMENT_SERVICE_URL: "http://payment-service:8004"
    VENDOR_SERVICE_URL: "http://vendor-service:8001"

# Enable autoscaling in production
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 20
  targetCPUUtilizationPercentage: 80

# Production-specific annotations
serviceAccount:
  annotations:
    environment: "production"

# Node selector for production nodes
nodeSelector:
  environment: "production"

# Production tolerations
tolerations:
  - key: "production"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

# Anti-affinity for high availability
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - account-service
            - bill-service
            - class-service
            - journalentry-service
            - payment-service
            - vendor-service
            - api-gateway
        topologyKey: kubernetes.io/hostname
