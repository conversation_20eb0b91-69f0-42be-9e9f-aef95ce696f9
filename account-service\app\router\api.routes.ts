import express from "express";
import { processServiceRequest } from "../adapters";
import { RequestExtended } from "../interfaces/globalInterface";
import ApiException from "../utils/api-exception";
import asyncHandler from "../utils/async-handler";
import { errorHandler, notFoundHandler } from "../utils/error-handler";
import { ErrorCodes } from "../utils/response";

const router = express.Router();

// === Validation Helpers ===
const validateServiceAndEntity = (service?: string, entity?: string) => {
  if (!service || !["qbo"].includes(service.toLowerCase())) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: `Invalid service: ${service}. Supported: qbo`,
    });
  }

  if (!entity) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "Entity is required",
    });
  }

  if (!["account", "ACCOUNT"].includes(entity)) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "Invalid entity. Allowed: 'account' or 'ACCOUNT'",
    });
  }
};

const validateAuthParams = (
  accessToken?: string | string[],
  companyId?: string | string[]
) => {
  if (!accessToken || !companyId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription:
        "Missing required authentication: accessToken and companyId",
    });
  }
};

const validateDateParams = (dateField?: string, startDate?: string) => {
  if (dateField && !startDate) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "startDate is required when dateField is provided",
    });
  }

  if (startDate && !dateField) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "dateField is required when startDate is provided",
    });
  }

  if (startDate && isNaN(Date.parse(startDate))) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription:
        "Invalid startDate format. Use ISO date format (YYYY-MM-DD)",
    });
  }
};

// === GET ===
router.get(
  "/service",
  asyncHandler(async (req: RequestExtended, res) => {
    const {
      service,
      entity,
      totalCount,
      startPosition,
      maxResults,
      ...queryParamsRaw
    } = req.query; // Destructure parameters
    const accessToken = req.headers["accesstoken"];
    const companyId = req.headers["companyid"];

    validateServiceAndEntity(service as string, entity as string);
    validateAuthParams(accessToken as string, companyId as string);

    req.log?.(`GET ${service} ${entity}`);

    // Clone queryParamsRaw for safe mutation
    const queryParams: Record<string, any> = { ...queryParamsRaw };

    // Add totalCount to queryParams if present in request
    if (totalCount !== undefined) {
      queryParams.totalCount = totalCount === "true"; // Convert string 'true' to boolean true
    }

    // Add pagination parameters
    if (startPosition !== undefined) {
      queryParams.startPosition = startPosition;
    }

    // Add maxResults with default value of 1000 if not provided
    queryParams.maxResults = maxResults || "1000";

    // === Build date filter object ===
    const { dateField, startDate, endDate } = queryParams;

    // Validate date parameters if provided
    if (dateField || startDate) {
      validateDateParams(dateField as string, startDate as string);

      if (endDate && isNaN(Date.parse(endDate as string))) {
        throw new ApiException({
          ...ErrorCodes.BAD_REQUEST,
          errorDescription:
            "Invalid endDate format. Use ISO date format (YYYY-MM-DD)",
        });
      }
    }

    // Create dateFilter object if date parameters are provided
    if (dateField && startDate) {
      const dateFilter = {
        fieldName: dateField as string,
        startDate: startDate as string,
        ...(endDate && { endDate: endDate as string }),
      };

      // Add dateFilter to queryParams
      queryParams.dateFilter = dateFilter;
    }

    // === Clean up queryParams ===
    delete queryParams.dateField;
    delete queryParams.startDate;
    delete queryParams.endDate;
    // totalCount, startPosition, and maxResults are already handled and placed in queryParams

    // === Final forwarding ===
    const result = await processServiceRequest({
      service: service as string,
      entity: entity as string,
      requestType: "get",
      accessToken: accessToken as string,
      companyId: companyId as string,
      queryParams,
    });

    return res.json(result);
  })
);

// === Error Handling Middleware ===
router.use(notFoundHandler);
router.use(errorHandler);

export default router;
