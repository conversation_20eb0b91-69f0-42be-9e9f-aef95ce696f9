// QBO API Service: Provides methods to interact with QuickBooks Online API for GET and POST operations.
import { createApiClient, ApiService } from "../api.service";
import { logger } from "../../utils/logger";
import ApiException from "../../utils/api-exception";
import { ErrorCodes } from "../../utils/response";

// QboApiService interface extends ApiService with QBO-specific methods
export interface QboApiService extends ApiService {
  post(entity: string, options: QboPostRequestOptions): Promise<any>;
}

// Options for QBO post requests
interface QboRequestOptions {
  accessToken: string;
  companyId: string;
  queryParams?: Record<string, any>;
}

// Options for QBO POST requests
interface QboPostRequestOptions extends QboRequestOptions {
  bodyData: any;
}

// Factory function to create a QBO API service instance
export const createQboService = (): QboApiService => {
  // QBO API base URL (sandbox environment)
  const QBO_API_BASE_URL =
    process.env.QBO_API_URL ||
    "https://sandbox-quickbooks.api.intuit.com/v3/company";
  // Use environment minor version or default to 75
  const defaultMinorVersion = process.env.QBO_MINOR_VERSION || "75";

  // Helper to construct QBO auth headers
  const getQboAuthHeaders = (accessToken: string): Record<string, string> => ({
    Authorization: `Bearer ${accessToken}`,
    Accept: "application/json",
    "Content-Type": "application/json",
  });

  // Create an Axios client for QBO API
  const qboClient = createApiClient(QBO_API_BASE_URL);

  // Interceptor for handling QBO API errors and mapping to ApiException
  qboClient.interceptors.response.use(
    (response) => response,
    (error) => {
      const resData = error.response?.data;

      // Handle authentication/authorization errors
      const faultType =
        resData?.Fault?.type || resData?.fault?.type || resData?.Fault?.Type;

      if (
        faultType === "AUTHENTICATION" ||
        faultType === "AuthorizationFault"
      ) {
        throw new ApiException(ErrorCodes.INVALID_TOKEN);
      }

      // Handle QBO-specific fault errors
      const qboFaultError =
        resData?.Fault?.Error?.[0] || resData?.fault?.error?.[0];

      if (qboFaultError) {
        throw new ApiException({
          status: error.response.status || qboFaultError.code,
          code: resData?.code || ErrorCodes.INTERNAL_ERROR.code,
          errorDescription:
            qboFaultError.detail ||
            qboFaultError.Detail ||
            qboFaultError.Message ||
            qboFaultError.message ||
            "QBO service request failed",

          message:
            qboFaultError.Message ||
            qboFaultError.message ||
            resData?.error ||
            error.message,
        });
      }

      return Promise.reject(error);
    }
  );

  // QBO API service implementation
  const qboService: QboApiService = {
    // POST: Creates a new entity in QBO
    async post(
      entity: string,
      {
        accessToken,
        companyId,
        bodyData,
        queryParams = {},
      }: QboPostRequestOptions
    ) {
      logger.info(
        `QBO POST request for entity: ${entity} | companyId: ${companyId}`
      );

      // Map payment entity to BillPayment for QBO API
      const qboEntity =
        entity.toLowerCase() === "payment" ? "billpayment" : entity;
      const url = `/${companyId}/${qboEntity}`;
      const response = await qboClient.post(url, bodyData, {
        params: {
          minorversion: defaultMinorVersion,
          ...queryParams,
        },
        headers: getQboAuthHeaders(accessToken),
      });

      // Handle different entity types for response message
      let entityId;
      let entityName = entity;

      if (entity.toLowerCase() === "payment") {
        // For payment entity, we call BillPayment API and return BillPayment data
        entityId = response.data?.BillPayment?.Id;
        entityName = "BillPayment";
      } else if (entity.toLowerCase() === "billpayment") {
        entityId = response.data?.BillPayment?.Id;
        entityName = "BillPayment";
      } else {
        // Fallback for other entities
        entityId = response.data?.[entity]?.Id || "Unknown";
        entityName = entity;
      }

      return {
        message: `Successfully created ${entityName} with Id: ${entityId}`,
        data: response.data,
      };
    },
  };

  return qboService;
};

// Export a singleton instance of the QBO service
export const qboService = createQboService();
