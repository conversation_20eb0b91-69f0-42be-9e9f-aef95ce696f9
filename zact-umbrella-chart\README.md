# ZACT Microservices Umbrella Chart

This Helm umbrella chart manages the deployment of all ZACT microservices with proper environment bifurcation and secrets management.

## Architecture

The umbrella chart consists of the following microservices:

- **API Gateway** (port 8000) - Entry point and request routing
- **Account Service** (port 8003) - Account management
- **Bill Service** (port 8006) - Bill processing
- **Class Service** (port 8002) - Class/category management
- **Journal Entry Service** (port 8005) - Journal entry operations
- **Payment Service** (port 8004) - Payment processing
- **Vendor Service** (port 8001) - Vendor management

## Environment Bifurcation

### Values Files Structure

```
zact-umbrella-chart/
├── values.yaml                    # Default values (safe to commit)
├── environments/
│   ├── development.yaml           # Development environment config
│   ├── staging.yaml              # Staging environment config
│   └── production.yaml           # Production environment config
└── ci-cd/
    ├── github-actions-deploy.yml # GitHub Actions workflow
    └── argocd-application.yaml   # ArgoCD application manifests
```

### Configuration Management

- **values.yaml**: Contains only defaults and non-sensitive configuration
- **Environment files**: Contains environment-specific values
- **No secrets required**: Simplified deployment without external dependencies

## Deployment

### Prerequisites

- Helm 3.x installed
- Kubernetes cluster access
- Docker images pushed to registry

### Local Development

```bash
# Build dependencies
helm dependency build zact-umbrella-chart/

# Deploy to development
helm install zact-dev ./zact-umbrella-chart \
  -f zact-umbrella-chart/environments/development.yaml \
  --namespace zact-dev \
  --create-namespace
```

### Staging Deployment

```bash
helm upgrade --install zact-staging ./zact-umbrella-chart \
  -f zact-umbrella-chart/environments/staging.yaml \
  --namespace zact-staging \
  --create-namespace
```

### Production Deployment

```bash
helm upgrade --install zact-prod ./zact-umbrella-chart \
  -f zact-umbrella-chart/environments/production.yaml \
  --namespace zact-prod \
  --create-namespace
```

### Selective Service Deployment

```bash
# Deploy only specific services
helm install zact-partial ./zact-umbrella-chart \
  -f zact-umbrella-chart/environments/development.yaml \
  --set account-service.enabled=true \
  --set bill-service.enabled=false \
  --set api-gateway.enabled=true \
  --namespace zact-dev
```

## Configuration

### Global Settings

```yaml
global:
  imageTag: "latest" # Docker image tag
  imagePullPolicy: "Always" # Image pull policy
  nodeEnv: "production" # Node.js environment
  qboApiUrl: "" # QuickBooks API URL
  qboMinorVersion: "75" # QB API version
  # Secrets (injected via CI/CD)
  dbPassword: ""
  jwtSecret: ""
  qboClientId: ""
  qboClientSecret: ""
```

### Service-Specific Configuration

Each service can be configured independently:

```yaml
account-service:
  enabled: true
  replicaCount: 3
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
```

## Monitoring and Health Checks

All services include:

- **Liveness probes**: `/health` endpoint
- **Readiness probes**: `/ready` endpoint
- **Resource limits**: CPU and memory constraints
- **Autoscaling**: HPA support (configurable)

## Secrets Management

### GitHub Secrets Required

- `DEV_DB_PASSWORD`, `STAGING_DB_PASSWORD`, `PROD_DB_PASSWORD`
- `DEV_JWT_SECRET`, `STAGING_JWT_SECRET`, `PROD_JWT_SECRET`
- `DEV_QBO_CLIENT_ID`, `STAGING_QBO_CLIENT_ID`, `PROD_QBO_CLIENT_ID`
- `DEV_QBO_CLIENT_SECRET`, `STAGING_QBO_CLIENT_SECRET`, `PROD_QBO_CLIENT_SECRET`
- `KUBE_CONFIG`: Kubernetes cluster configuration

### External Secret Management

For production environments, consider using:

- **AWS Secrets Manager** with External Secrets Operator
- **Azure Key Vault** with CSI driver
- **HashiCorp Vault** integration
- **Google Secret Manager**

## Troubleshooting

### Check Deployment Status

```bash
# Check all pods
kubectl get pods -n zact-prod

# Check services
kubectl get services -n zact-prod

# Check ingress (if configured)
kubectl get ingress -n zact-prod

# View logs
kubectl logs -f deployment/api-gateway -n zact-prod
```

### Common Issues

1. **Image pull errors**: Verify Docker Hub credentials and image names
2. **Service communication**: Check service URLs in API Gateway configuration
3. **Resource limits**: Monitor CPU/memory usage and adjust limits
4. **Secrets**: Ensure all required secrets are properly injected

## Development Workflow

1. **Make changes** to individual service charts
2. **Test locally** using development environment
3. **Update image tags** in environment files
4. **Deploy to staging** for integration testing
5. **Deploy to production** after validation

## Contributing

1. Follow the established chart structure
2. Update environment files for new configuration
3. Test deployments in development environment
4. Update this README for any new features
