// src/utils/responseHandler.ts

// HTTP Status Code Constants
export const HttpStatus = {
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Error Code Constants
export const ErrorCode = {
  VALIDATION_ERROR: 101,
  BAD_REQUEST: 102,
  UNAUTHORIZED: 103,
  NOT_FOUND: 104,
  INTERNAL_ERROR: 105,
  FORBIDDEN: 106,
  CONFLICT: 107,
  UNPROCESSABLE_ENTITY: 108,
  SERVICE_UNAVAILABLE: 109,
} as const;

// Predefined Error Types
export const ErrorTypes = {
  // 400 Bad Request Errors
  BAD_REQUEST: {
    status: HttpStatus.BAD_REQUEST,
    code: ErrorCode.BAD_REQUEST,
    message: "Bad request",
  },

  VALIDATION_ERROR: {
    status: HttpStatus.BAD_REQUEST,
    code: ErrorCode.VALIDATION_ERROR,
    message: "Validation failed",
  },

  INVALID_DATE: {
    status: HttpStatus.BAD_REQUEST,
    code: ErrorCode.BAD_REQUEST,
    message: "Date is not in ISO string format. Please enter a valid string",
  },

  // 401 Unauthorized Errors
  UNAUTHORIZED: {
    status: HttpStatus.UNAUTHORIZED,
    code: ErrorCode.UNAUTHORIZED,
    message: "Unauthorized",
  },

  INVALID_TOKEN: {
    status: HttpStatus.UNAUTHORIZED,
    code: ErrorCode.UNAUTHORIZED,
    message: "Invalid access token or company id",
  },

  // 403 Forbidden Errors
  FORBIDDEN: {
    status: HttpStatus.FORBIDDEN,
    code: ErrorCode.FORBIDDEN,
    message: "Forbidden",
  },

  // 404 Not Found Errors
  NOT_FOUND: {
    status: HttpStatus.NOT_FOUND,
    code: ErrorCode.NOT_FOUND,
    message: "Not found",
  },

  // 409 Conflict Errors
  CONFLICT: {
    status: HttpStatus.CONFLICT,
    code: ErrorCode.CONFLICT,
    message: "Conflict",
  },

  // 422 Unprocessable Entity
  UNPROCESSABLE_ENTITY: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    code: ErrorCode.UNPROCESSABLE_ENTITY,
    message: "Unprocessable entity",
  },

  // 500 Internal Server Error
  INTERNAL_ERROR: {
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    code: ErrorCode.INTERNAL_ERROR,
    message: "Internal server error",
  },

  // 503 Service Unavailable
  SERVICE_UNAVAILABLE: {
    status: HttpStatus.SERVICE_UNAVAILABLE,
    code: ErrorCode.SERVICE_UNAVAILABLE,
    message: "Service unavailable",
  },
} as const;

// Success Response
export const BaseResponse = (result: any) => ({
  responseStatus: HttpStatus.OK,
  message: result?.message || "Success",
  data: result?.data,
});

// Error Response Helper (generic)
export const ErrorResponse = (
  error: (typeof ErrorTypes)[keyof typeof ErrorTypes] & {
    errorDescription?: string;
  }
) => ({
  responseStatus: error.status,
  error,
});

// Exposed ErrorCodes with customization
export const ErrorCodes = {
  ...ErrorTypes,

  GENERATE_BAD_REQUEST: (desc: string) => ({
    ...ErrorTypes.BAD_REQUEST,
    errorDescription: desc,
  }),
};
