# API Gateway Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.services.apiGateway.name }}
  labels:
    app: {{ .Values.services.apiGateway.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.services.apiGateway.type }}
  ports:
    - port: {{ .Values.services.apiGateway.port }}
      targetPort: {{ .Values.services.apiGateway.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.services.apiGateway.name }}
    release: {{ .Release.Name }}

---

# Account Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.services.accountService.name }}
  labels:
    app: {{ .Values.services.accountService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.services.accountService.type }}
  ports:
    - port: {{ .Values.services.accountService.port }}
      targetPort: {{ .Values.services.accountService.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.services.accountService.name }}
    release: {{ .Release.Name }}

---

# Bill Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.services.billService.name }}
  labels:
    app: {{ .Values.services.billService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.services.billService.type }}
  ports:
    - port: {{ .Values.services.billService.port }}
      targetPort: {{ .Values.services.billService.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.services.billService.name }}
    release: {{ .Release.Name }}

---

# Class Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.services.classService.name }}
  labels:
    app: {{ .Values.services.classService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.services.classService.type }}
  ports:
    - port: {{ .Values.services.classService.port }}
      targetPort: {{ .Values.services.classService.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.services.classService.name }}
    release: {{ .Release.Name }}

---

# Journal Entry Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.services.journalentryService.name }}
  labels:
    app: {{ .Values.services.journalentryService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.services.journalentryService.type }}
  ports:
    - port: {{ .Values.services.journalentryService.port }}
      targetPort: {{ .Values.services.journalentryService.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.services.journalentryService.name }}
    release: {{ .Release.Name }}

---

# Payment Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.services.paymentService.name }}
  labels:
    app: {{ .Values.services.paymentService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.services.paymentService.type }}
  ports:
    - port: {{ .Values.services.paymentService.port }}
      targetPort: {{ .Values.services.paymentService.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.services.paymentService.name }}
    release: {{ .Release.Name }}

---

# Vendor Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.services.vendorService.name }}
  labels:
    app: {{ .Values.services.vendorService.name }}
    chart: {{ include "zact-ms-chart.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.services.vendorService.type }}
  ports:
    - port: {{ .Values.services.vendorService.port }}
      targetPort: {{ .Values.services.vendorService.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.services.vendorService.name }}
    release: {{ .Release.Name }}