# Staging environment configuration
# Non-sensitive environment-specific values

global:
  nodeEnv: "staging"
  qboApiUrl: "https://sandbox-quickbooks.api.intuit.com/v3/company"
  imageTag: "staging"
  imagePullPolicy: "Always"

# Staging resource limits (moderate)
defaultResources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 200m
    memory: 256Mi

# Service configurations for staging
account-service:
  replicaCount: 2

bill-service:
  replicaCount: 2

class-service:
  replicaCount: 2

journalentry-service:
  replicaCount: 2

payment-service:
  replicaCount: 2

vendor-service:
  replicaCount: 2

api-gateway:
  replicaCount: 2
  service:
    type: LoadBalancer
  env:
    ACCOUNT_SERVICE_URL: "http://account-service:8003"
    BILL_SERVICE_URL: "http://bill-service:8006"
    CLASS_SERVICE_URL: "http://class-service:8002"
    JOURNALENTRY_SERVICE_URL: "http://journalentry-service:8005"
    PAYMENT_SERVICE_URL: "http://payment-service:8004"
    VENDOR_SERVICE_URL: "http://vendor-service:8001"

# Enable autoscaling in staging
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  targetCPUUtilizationPercentage: 70

# Staging-specific annotations
serviceAccount:
  annotations:
    environment: "staging"

# Node selector for staging nodes
nodeSelector:
  environment: "staging"
