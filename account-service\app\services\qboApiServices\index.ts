import { createApiClient, ApiService } from "../api.service";
import { logger } from "../../utils/logger";
import ApiException from "../../utils/api-exception";
import { ErrorCodes } from "../../utils/response";

// Interfaces
export interface DateFilterOptions {
  fieldName: string;
  startDate: string;
  endDate?: string;
}

export interface QboRequestOptions {
  accessToken: string;
  companyId: string;
  queryParams?: Record<string, any>;
  dateFilter?: DateFilterOptions;
}

export interface QboApiService extends ApiService {
  get(entity: string, options: QboRequestOptions): Promise<any>;
}

// Constants
const QBO_API_BASE_URL = "https://sandbox-quickbooks.api.intuit.com/v3/company";
const defaultMinorVersion = process.env.QBO_MINOR_VERSION || "75";

// Auth headers
const getQboAuthHeaders = (accessToken: string): Record<string, string> => ({
  Authorization: `Bearer ${accessToken}`,
  Accept: "application/json",
  "Content-Type": "application/json",
  "User-Agent": "QBOV3-OAuth2-Postman-Collection",
});

// Date query builder
const buildDateQuery = (
  entity: string,
  dateFilter?: DateFilterOptions,
  getTotalCount: boolean = false, // Added parameter for total count
  startPosition?: number,
  maxResults?: number
): string => {
  let query = getTotalCount
    ? `SELECT COUNT(*) FROM ${entity}`
    : `SELECT * FROM ${entity}`; // Modified query based on getTotalCount
  if (dateFilter?.fieldName && dateFilter?.startDate) {
    let fieldName = dateFilter.fieldName;

    // Field transformation for known types
    if (entity.toLowerCase() === "account") {
      if (fieldName === "createdDate") fieldName = "MetaData.CreateTime";
      if (["updatedDate", "modifiedDate"].includes(fieldName))
        fieldName = "MetaData.LastUpdatedTime";
    }

    if (isNaN(Date.parse(dateFilter.startDate))) {
      throw new ApiException(ErrorCodes.INVALID_DATE);
    }

    if (dateFilter.endDate) {
      if (isNaN(Date.parse(dateFilter.endDate))) {
        throw new ApiException(ErrorCodes.INVALID_DATE);
      }
      query += ` WHERE ${fieldName} >= '${dateFilter.startDate}' AND ${fieldName} <= '${dateFilter.endDate}'`;
    } else {
      query += ` WHERE ${fieldName} >= '${dateFilter.startDate}'`;
    }
  }

  // Add pagination parameters if not getting total count
  if (!getTotalCount) {
    // Only add STARTPOSITION and MAXRESULTS if not counting
    if (startPosition !== undefined) {
      query += ` STARTPOSITION ${startPosition}`;
    }

    if (maxResults !== undefined) {
      query += ` MAXRESULTS ${maxResults}`;
    }
  }

  return query;
};

// Axios client
const qboClient = createApiClient(QBO_API_BASE_URL);

// Response interceptor
qboClient.interceptors.response.use(
  (response: any) => response,
  (error: any) => {
    const resData = error.response?.data;
    const faultType =
      resData?.Fault?.type ||
      resData?.fault?.type ||
      resData?.Fault?.Type ||
      resData?.fault?.Type;

    if (faultType === "AUTHENTICATION" || faultType === "AuthorizationFault") {
      throw new ApiException(ErrorCodes.INVALID_TOKEN);
    }

    const qboFaultError =
      resData?.Fault?.Error?.[0] ||
      resData?.fault?.error?.[0] ||
      resData?.Fault?.Error ||
      resData?.fault?.Error;

    if (qboFaultError) {
      const errorDetail = Array.isArray(qboFaultError)
        ? qboFaultError[0]
        : qboFaultError;
      throw new ApiException({
        status: error.response.status,
        code: resData?.code || ErrorCodes.INTERNAL_ERROR.code,
        errorDescription:
          errorDetail.detail ||
          errorDetail.Detail ||
          errorDetail.Message ||
          errorDetail.message ||
          "QBO service request failed",
        message:
          errorDetail.Message ||
          errorDetail.message ||
          resData?.error ||
          error.message,
      });
    }

    return Promise.reject(error);
  }
);

// Factory
export const createQboService = (): QboApiService => {
  return {
    async get(entity, { accessToken, companyId, queryParams = {} }) {
      logger.info(
        `QBO GET request | entity: ${entity} | companyId: ${companyId}`
      );

      const getTotalCount = queryParams.totalCount === true; // Check for totalCount

      // Parse pagination parameters
      let startPosition: number | undefined = undefined;
      if (queryParams.startPosition !== undefined) {
        startPosition = parseInt(queryParams.startPosition as string, 10);
        if (isNaN(startPosition) || startPosition < 1) {
          throw new ApiException({
            ...ErrorCodes.BAD_REQUEST,
            errorDescription: "startPosition must be a positive integer",
          });
        }
      }

      // Set default maxResults to 1000 if not provided
      let maxResults: number = 1000; // Default value
      if (queryParams.maxResults !== undefined) {
        const parsedMaxResults = parseInt(queryParams.maxResults as string, 10);
        if (isNaN(parsedMaxResults) || parsedMaxResults < 1) {
          throw new ApiException({
            ...ErrorCodes.BAD_REQUEST,
            errorDescription: "maxResults must be a positive integer",
          });
        }
        maxResults = parsedMaxResults;
      }

      // Build query with date filter, total count flag, and pagination
      const query = buildDateQuery(
        entity,
        queryParams.dateFilter,
        getTotalCount,
        startPosition,
        maxResults
      );
      const url = `/${companyId}/query`;

      logger.info(`QBO query: ${query}`);

      const response = await qboClient.get(url, {
        params: {
          query: query,
          minorversion: defaultMinorVersion,
        },
        headers: getQboAuthHeaders(accessToken),
      });

      if (getTotalCount) {
        const totalCount = response.data?.QueryResponse?.totalCount;
        if (typeof totalCount === "number") {
          return {
            data: { totalCount: totalCount },
            message: `Successfully fetched total count for ${entity}`,
          };
        } else {
          // Handle cases where totalCount might not be directly available or is malformed
          logger.warn(
            `Could not find totalCount in QBO response for entity: ${entity}`
          );
          return {
            totalCount: 0, // Default to 0 if not found
            message: `Total count not found for ${entity}`,
          };
        }
      } else {
        const responseDataObj = response?.data?.QueryResponse;

        if (!responseDataObj) {
          return {
            data: {
              Account: [],
            },
            message: `No ${entity} records found`,
          };
        }

        if (!responseDataObj.Account) {
          responseDataObj.Account = [];
        }

        return {
          data: responseDataObj,
          message: `Successfully fetched ${
            responseDataObj?.Account?.length || 0
          } ${entity} record(s)`,
        };
      }
    },
  };
};
export const qboService = createQboService();
