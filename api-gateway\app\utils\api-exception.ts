// src/utils/ApiException.ts
export default class ApiException extends Error {
  status?: number;
  code?: number;
  errorDescription?: string;

  constructor({
    status,
    code,
    message,
    errorDescription,
  }: {
    status?: number;
    code?: number;
    message?: string;
    errorDescription?: string;
  }) {
    super(message);
    this.status = status;
    this.code = code;
    this.errorDescription = errorDescription ?? message;
  }
}
