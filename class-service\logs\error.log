{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"POST /api/service?service=qbo&entity=bill: Bad request","stack":"Error: Bad request\n    at validateAuthParams (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:40:11)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:57:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:51:52\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 11:42:34:4234"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 12:15:09:159"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 12:56:33:5633"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:00:38:038"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:01:43:143"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:02:41:241"}
{"code":105,"errorDescription":"Request failed with status code 400","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:03:05:35"}
{"code":105,"errorDescription":"Invalid Reference Id","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","stack":"Error: Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:05:10:510"}
{"code":105,"errorDescription":"Invalid Reference Id : Customer assigned to this transaction has been deleted. Before you can modify this transaction, you must restore Gurshaan Test 1 (deleted).","level":"error","message":"POST /api/service?service=qbo&entity=payment: Invalid Reference Id","stack":"Error: Invalid Reference Id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\payment-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 13:07:22:722"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 14:16:27:1627"}
{"code":105,"errorDescription":"Title, GivenName, MiddleName, FamilyName, DisplayName, Suffix - one of these must be non-empty.","level":"error","message":"POST /api/service?service=qbo&entity=vendor: No name provided","stack":"Error: No name provided\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\vendor-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 14:35:31:3531"}
{"code":105,"errorDescription":"The name supplied already exists. : Id=562017","level":"error","message":"POST /api/service?service=qbo&entity=class: Duplicate Name Exists Error","stack":"Error: Duplicate Name Exists Error\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 15:35:16:3516"}
{"code":105,"errorDescription":"The name supplied already exists. : Id=562017","level":"error","message":"POST /api/service?service=qbo&entity=class: Duplicate Name Exists Error","stack":"Error: Duplicate Name Exists Error\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-05-27 15:35:27:3527"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'class' or 'CLASS'","level":"error","message":"GET /api/service?service=qbo&entity=cla: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:39:10:3910"}
{"code":102,"errorDescription":"Invalid service: qb. Supported: qbo","level":"error","message":"GET /api/service?service=qb&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:14:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:39:27:3927"}
{"code":102,"errorDescription":"Invalid service: . Supported: qbo","level":"error","message":"GET /api/service?service=&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:14:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:39:45:3945"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:40:02:402"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:40:11:4011"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:40:47:4047"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:40:51:4051"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:40:52:4052"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:41:03:413"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:42:22:4222"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:43:00:430"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:41:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:44:40:4440"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:41:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:45:17:4517"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:45:44:4544"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:41:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:46:11:4611"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:41:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:46:16:4616"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:46:25:4625"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:48:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:47:52:4752"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:46:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:52:58:5258"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:46:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:53:23:5323"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:42:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:55:04:554"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:59:32:5932"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=QBO&entity=CLASS: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\app\\services\\qboApiServices\\index.ts:49:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\ZACT\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:59:39:5939"}
{"code":105,"errorDescription":"QueryValidationError: Property createdDate not found for Entity Class","level":"error","message":"GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class: Invalid query","stack":"Error: Invalid query\n    at D:\\Zact_New_Arch\\class-service\\app\\services\\qboApiServices\\index.ts:107:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 17:51:31:5131"}
{"code":105,"errorDescription":"QueryValidationError: Property createdDate not found for Entity Class","level":"error","message":"GET /api/service?service=qbo&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12&entity=class: Invalid query","stack":"Error: Invalid query\n    at D:\\Zact_New_Arch\\class-service\\app\\services\\qboApiServices\\index.ts:107:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\class-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 17:52:16:5216"}
