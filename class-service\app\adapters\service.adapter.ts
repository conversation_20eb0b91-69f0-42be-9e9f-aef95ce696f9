import { qboService } from "../services/qboApiServices";
import ApiException from "../utils/api-exception";
import { ErrorCodes } from "../utils/response";

interface ServiceRequest {
  service: string;
  entity: string;
  requestType: "get";
  accessToken: string;
  companyId: string;
  queryParams?: any; // for GET query params
}

export const processServiceRequest = async (request: ServiceRequest) => {
  const { service, entity, requestType, queryParams, accessToken, companyId } =
    request;

  let serviceHandler;

  switch (service.toLowerCase()) {
    case "qbo":
      serviceHandler = qboService;
      break;
    default:
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: `Unsupported service: ${service}`,
      });
  }
  if (requestType === "get") {
    return await serviceHandler.get(entity, {
      accessToken,
      companyId,
      queryParams,
    });
  } else {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: `Unsupported request type: ${requestType}`,
    });
  }
};
