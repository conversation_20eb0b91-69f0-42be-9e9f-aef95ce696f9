#!/bin/bash

# ZACT Deployment Checker - For Beginners
# This script checks if your deployment is working correctly

echo "🔍 ZACT Deployment Health Check"
echo "================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# Check if kubectl is available
echo -e "${YELLOW}1. Checking if kubectl is installed...${NC}"
if command -v kubectl &> /dev/null; then
    KUBECTL_VERSION=$(kubectl version --client --short 2>/dev/null)
    echo -e "   ${GREEN}✅ kubectl is installed: $KUBECTL_VERSION${NC}"
else
    echo -e "   ${RED}❌ kubectl not found. Please install Docker Desktop with Kubernetes enabled.${NC}"
    exit 1
fi

# Check if He<PERSON> is available
echo -e "${YELLOW}2. Checking if He<PERSON> is installed...${NC}"
if command -v helm &> /dev/null; then
    HELM_VERSION=$(helm version --short 2>/dev/null)
    echo -e "   ${GREEN}✅ Helm is installed: $HELM_VERSION${NC}"
else
    echo -e "   ${RED}❌ Helm not found. Please install Helm first.${NC}"
    exit 1
fi

# Check if Kubernetes cluster is running
echo -e "${YELLOW}3. Checking Kubernetes cluster...${NC}"
if kubectl get nodes --no-headers &> /dev/null; then
    echo -e "   ${GREEN}✅ Kubernetes cluster is running${NC}"
else
    echo -e "   ${RED}❌ Kubernetes cluster not accessible. Start Docker Desktop.${NC}"
    exit 1
fi

# Check if zact-dev namespace exists
echo -e "${YELLOW}4. Checking ZACT deployment...${NC}"
if kubectl get namespace zact-dev --no-headers &> /dev/null; then
    echo -e "   ${GREEN}✅ zact-dev namespace exists${NC}"
else
    echo -e "   ${YELLOW}⚠️  zact-dev namespace not found. Run deployment first.${NC}"
    echo -e "      ${GRAY}Command: helm install zact-dev . -f environments/development.yaml --namespace zact-dev --create-namespace${NC}"
    exit 0
fi

# Check Helm deployment
echo -e "${YELLOW}5. Checking Helm deployment status...${NC}"
if helm status zact-dev -n zact-dev --short 2>/dev/null | grep -q "deployed"; then
    echo -e "   ${GREEN}✅ Helm deployment is active${NC}"
else
    echo -e "   ${RED}❌ Helm deployment not found or failed${NC}"
fi

# Check pod status
echo -e "${YELLOW}6. Checking pod status...${NC}"
PODS=$(kubectl get pods -n zact-dev --no-headers 2>/dev/null)
if [ ! -z "$PODS" ]; then
    RUNNING_PODS=$(echo "$PODS" | grep -c "Running")
    TOTAL_PODS=$(echo "$PODS" | wc -l)
    
    echo -e "   ${CYAN}📊 Pod Status: $RUNNING_PODS/$TOTAL_PODS running${NC}"
    
    if [ "$RUNNING_PODS" -eq "$TOTAL_PODS" ] && [ "$TOTAL_PODS" -eq 7 ]; then
        echo -e "   ${GREEN}✅ All 7 services are running!${NC}"
    elif [ "$RUNNING_PODS" -eq "$TOTAL_PODS" ]; then
        echo -e "   ${GREEN}✅ All pods are running (expected 7, found $TOTAL_PODS)${NC}"
    else
        echo -e "   ${YELLOW}⚠️  Some pods are not ready yet. Wait a few minutes and try again.${NC}"
        echo ""
        echo -e "   ${GRAY}Pod Details:${NC}"
        kubectl get pods -n zact-dev
    fi
else
    echo -e "   ${RED}❌ No pods found in zact-dev namespace${NC}"
fi

# Check services
echo -e "${YELLOW}7. Checking services...${NC}"
SERVICES=$(kubectl get services -n zact-dev --no-headers 2>/dev/null)
if [ ! -z "$SERVICES" ]; then
    SERVICE_COUNT=$(echo "$SERVICES" | wc -l)
    echo -e "   ${GREEN}✅ Found $SERVICE_COUNT services${NC}"
    
    # Check API Gateway specifically
    if kubectl get service zact-dev-api-gateway -n zact-dev --no-headers &> /dev/null; then
        echo -e "   ${GREEN}✅ API Gateway service is available${NC}"
    fi
else
    echo -e "   ${RED}❌ No services found${NC}"
fi

# Test API Gateway connectivity
echo -e "${YELLOW}8. Testing API Gateway...${NC}"
if curl -s --connect-timeout 5 http://localhost:8000/health &> /dev/null; then
    echo -e "   ${GREEN}✅ API Gateway is responding at http://localhost:8000${NC}"
else
    echo -e "   ${YELLOW}⚠️  Cannot reach API Gateway at localhost:8000${NC}"
    echo -e "      ${GRAY}Try: kubectl port-forward service/zact-dev-api-gateway 8000:8000 -n zact-dev${NC}"
fi

echo ""
echo -e "${CYAN}🎉 Health Check Complete!${NC}"
echo ""
echo -e "${NC}📋 Quick Commands:${NC}"
echo -e "   ${GRAY}View pods:     kubectl get pods -n zact-dev${NC}"
echo -e "   ${GRAY}View services: kubectl get services -n zact-dev${NC}"
echo -e "   ${GRAY}View logs:     kubectl logs <pod-name> -n zact-dev${NC}"
echo -e "   ${GRAY}Port forward:  kubectl port-forward service/zact-dev-api-gateway 8000:8000 -n zact-dev${NC}"
echo ""
