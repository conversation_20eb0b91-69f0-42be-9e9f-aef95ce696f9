// src/middlewares/asyncHandler.ts
import { Request, Response, NextFunction } from "express";
import { validationResult } from "express-validator";
import ApiException from "./api-exception";
import { BaseResponse, ErrorCodes } from "../utils/response";

const asyncHandler =
  (
    callback: (req: Request, res: Response, next: NextFunction) => Promise<any>
  ) =>
  async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    const errors = validationResult(req);

    try {
      if (!errors.isEmpty()) {
        throw new ApiException({
          ...ErrorCodes.BAD_REQUEST,
          errorDescription: errors
            .array()
            .map((e: any) => `${e.msg}`)
            .join(", "),
        });
      }

      const result = await callback(req, res, next);

      if (!res.headersSent) {
        res.status(200).json(BaseResponse(result));
      }

      return result;
    } catch (error) {
      next(error);
    }
  };

export default asyncHandler;
