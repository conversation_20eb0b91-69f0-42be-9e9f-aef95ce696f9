0{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 11:35:37:3537"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: bcd74e14f27155c8fb4388314af0fbcb ","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"2025-05-27T06:08:20.541Z  GET qbo bill | companyId=*********5356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: *********5356807410","timestamp":"2025-05-27 11:38:20:3820"}
{"level":"info","message":"Response sent with status: 200 - MessageID: bcd74e14f27155c8fb4388314af0fbcb","timestamp":"2025-05-27 11:38:23:3823"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: 1df82a54c249516e5c981c84874caace ","timestamp":"2025-05-27 11:42:34:4234"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"POST /api/service?service=qbo&entity=bill: Bad request","stack":"Error: Bad request\n    at validateAuthParams (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:40:11)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:57:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\bill-service\\app\\router\\api.routes.ts:51:52\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\bill-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1df82a54c249516e5c981c84874caace","timestamp":"2025-05-27 11:42:34:4234"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 12:11:37:1137"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=bill - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313 ","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"2025-05-27T06:42:11.889Z  POST qbo bill | companyId=*********5356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"QBO POST request for entity: bill | companyId: *********5356807410","timestamp":"2025-05-27 12:12:11:1211"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a3d88ad879fbf4dbdefb50f2cd96b313","timestamp":"2025-05-27 12:12:15:1215"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=bill - MessageID: 68e79fd371f2326cb390a307c4cbf901 ","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"2025-05-27T06:45:07.909Z  GET qbo bill | companyId=*********5356807410","timestamp":"2025-05-27 12:15:07:157"}
{"level":"info","message":"QBO GET request for entity: bill | companyId: *********5356807410","timestamp":"2025-05-27 12:15:07:157"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=bill: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\bill-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\bill-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 68e79fd371f2326cb390a307c4cbf901","timestamp":"2025-05-27 12:15:09:159"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:07:25:725"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:12:45:1245"}
{"level":"info","message":"Request received: GET /api/services/service?service=qbo&entity=account - MessageID: 121772413c12d439d3fb4b70dd47d8bb ","timestamp":"2025-05-27 13:12:47:1247"}
{"level":"error","message":"GET /api/services/service?service=qbo&entity=account: Path not found","stack":"Error: Path not found\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\account-service\\app\\utils\\error-handler.ts:54:22)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)","status":404,"timestamp":"2025-05-27 13:12:47:1247"}
{"level":"info","message":"Response sent with status: 404 - MessageID: 121772413c12d439d3fb4b70dd47d8bb","timestamp":"2025-05-27 13:12:47:1247"}
{"level":"info","message":"Request received: GET /api/services?service=qbo&entity=account - MessageID: 361c1167fca1d4422085a3507e050832 ","timestamp":"2025-05-27 13:13:14:1314"}
{"level":"error","message":"GET /api/services?service=qbo&entity=account: Path not found","stack":"Error: Path not found\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\account-service\\app\\utils\\error-handler.ts:54:22)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)","status":404,"timestamp":"2025-05-27 13:13:14:1314"}
{"level":"info","message":"Response sent with status: 404 - MessageID: 361c1167fca1d4422085a3507e050832","timestamp":"2025-05-27 13:13:14:1314"}
{"level":"info","message":"Request received: GET /api/services/service?service=qbo&entity=account - MessageID: bb879e4c542415a9b8c7d920fa92505f ","timestamp":"2025-05-27 13:13:24:1324"}
{"level":"error","message":"GET /api/services/service?service=qbo&entity=account: Path not found","stack":"Error: Path not found\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\account-service\\app\\utils\\error-handler.ts:54:22)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)\n    at C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:297:9\n    at processParams (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:582:12)\n    at next (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:291:5)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:186:3)\n    at router (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\router\\index.js:342:13)","status":404,"timestamp":"2025-05-27 13:13:24:1324"}
{"level":"info","message":"Response sent with status: 404 - MessageID: bb879e4c542415a9b8c7d920fa92505f","timestamp":"2025-05-27 13:13:24:1324"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 5e292c136eee4ec21f46fb76b7b4b33d ","timestamp":"2025-05-27 13:14:16:1416"}
{"level":"info","message":"2025-05-27T07:44:16.444Z  GET qbo account","timestamp":"2025-05-27 13:14:16:1416"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 13:14:16:1416"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\Desktop\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\Desktop\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 13:14:18:1418"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 5e292c136eee4ec21f46fb76b7b4b33d","timestamp":"2025-05-27 13:14:18:1418"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 5ae38e54cb74c65a320e37d557e47a8c ","timestamp":"2025-05-27 13:16:23:1623"}
{"level":"info","message":"2025-05-27T07:46:23.552Z  GET qbo account","timestamp":"2025-05-27 13:16:23:1623"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 13:16:23:1623"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 5ae38e54cb74c65a320e37d557e47a8c","timestamp":"2025-05-27 13:16:25:1625"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 13:18:41:1841"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 169cebeb55088f695e77aed1ef67246f ","timestamp":"2025-05-27 13:18:48:1848"}
{"level":"info","message":"2025-05-27T07:48:48.054Z  GET qbo account","timestamp":"2025-05-27 13:18:48:1848"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 13:18:48:1848"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 169cebeb55088f695e77aed1ef67246f","timestamp":"2025-05-27 13:18:49:1849"}
{"level":"info","message":"Server is listening on port 8006","timestamp":"2025-05-27 14:59:52:5952"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 15:27:42:2742"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 562c85d72e43cfaecb29e6f711ab931d ","timestamp":"2025-05-27 15:27:56:2756"}
{"level":"info","message":"2025-05-27T09:57:56.451Z  GET qbo account","timestamp":"2025-05-27 15:27:56:2756"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:27:56:2756"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 562c85d72e43cfaecb29e6f711ab931d","timestamp":"2025-05-27 15:27:58:2758"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 5c3b7c7c5cd63254b2a604a4d4cc4878 ","timestamp":"2025-05-27 15:34:38:3438"}
{"level":"info","message":"2025-05-27T10:04:38.249Z  GET qbo account","timestamp":"2025-05-27 15:34:38:3438"}
{"level":"info","message":"QBO GET request for entity: account | companyId: **************","timestamp":"2025-05-27 15:34:38:3438"}
{"code":105,"errorDescription":"AuthorizationFailure: {0}, statusCode: {1}","level":"error","message":"GET /api/service?service=qbo&entity=account: Authorization Failure","stack":"Error: Authorization Failure\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:47:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:34:39:3439"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 5c3b7c7c5cd63254b2a604a4d4cc4878","timestamp":"2025-05-27 15:34:39:3439"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 8a1766826965ac86d9e754cfe7ce3d97 ","timestamp":"2025-05-27 15:34:49:3449"}
{"level":"info","message":"2025-05-27T10:04:49.466Z  GET qbo account","timestamp":"2025-05-27 15:34:49:3449"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:34:49:3449"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8a1766826965ac86d9e754cfe7ce3d97","timestamp":"2025-05-27 15:34:50:3450"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 137b16133939cd884c15d412fcb19d76 ","timestamp":"2025-05-27 15:35:20:3520"}
{"level":"info","message":"2025-05-27T10:05:20.057Z  GET qbo account","timestamp":"2025-05-27 15:35:20:3520"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:35:20:3520"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:35:21:3521"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 137b16133939cd884c15d412fcb19d76","timestamp":"2025-05-27 15:35:21:3521"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 274ca2ebb7a0a699e22b6c55c4c96756 ","timestamp":"2025-05-27 15:35:26:3526"}
{"level":"info","message":"2025-05-27T10:05:26.882Z  GET qbo account","timestamp":"2025-05-27 15:35:26:3526"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:35:26:3526"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 274ca2ebb7a0a699e22b6c55c4c96756","timestamp":"2025-05-27 15:35:28:3528"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 61bf234f52c061bbb4de6ac01bbc3624 ","timestamp":"2025-05-27 15:35:39:3539"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:35:39:3539"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 61bf234f52c061bbb4de6ac01bbc3624","timestamp":"2025-05-27 15:35:39:3539"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: a28af0db713a70fa507a59aab392de85 ","timestamp":"2025-05-27 15:35:45:3545"}
{"level":"info","message":"2025-05-27T10:05:45.478Z  GET qbo account","timestamp":"2025-05-27 15:35:45:3545"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:35:45:3545"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a28af0db713a70fa507a59aab392de85","timestamp":"2025-05-27 15:35:47:3547"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 8018f421cbd60e13ffafd804d471cc48 ","timestamp":"2025-05-27 15:36:20:3620"}
{"level":"info","message":"2025-05-27T10:06:20.610Z  GET qbo account","timestamp":"2025-05-27 15:36:20:3620"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:36:20:3620"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8018f421cbd60e13ffafd804d471cc48","timestamp":"2025-05-27 15:36:21:3621"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=acc - MessageID: c831984d744349ee6c5c816d82fb4371 ","timestamp":"2025-05-27 15:36:26:3626"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=acc: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:36:26:3626"}
{"level":"info","message":"Response sent with status: 400 - MessageID: c831984d744349ee6c5c816d82fb4371","timestamp":"2025-05-27 15:36:26:3626"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: a4663bca016c37b63e91fa023881df33 ","timestamp":"2025-05-27 15:36:31:3631"}
{"level":"info","message":"2025-05-27T10:06:31.338Z  GET qbo account","timestamp":"2025-05-27 15:36:31:3631"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:36:31:3631"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a4663bca016c37b63e91fa023881df33","timestamp":"2025-05-27 15:36:33:3633"}
{"level":"info","message":"Request received: GET /api/service?service=q&entity=account - MessageID: c51dfbc37c36153cb149271b569b48c1 ","timestamp":"2025-05-27 15:36:38:3638"}
{"code":102,"errorDescription":"Invalid service: q. Supported: qbo","level":"error","message":"GET /api/service?service=q&entity=account: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:14:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:36:38:3638"}
{"level":"info","message":"Response sent with status: 400 - MessageID: c51dfbc37c36153cb149271b569b48c1","timestamp":"2025-05-27 15:36:38:3638"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 6ad6cf788822b4524d13824eafcc1c34 ","timestamp":"2025-05-27 15:36:45:3645"}
{"level":"info","message":"2025-05-27T10:06:45.003Z  GET qbo account","timestamp":"2025-05-27 15:36:45:3645"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:36:45:3645"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6ad6cf788822b4524d13824eafcc1c34","timestamp":"2025-05-27 15:36:46:3646"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 72855cd6daa077d0466fee3dfa201d1f ","timestamp":"2025-05-27 15:40:47:4047"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:40:47:4047"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 72855cd6daa077d0466fee3dfa201d1f","timestamp":"2025-05-27 15:40:47:4047"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e248074fbebf2aa272270284cf52f53a ","timestamp":"2025-05-27 15:41:18:4118"}
{"level":"info","message":"2025-05-27T10:11:18.167Z  GET qbo account","timestamp":"2025-05-27 15:41:18:4118"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:41:18:4118"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e248074fbebf2aa272270284cf52f53a","timestamp":"2025-05-27 15:41:19:4119"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: a1d294f25636dfc5e5b640d85d0e5eed ","timestamp":"2025-05-27 15:41:41:4141"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 15:41:41:4141"}
{"level":"info","message":"Response sent with status: 400 - MessageID: a1d294f25636dfc5e5b640d85d0e5eed","timestamp":"2025-05-27 15:41:41:4141"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 40ea2d819bc63cf0c52fb39da9257413 ","timestamp":"2025-05-27 15:41:44:4144"}
{"level":"info","message":"2025-05-27T10:11:44.990Z  GET qbo account","timestamp":"2025-05-27 15:41:44:4144"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:41:44:4144"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 40ea2d819bc63cf0c52fb39da9257413","timestamp":"2025-05-27 15:41:46:4146"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 83f8a5ad115498418144d5e993233d38 ","timestamp":"2025-05-27 15:55:53:5553"}
{"level":"info","message":"2025-05-27T10:25:53.296Z  GET qbo account","timestamp":"2025-05-27 15:55:53:5553"}
{"level":"info","message":"QBO GET request for entity: account | companyId: **************","timestamp":"2025-05-27 15:55:53:5553"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:55:55:5555"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 83f8a5ad115498418144d5e993233d38","timestamp":"2025-05-27 15:55:55:5555"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 27edd50c1b7680bd6bbed193bd98a8e1 ","timestamp":"2025-05-27 15:56:07:567"}
{"level":"info","message":"2025-05-27T10:26:07.482Z  GET qbo account","timestamp":"2025-05-27 15:56:07:567"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:56:07:567"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:56:08:568"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 27edd50c1b7680bd6bbed193bd98a8e1","timestamp":"2025-05-27 15:56:08:568"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 24647fde85dfcec1667ce465eb9935ac ","timestamp":"2025-05-27 15:56:13:5613"}
{"level":"info","message":"2025-05-27T10:26:13.178Z  GET qbo account","timestamp":"2025-05-27 15:56:13:5613"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:56:13:5613"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:56:13:5613"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 24647fde85dfcec1667ce465eb9935ac","timestamp":"2025-05-27 15:56:13:5613"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 37a78971b144b826bb0095d3bb083165 ","timestamp":"2025-05-27 15:56:20:5620"}
{"level":"info","message":"2025-05-27T10:26:20.363Z  GET qbo account","timestamp":"2025-05-27 15:56:20:5620"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:56:20:5620"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:56:21:5621"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 37a78971b144b826bb0095d3bb083165","timestamp":"2025-05-27 15:56:21:5621"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 40bc01fab00dae1c4743bb60d866e7f2 ","timestamp":"2025-05-27 15:56:45:5645"}
{"level":"info","message":"2025-05-27T10:26:45.589Z  GET qbo account","timestamp":"2025-05-27 15:56:45:5645"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:56:45:5645"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:56:46:5646"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 40bc01fab00dae1c4743bb60d866e7f2","timestamp":"2025-05-27 15:56:46:5646"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 01681a104bb2e6e12ea05cb7913762c9 ","timestamp":"2025-05-27 15:57:04:574"}
{"level":"info","message":"2025-05-27T10:27:04.907Z  GET qbo account","timestamp":"2025-05-27 15:57:04:574"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:57:04:574"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:40:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 15:57:06:576"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 01681a104bb2e6e12ea05cb7913762c9","timestamp":"2025-05-27 15:57:06:576"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: b9b0a271da8330b786e0ffdd14dc42d2 ","timestamp":"2025-05-27 15:59:19:5919"}
{"level":"info","message":"2025-05-27T10:29:19.508Z  GET qbo account","timestamp":"2025-05-27 15:59:19:5919"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 15:59:19:5919"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b9b0a271da8330b786e0ffdd14dc42d2","timestamp":"2025-05-27 15:59:21:5921"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 16:00:52:052"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 16:01:00:10"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 16:03:53:353"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 1b500f1b65ebc1d1b50ca969af00d532 ","timestamp":"2025-05-27 16:10:21:1021"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 16:10:21:1021"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1b500f1b65ebc1d1b50ca969af00d532","timestamp":"2025-05-27 16:10:21:1021"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: b7bd67e03bcbfba7ed8e39aaf0de3fbb ","timestamp":"2025-05-27 16:10:26:1026"}
{"level":"info","message":"2025-05-27T10:40:26.641Z  GET qbo account","timestamp":"2025-05-27 16:10:26:1026"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 16:10:26:1026"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b7bd67e03bcbfba7ed8e39aaf0de3fbb","timestamp":"2025-05-27 16:10:28:1028"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 061b94f15214582b5b69dbcc7fd732b7 ","timestamp":"2025-05-27 16:11:25:1125"}
{"level":"info","message":"2025-05-27T10:41:25.179Z  GET qbo account","timestamp":"2025-05-27 16:11:25:1125"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 16:11:25:1125"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:11:26:1126"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 061b94f15214582b5b69dbcc7fd732b7","timestamp":"2025-05-27 16:11:26:1126"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: d7b52ae93a1fe6d4bbd903c71ce2fcb8 ","timestamp":"2025-05-27 16:13:17:1317"}
{"level":"info","message":"2025-05-27T10:43:17.517Z  GET qbo account","timestamp":"2025-05-27 16:13:17:1317"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 16:13:17:1317"}
{"level":"info","message":"Response sent with status: 200 - MessageID: d7b52ae93a1fe6d4bbd903c71ce2fcb8","timestamp":"2025-05-27 16:13:18:1318"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: f235a931f0a5e78e041a03a7a0d539c9 ","timestamp":"2025-05-27 16:16:42:1642"}
{"level":"info","message":"2025-05-27T10:46:42.721Z  GET qbo account","timestamp":"2025-05-27 16:16:42:1642"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 16:16:42:1642"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:50:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:16:44:1644"}
{"level":"info","message":"Response sent with status: 500 - MessageID: f235a931f0a5e78e041a03a7a0d539c9","timestamp":"2025-05-27 16:16:44:1644"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e3ef83517793b77dc7de6c956cb4416a ","timestamp":"2025-05-27 16:17:10:1710"}
{"level":"info","message":"2025-05-27T10:47:10.315Z  GET qbo account","timestamp":"2025-05-27 16:17:10:1710"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 16:17:10:1710"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e3ef83517793b77dc7de6c956cb4416a","timestamp":"2025-05-27 16:17:12:1712"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 16:21:08:218"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 2935046c949be6140e623be48d6aa2c4 ","timestamp":"2025-05-27 16:21:13:2113"}
{"level":"info","message":"2025-05-27T10:51:13.498Z  GET qbo account","timestamp":"2025-05-27 16:21:13:2113"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 16:21:13:2113"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:51:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:21:15:2115"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 2935046c949be6140e623be48d6aa2c4","timestamp":"2025-05-27 16:21:15:2115"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 16:21:57:2157"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 58bd0204e0eb4f6546df8cbb1b50175e ","timestamp":"2025-05-27 16:21:59:2159"}
{"level":"info","message":"2025-05-27T10:51:59.611Z  GET qbo account","timestamp":"2025-05-27 16:21:59:2159"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 16:21:59:2159"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:22:01:221"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 58bd0204e0eb4f6546df8cbb1b50175e","timestamp":"2025-05-27 16:22:01:221"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 16:25:48:2548"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e3bdcda6ee94922e11fb6b3b9984c742 ","timestamp":"2025-05-27 16:25:52:2552"}
{"level":"info","message":"2025-05-27T10:55:52.306Z  GET qbo account","timestamp":"2025-05-27 16:25:52:2552"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 16:25:52:2552"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:53:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 16:25:53:2553"}
{"level":"info","message":"Response sent with status: 500 - MessageID: e3bdcda6ee94922e11fb6b3b9984c742","timestamp":"2025-05-27 16:25:53:2553"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 16:26:25:2625"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: b1fe96058be0c94da4886fa51eb150b1 ","timestamp":"2025-05-27 17:03:34:334"}
{"level":"info","message":"2025-05-27T11:33:34.882Z  GET qbo account","timestamp":"2025-05-27 17:03:34:334"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:03:34:334"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:03:36:336"}
{"level":"info","message":"Response sent with status: 401 - MessageID: b1fe96058be0c94da4886fa51eb150b1","timestamp":"2025-05-27 17:03:36:336"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 4bb27ba1ad209083367f21fbb9a0971e ","timestamp":"2025-05-27 17:05:50:550"}
{"level":"info","message":"2025-05-27T11:35:50.191Z  GET qbo account","timestamp":"2025-05-27 17:05:50:550"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:05:50:550"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:05:51:551"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 4bb27ba1ad209083367f21fbb9a0971e","timestamp":"2025-05-27 17:05:51:551"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e917f71595e47b5a0b8c07c922cc4c10 ","timestamp":"2025-05-27 17:05:59:559"}
{"level":"info","message":"2025-05-27T11:35:59.168Z  GET qbo account","timestamp":"2025-05-27 17:05:59:559"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:05:59:559"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:06:00:60"}
{"level":"info","message":"Response sent with status: 401 - MessageID: e917f71595e47b5a0b8c07c922cc4c10","timestamp":"2025-05-27 17:06:00:60"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 373abf61052ed5d3070a3f9057ef600b ","timestamp":"2025-05-27 17:06:05:65"}
{"level":"info","message":"2025-05-27T11:36:05.777Z  GET qbo account","timestamp":"2025-05-27 17:06:05:65"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:06:05:65"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 373abf61052ed5d3070a3f9057ef600b","timestamp":"2025-05-27 17:06:06:66"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 08f3633a52e4780062d9aece0e6ca14b ","timestamp":"2025-05-27 17:10:09:109"}
{"code":102,"errorDescription":"Missing required authentication: accessToken and companyId","level":"error","message":"GET /api/service?service=qbo&entity=account: Bad request","stack":"Error: Bad request\n    at validateAuthParams (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:40:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:92:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 17:10:09:109"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 08f3633a52e4780062d9aece0e6ca14b","timestamp":"2025-05-27 17:10:09:109"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 68101f58b0a85e9ae65d897f46acc10f ","timestamp":"2025-05-27 17:10:18:1018"}
{"level":"info","message":"2025-05-27T11:40:18.375Z  GET qbo account","timestamp":"2025-05-27 17:10:18:1018"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:10:18:1018"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 68101f58b0a85e9ae65d897f46acc10f","timestamp":"2025-05-27 17:10:20:1020"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 17:12:14:1214"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 3a6e008b50de303e65054d5ab15b96f7 ","timestamp":"2025-05-27 17:14:38:1438"}
{"level":"info","message":"2025-05-27T11:44:38.986Z  GET qbo account","timestamp":"2025-05-27 17:14:38:1438"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 17:14:38:1438"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:14:41:1441"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 3a6e008b50de303e65054d5ab15b96f7","timestamp":"2025-05-27 17:14:41:1441"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: f952b50d1aacb8deb3aa8e7e4729eacd ","timestamp":"2025-05-27 17:17:55:1755"}
{"level":"info","message":"2025-05-27T11:47:55.535Z  GET qbo account","timestamp":"2025-05-27 17:17:55:1755"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:17:55:1755"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:43:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-27 17:17:57:1757"}
{"level":"info","message":"Response sent with status: 401 - MessageID: f952b50d1aacb8deb3aa8e7e4729eacd","timestamp":"2025-05-27 17:17:57:1757"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 77f046b07e355d1d8c851ba686ab6cc9 ","timestamp":"2025-05-27 17:19:26:1926"}
{"level":"info","message":"2025-05-27T11:49:26.653Z  GET qbo account","timestamp":"2025-05-27 17:19:26:1926"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:19:26:1926"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 77f046b07e355d1d8c851ba686ab6cc9","timestamp":"2025-05-27 17:19:28:1928"}
{"level":"info","message":"Request received: GET /api/service?service=q&entity=account - MessageID: 7ea2a4ee7f11799a8a599839c390386c ","timestamp":"2025-05-27 17:20:12:2012"}
{"code":102,"errorDescription":"Invalid service: q. Supported: qbo","level":"error","message":"GET /api/service?service=q&entity=account: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:14:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 17:20:12:2012"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 7ea2a4ee7f11799a8a599839c390386c","timestamp":"2025-05-27 17:20:12:2012"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 01c5ab64e69cdf0798ab1574548f23f2 ","timestamp":"2025-05-27 17:22:03:223"}
{"level":"info","message":"2025-05-27T11:52:03.490Z  GET qbo account","timestamp":"2025-05-27 17:22:03:223"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:22:03:223"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 01c5ab64e69cdf0798ab1574548f23f2","timestamp":"2025-05-27 17:22:05:225"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=acc - MessageID: 9901fa81dfc50e0f7f6302dcb8d64b38 ","timestamp":"2025-05-27 17:22:18:2218"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=acc: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:28:11)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:91:5\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:4:12)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\router\\api.routes.ts:86:52\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-05-27 17:22:18:2218"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 9901fa81dfc50e0f7f6302dcb8d64b38","timestamp":"2025-05-27 17:22:18:2218"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 8d51c6399eb99c557d95c1d71fc3556e ","timestamp":"2025-05-27 17:24:05:245"}
{"level":"info","message":"2025-05-27T11:54:05.267Z  GET qbo account","timestamp":"2025-05-27 17:24:05:245"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:24:05:245"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8d51c6399eb99c557d95c1d71fc3556e","timestamp":"2025-05-27 17:24:06:246"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 4422f89510af7dc8b85df87c1e44e00a ","timestamp":"2025-05-27 17:30:42:3042"}
{"level":"info","message":"2025-05-27T12:00:42.299Z  GET qbo account","timestamp":"2025-05-27 17:30:42:3042"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 17:30:42:3042"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:30:44:3044"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 4422f89510af7dc8b85df87c1e44e00a","timestamp":"2025-05-27 17:30:44:3044"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 8fa803000fa639e1ba958bffd04004fb ","timestamp":"2025-05-27 17:31:15:3115"}
{"level":"info","message":"2025-05-27T12:01:15.085Z  GET qbo account","timestamp":"2025-05-27 17:31:15:3115"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:31:15:3115"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8fa803000fa639e1ba958bffd04004fb","timestamp":"2025-05-27 17:31:16:3116"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e224e4b1e204238a8492cd195521059f ","timestamp":"2025-05-27 17:37:16:3716"}
{"level":"info","message":"2025-05-27T12:07:16.270Z  GET qbo account","timestamp":"2025-05-27 17:37:16:3716"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 17:37:16:3716"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:37:17:3717"}
{"level":"info","message":"Response sent with status: 500 - MessageID: e224e4b1e204238a8492cd195521059f","timestamp":"2025-05-27 17:37:17:3717"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 34c717c4d78807079a324323131404dd ","timestamp":"2025-05-27 17:38:08:388"}
{"level":"info","message":"2025-05-27T12:08:08.414Z  GET qbo account","timestamp":"2025-05-27 17:38:08:388"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 17:38:08:388"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:38:09:389"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 34c717c4d78807079a324323131404dd","timestamp":"2025-05-27 17:38:09:389"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: b23fd5613850d260fc4279006afe2225 ","timestamp":"2025-05-27 17:38:14:3814"}
{"level":"info","message":"2025-05-27T12:08:14.108Z  GET qbo account","timestamp":"2025-05-27 17:38:14:3814"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:38:14:3814"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b23fd5613850d260fc4279006afe2225","timestamp":"2025-05-27 17:38:14:3814"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 329ccf347cdf1dc120a700d67aa94c25 ","timestamp":"2025-05-27 17:39:09:399"}
{"level":"info","message":"2025-05-27T12:09:09.703Z  GET qbo account","timestamp":"2025-05-27 17:39:09:399"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************GG","timestamp":"2025-05-27 17:39:09:399"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:39:11:3911"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 329ccf347cdf1dc120a700d67aa94c25","timestamp":"2025-05-27 17:39:11:3911"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: d3eb56a0615353bfb244533a71cc3b5b ","timestamp":"2025-05-27 17:40:31:4031"}
{"level":"info","message":"2025-05-27T12:10:31.157Z  GET qbo account","timestamp":"2025-05-27 17:40:31:4031"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:40:31:4031"}
{"level":"info","message":"Response sent with status: 200 - MessageID: d3eb56a0615353bfb244533a71cc3b5b","timestamp":"2025-05-27 17:40:33:4033"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 3fada9ab9386b29a35aae150da3ffca0 ","timestamp":"2025-05-27 17:55:37:5537"}
{"level":"info","message":"2025-05-27T12:25:37.793Z  GET qbo account","timestamp":"2025-05-27 17:55:37:5537"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 17:55:37:5537"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:55:39:5539"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 3fada9ab9386b29a35aae150da3ffca0","timestamp":"2025-05-27 17:55:39:5539"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: a8d930fc32b3895914af6ba5beb72d8f ","timestamp":"2025-05-27 17:55:43:5543"}
{"level":"info","message":"2025-05-27T12:25:43.444Z  GET qbo account","timestamp":"2025-05-27 17:55:43:5543"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:55:43:5543"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a8d930fc32b3895914af6ba5beb72d8f","timestamp":"2025-05-27 17:55:43:5543"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 58d859bcc19c3c7e254628e208dd025a ","timestamp":"2025-05-27 17:56:14:5614"}
{"level":"info","message":"2025-05-27T12:26:14.934Z  GET qbo account","timestamp":"2025-05-27 17:56:14:5614"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************hh","timestamp":"2025-05-27 17:56:14:5614"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:56:16:5616"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 58d859bcc19c3c7e254628e208dd025a","timestamp":"2025-05-27 17:56:16:5616"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 716b9662896ffa7f3c396e60dee63447 ","timestamp":"2025-05-27 17:56:24:5624"}
{"level":"info","message":"2025-05-27T12:26:24.013Z  GET qbo account","timestamp":"2025-05-27 17:56:24:5624"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 17:56:24:5624"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:56:25:5625"}
{"level":"info","message":"Response sent with status: 500 - MessageID: 716b9662896ffa7f3c396e60dee63447","timestamp":"2025-05-27 17:56:25:5625"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: eb081fcee17fcde05c90634ff6290b25 ","timestamp":"2025-05-27 17:56:35:5635"}
{"level":"info","message":"2025-05-27T12:26:35.962Z  GET qbo account","timestamp":"2025-05-27 17:56:35:5635"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:56:35:5635"}
{"level":"info","message":"Response sent with status: 200 - MessageID: eb081fcee17fcde05c90634ff6290b25","timestamp":"2025-05-27 17:56:37:5637"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 17:57:07:577"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: c4b44521dcd3ae9311f123c4602b47b4 ","timestamp":"2025-05-27 17:57:10:5710"}
{"level":"info","message":"2025-05-27T12:27:10.649Z  GET qbo account","timestamp":"2025-05-27 17:57:10:5710"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************hh","timestamp":"2025-05-27 17:57:10:5710"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:52:17\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 17:57:12:5712"}
{"level":"info","message":"Response sent with status: 500 - MessageID: c4b44521dcd3ae9311f123c4602b47b4","timestamp":"2025-05-27 17:57:12:5712"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: b4666c5eb3e10abbb52e18b807762f61 ","timestamp":"2025-05-27 17:57:18:5718"}
{"level":"info","message":"2025-05-27T12:27:18.307Z  GET qbo account","timestamp":"2025-05-27 17:57:18:5718"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 17:57:18:5718"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b4666c5eb3e10abbb52e18b807762f61","timestamp":"2025-05-27 17:57:20:5720"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 18:01:49:149"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 18:02:01:21"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 18:02:39:239"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 18:02:51:251"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 6513510f724633ce8274bd2b67612be6 ","timestamp":"2025-05-27 18:02:55:255"}
{"level":"info","message":"2025-05-27T12:32:55.696Z  GET qbo account","timestamp":"2025-05-27 18:02:55:255"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 18:02:55:255"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6513510f724633ce8274bd2b67612be6","timestamp":"2025-05-27 18:02:57:257"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-27 18:03:28:328"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e0df393f8e376b7a5e055454ae1f1009 ","timestamp":"2025-05-27 18:05:12:512"}
{"level":"info","message":"2025-05-27T12:35:12.041Z  GET qbo account","timestamp":"2025-05-27 18:05:12:512"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************gg","timestamp":"2025-05-27 18:05:12:512"}
{"code":105,"errorDescription":"System Failure Error: {0}","level":"error","message":"GET /api/service?service=qbo&entity=account: An application error has occurred while processing your request","stack":"Error: An application error has occurred while processing your request\n    at C:\\Users\\<USER>\\zact\\account-service\\app\\services\\qboApiServices\\index.ts:58:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (C:\\Users\\<USER>\\zact\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-05-27 18:05:13:513"}
{"level":"info","message":"Response sent with status: 500 - MessageID: e0df393f8e376b7a5e055454ae1f1009","timestamp":"2025-05-27 18:05:13:513"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: f05c0f581485e0872277f14a539a20bd ","timestamp":"2025-05-27 18:05:32:532"}
{"level":"info","message":"2025-05-27T12:35:32.025Z  GET qbo account","timestamp":"2025-05-27 18:05:32:532"}
{"level":"info","message":"QBO GET request for entity: account | companyId: ****************","timestamp":"2025-05-27 18:05:32:532"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f05c0f581485e0872277f14a539a20bd","timestamp":"2025-05-27 18:05:33:533"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 18:18:17:1817"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: f8af1f734c418ec165af7332e7928e32 ","timestamp":"2025-05-30 18:29:00:290"}
{"level":"info","message":"2025-05-30T12:59:00.412Z  GET qbo account","timestamp":"2025-05-30 18:29:00:290"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 18:29:00:290"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:234:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-30 18:29:03:293"}
{"level":"info","message":"Response sent with status: 401 - MessageID: f8af1f734c418ec165af7332e7928e32","timestamp":"2025-05-30 18:29:03:293"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e66588820e33216e07ad05d50e6698ba ","timestamp":"2025-05-30 18:35:00:350"}
{"level":"info","message":"2025-05-30T13:05:00.199Z  GET qbo account","timestamp":"2025-05-30 18:35:00:350"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 18:35:00:350"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:234:15\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-30 18:35:03:353"}
{"level":"info","message":"Response sent with status: 401 - MessageID: e66588820e33216e07ad05d50e6698ba","timestamp":"2025-05-30 18:35:03:353"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 410b308a96881bc8b4b4f60ada0d0afe ","timestamp":"2025-05-30 18:36:16:3616"}
{"level":"info","message":"2025-05-30T13:06:16.293Z  GET qbo account","timestamp":"2025-05-30 18:36:16:3616"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 18:36:16:3616"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 410b308a96881bc8b4b4f60ada0d0afe","timestamp":"2025-05-30 18:36:19:3619"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 18:37:10:3710"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: a3e96c19cc03ddf332116dc2f6b81f83 ","timestamp":"2025-05-30 18:37:15:3715"}
{"level":"info","message":"2025-05-30T13:07:15.877Z  GET qbo account","timestamp":"2025-05-30 18:37:15:3715"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 18:37:15:3715"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a3e96c19cc03ddf332116dc2f6b81f83","timestamp":"2025-05-30 18:37:19:3719"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 18:38:00:380"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: f4e0c97911865c3ecaf423157d28b5bf ","timestamp":"2025-05-30 18:38:05:385"}
{"level":"info","message":"2025-05-30T13:08:05.863Z  GET qbo account","timestamp":"2025-05-30 18:38:05:385"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 18:38:22:3822"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f4e0c97911865c3ecaf423157d28b5bf","timestamp":"2025-05-30 18:38:51:3851"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: c19066761dc6dea6e281ac5320573902 ","timestamp":"2025-05-30 18:38:55:3855"}
{"level":"info","message":"2025-05-30T13:08:55.558Z  GET qbo account","timestamp":"2025-05-30 18:38:55:3855"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 18:39:06:396"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 18:40:40:4040"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 18:40:57:4057"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 18:41:13:4113"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 18:41:28:4128"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 9ff81b19e99ddafbeef93e280e7340d3 ","timestamp":"2025-05-30 18:41:30:4130"}
{"level":"info","message":"2025-05-30T13:11:30.918Z  GET qbo account","timestamp":"2025-05-30 18:41:30:4130"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 18:41:30:4130"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 9ff81b19e99ddafbeef93e280e7340d3","timestamp":"2025-05-30 18:41:34:4134"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 18:59:20:5920"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:17:38:1738"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:18:03:183"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&fieldValue2&operator - MessageID: 1139604278daadb93fb647e1765a757f ","timestamp":"2025-05-30 19:19:36:1936"}
{"level":"info","message":"2025-05-30T13:49:36.298Z  GET qbo account","timestamp":"2025-05-30 19:19:36:1936"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:19:36:1936"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 1139604278daadb93fb647e1765a757f","timestamp":"2025-05-30 19:19:40:1940"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&fieldValue2&operator== - MessageID: 1adb275b8766fc52db60101e4e0e607e ","timestamp":"2025-05-30 19:19:44:1944"}
{"level":"info","message":"2025-05-30T13:49:44.720Z  GET qbo account","timestamp":"2025-05-30 19:19:44:1944"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:19:44:1944"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 1adb275b8766fc52db60101e4e0e607e","timestamp":"2025-05-30 19:19:45:1945"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&fieldValue2=AccountType&operator== - MessageID: 8d12d3325f80ee446489dafda120fe4c ","timestamp":"2025-05-30 19:19:53:1953"}
{"level":"info","message":"2025-05-30T13:49:53.630Z  GET qbo account","timestamp":"2025-05-30 19:19:53:1953"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:19:53:1953"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8d12d3325f80ee446489dafda120fe4c","timestamp":"2025-05-30 19:19:56:1956"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:20:11:2011"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&fieldValue2=AccountType&operator== - MessageID: e12f09620b7aebe12424f44d3f7e6c1b ","timestamp":"2025-05-30 19:20:13:2013"}
{"level":"info","message":"2025-05-30T13:50:13.925Z  GET qbo account","timestamp":"2025-05-30 19:20:13:2013"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:20:13:2013"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e12f09620b7aebe12424f44d3f7e6c1b","timestamp":"2025-05-30 19:20:17:2017"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=AccountType&fieldValue=true&fieldValue2=&operator== - MessageID: 7191fb357c41e35c15ec7860610db533 ","timestamp":"2025-05-30 19:21:13:2113"}
{"level":"info","message":"2025-05-30T13:51:13.971Z  GET qbo account","timestamp":"2025-05-30 19:21:13:2113"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:21:13:2113"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 7191fb357c41e35c15ec7860610db533","timestamp":"2025-05-30 19:21:17:2117"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: b702cee2b8a6b4601e6beb65bbaae3bb ","timestamp":"2025-05-30 19:21:37:2137"}
{"level":"info","message":"2025-05-30T13:51:37.751Z  GET qbo account","timestamp":"2025-05-30 19:21:37:2137"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:21:37:2137"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b702cee2b8a6b4601e6beb65bbaae3bb","timestamp":"2025-05-30 19:21:41:2141"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:22:11:2211"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e336ec4918d6d5fcea261bb6843084e5 ","timestamp":"2025-05-30 19:22:12:2212"}
{"level":"info","message":"2025-05-30T13:52:12.934Z  GET qbo account","timestamp":"2025-05-30 19:22:12:2212"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:22:12:2212"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e336ec4918d6d5fcea261bb6843084e5","timestamp":"2025-05-30 19:22:16:2216"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: 78860dd7fbf021c565ed7ce82fa7764d ","timestamp":"2025-05-30 19:22:43:2243"}
{"level":"info","message":"2025-05-30T13:52:43.826Z  GET qbo account","timestamp":"2025-05-30 19:22:43:2243"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:22:43:2243"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 78860dd7fbf021c565ed7ce82fa7764d","timestamp":"2025-05-30 19:22:47:2247"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: d333c9a67c7106e7c70203308d2c4f5e ","timestamp":"2025-05-30 19:25:53:2553"}
{"level":"info","message":"2025-05-30T13:55:53.811Z  GET qbo account","timestamp":"2025-05-30 19:25:53:2553"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:25:53:2553"}
{"level":"info","message":"Response sent with status: 200 - MessageID: d333c9a67c7106e7c70203308d2c4f5e","timestamp":"2025-05-30 19:25:57:2557"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:27:51:2751"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:28:16:2816"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: ba2067875c2c957b0a3a9e1d371915af ","timestamp":"2025-05-30 19:28:19:2819"}
{"level":"info","message":"2025-05-30T13:58:19.881Z  GET qbo account","timestamp":"2025-05-30 19:28:19:2819"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:28:19:2819"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ba2067875c2c957b0a3a9e1d371915af","timestamp":"2025-05-30 19:28:23:2823"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: f5b6599df73130fccb6b506d8f279035 ","timestamp":"2025-05-30 19:28:35:2835"}
{"level":"info","message":"2025-05-30T13:58:35.610Z  GET qbo account","timestamp":"2025-05-30 19:28:35:2835"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:28:35:2835"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f5b6599df73130fccb6b506d8f279035","timestamp":"2025-05-30 19:28:38:2838"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:29:23:2923"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: 8920d185f5fa5806f5777c84de21ab74 ","timestamp":"2025-05-30 19:29:25:2925"}
{"level":"info","message":"2025-05-30T13:59:25.802Z  GET qbo account","timestamp":"2025-05-30 19:29:25:2925"}
{"level":"info","message":"QBO GET request for entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:29:25:2925"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8920d185f5fa5806f5777c84de21ab74","timestamp":"2025-05-30 19:29:28:2928"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:31:27:3127"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:43:24:4324"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:43:52:4352"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: 19a1d45e7b6109729c7fa896e87857db ","timestamp":"2025-05-30 19:44:00:440"}
{"level":"info","message":"2025-05-30T14:14:00.066Z  GET qbo account","timestamp":"2025-05-30 19:44:00:440"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-05-30 19:44:00:440"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator==: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:210:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-05-30 19:44:03:443"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 19a1d45e7b6109729c7fa896e87857db","timestamp":"2025-05-30 19:44:03:443"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:45:17:4517"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:55:08:558"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-05-30 19:55:56:5556"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:22:31:2231"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: 1ed44e47d6efa27238cd665a9e1680a0 ","timestamp":"2025-06-02 11:23:10:2310"}
{"level":"info","message":"2025-06-02T05:53:10.955Z  GET qbo account","timestamp":"2025-06-02 11:23:10:2310"}
{"level":"info","message":"QBO GET request | entity: account | companyId: {{companyId}}","timestamp":"2025-06-02 11:23:10:2310"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator==: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:210:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 11:23:14:2314"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 1ed44e47d6efa27238cd665a9e1680a0","timestamp":"2025-06-02 11:23:14:2314"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: 3c46a8b2c8173a86f1261689c7f4adf1 ","timestamp":"2025-06-02 11:25:22:2522"}
{"level":"info","message":"2025-06-02T05:55:22.724Z  GET qbo account","timestamp":"2025-06-02 11:25:22:2522"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:25:22:2522"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 11:25:26:2526"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 3c46a8b2c8173a86f1261689c7f4adf1","timestamp":"2025-06-02 11:25:26:2526"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:26:58:2658"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: e5d3ea83a2068d344bfa4af1ec744041 ","timestamp":"2025-06-02 11:27:00:270"}
{"level":"info","message":"2025-06-02T05:57:00.081Z  GET qbo account","timestamp":"2025-06-02 11:27:00:270"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:27:00:270"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 11:27:02:272"}
{"level":"info","message":"Response sent with status: 400 - MessageID: e5d3ea83a2068d344bfa4af1ec744041","timestamp":"2025-06-02 11:27:02:272"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:27:32:2732"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:27:34:2734"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:27:36:2736"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: 8d941b80b112777f11fccb3a32a6ddce ","timestamp":"2025-06-02 11:27:40:2740"}
{"level":"info","message":"2025-06-02T05:57:40.101Z  GET qbo account","timestamp":"2025-06-02 11:27:40:2740"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:27:40:2740"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 8d941b80b112777f11fccb3a32a6ddce","timestamp":"2025-06-02 11:27:43:2743"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&operator== - MessageID: 3cb461342355661b29e263a66be4c376 ","timestamp":"2025-06-02 11:28:07:287"}
{"level":"info","message":"2025-06-02T05:58:07.196Z  GET qbo account","timestamp":"2025-06-02 11:28:07:287"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:28:07:287"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 3cb461342355661b29e263a66be4c376","timestamp":"2025-06-02 11:28:11:2811"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:29:19:2919"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&operator== - MessageID: e9cfcbfc036d28497865b78440439727 ","timestamp":"2025-06-02 11:29:23:2923"}
{"level":"info","message":"2025-06-02T05:59:23.071Z  GET qbo account","timestamp":"2025-06-02 11:29:23:2923"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:29:23:2923"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e9cfcbfc036d28497865b78440439727","timestamp":"2025-06-02 11:29:26:2926"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:29:57:2957"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&operator== - MessageID: e268f0d3927f8766688602e0f4f3ea6f ","timestamp":"2025-06-02 11:30:03:303"}
{"level":"info","message":"2025-06-02T06:00:03.162Z  GET qbo account","timestamp":"2025-06-02 11:30:03:303"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:30:03:303"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e268f0d3927f8766688602e0f4f3ea6f","timestamp":"2025-06-02 11:30:06:306"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:30:38:3038"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:30:43:3043"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&fieldValue2=true&operator== - MessageID: 743758002ff498767252488b0958167f ","timestamp":"2025-06-02 11:30:48:3048"}
{"level":"info","message":"2025-06-02T06:00:48.183Z  GET qbo account","timestamp":"2025-06-02 11:30:48:3048"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:30:48:3048"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 743758002ff498767252488b0958167f","timestamp":"2025-06-02 11:30:51:3051"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:32:08:328"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:32:14:3214"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&fieldValue2=true&operator== - MessageID: a722eb236ee52ba82bb679a16be9b34d ","timestamp":"2025-06-02 11:32:28:3228"}
{"level":"info","message":"2025-06-02T06:02:28.256Z  GET qbo account","timestamp":"2025-06-02 11:32:28:3228"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:32:28:3228"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"filter":{"fieldName":"Active","fieldValue":"false","fieldValue2":"true","operator":"="},"minorversion":"75","query":"SELECT * FROM account MAXRESULTS 1000"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/*********5356807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&fieldValue2=true&operator==: Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","pathname":"/v3/company/*********5356807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 02 Jun 2025 06:02:31 GMT","Content-Type","text/html;charset=utf-8","Content-Length","435","Connection","keep-alive","x-spanid","e1c2ba5c-2c04-6a97-38c6-08c532f464e7","x-amzn-trace-id","Root=1-683d3e76-6143511a49f1b7a15c7349f6","X-Content-Type-Options","nosniff","Content-Language","en","x-envoy-upstream-service-time","16","Server","istio-envoy","x-envoy-decorator-operation","v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","Strict-Transport-Security","max-age=********","intuit_tid","1-683d3e76-6143511a49f1b7a15c7349f6","x-request-id","1-683d3e76-6143511a49f1b7a15c7349f6"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"filter":{"fieldName":"Active","fieldValue":"false","fieldValue2":"true","operator":"="},"minorversion":"75","query":"SELECT * FROM account MAXRESULTS 1000"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/*********5356807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":"<!doctype html><html lang=\"en\"><head><title>HTTP Status 400 – Bad Request</title><style type=\"text/css\">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 400 – Bad Request</h1></body></html>","headers":{"connection":"keep-alive","content-language":"en","content-length":"435","content-type":"text/html;charset=utf-8","date":"Mon, 02 Jun 2025 06:02:31 GMT","intuit_tid":"1-683d3e76-6143511a49f1b7a15c7349f6","server":"istio-envoy","strict-transport-security":"max-age=********","x-amzn-trace-id":"Root=1-683d3e76-6143511a49f1b7a15c7349f6","x-content-type-options":"nosniff","x-envoy-decorator-operation":"v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","x-envoy-upstream-service-time":"16","x-request-id":"1-683d3e76-6143511a49f1b7a15c7349f6","x-spanid":"e1c2ba5c-2c04-6a97-38c6-08c532f464e7"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","pathname":"/v3/company/*********5356807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d0:2453:443","***********:443","64:ff9b::22d6:479a:443","************:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 02 Jun 2025 06:02:31 GMT","Content-Type","text/html;charset=utf-8","Content-Length","435","Connection","keep-alive","x-spanid","e1c2ba5c-2c04-6a97-38c6-08c532f464e7","x-amzn-trace-id","Root=1-683d3e76-6143511a49f1b7a15c7349f6","X-Content-Type-Options","nosniff","Content-Language","en","x-envoy-upstream-service-time","16","Server","istio-envoy","x-envoy-decorator-operation","v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","Strict-Transport-Security","max-age=********","intuit_tid","1-683d3e76-6143511a49f1b7a15c7349f6","x-request-id","1-683d3e76-6143511a49f1b7a15c7349f6"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"stack":"AxiosError: Request failed with status code 400\n    at settle (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:599:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 11:32:31:3231"}
{"level":"info","message":"Response sent with status: 400 - MessageID: a722eb236ee52ba82bb679a16be9b34d","timestamp":"2025-06-02 11:32:31:3231"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:33:29:3329"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:33:36:3336"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:34:00:340"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:34:07:347"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&fieldValue2=true&operator== - MessageID: f4735b2e2ca7b2c16aff5196debf85b0 ","timestamp":"2025-06-02 11:34:11:3411"}
{"level":"info","message":"2025-06-02T06:04:11.013Z  GET qbo account","timestamp":"2025-06-02 11:34:11:3411"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:34:11:3411"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"filter":{"fieldName":"Active","fieldValue":"false","fieldValue2":"true","operator":"="},"minorversion":"75","query":"SELECT * FROM account WHERE Active = false MAXRESULTS 1000"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/*********5356807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&fieldValue2=true&operator==: Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","pathname":"/v3/company/*********5356807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 02 Jun 2025 06:04:13 GMT","Content-Type","text/html;charset=utf-8","Content-Length","435","Connection","keep-alive","x-spanid","7ec0560e-e8dc-dfd2-55fc-3c55f1ef0904","x-amzn-trace-id","Root=1-683d3edd-42384e556336fa923a46ca4e","X-Content-Type-Options","nosniff","Content-Language","en","x-envoy-upstream-service-time","60","Server","istio-envoy","x-envoy-decorator-operation","v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","Strict-Transport-Security","max-age=********","intuit_tid","1-683d3edd-42384e556336fa923a46ca4e","x-request-id","1-683d3edd-42384e556336fa923a46ca4e"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"filter":{"fieldName":"Active","fieldValue":"false","fieldValue2":"true","operator":"="},"minorversion":"75","query":"SELECT * FROM account WHERE Active = false MAXRESULTS 1000"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/*********5356807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":"<!doctype html><html lang=\"en\"><head><title>HTTP Status 400 – Bad Request</title><style type=\"text/css\">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 400 – Bad Request</h1></body></html>","headers":{"connection":"keep-alive","content-language":"en","content-length":"435","content-type":"text/html;charset=utf-8","date":"Mon, 02 Jun 2025 06:04:13 GMT","intuit_tid":"1-683d3edd-42384e556336fa923a46ca4e","server":"istio-envoy","strict-transport-security":"max-age=********","x-amzn-trace-id":"Root=1-683d3edd-42384e556336fa923a46ca4e","x-content-type-options":"nosniff","x-envoy-decorator-operation":"v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","x-envoy-upstream-service-time":"60","x-request-id":"1-683d3edd-42384e556336fa923a46ca4e","x-spanid":"7ec0560e-e8dc-dfd2-55fc-3c55f1ef0904"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..w1Et7CDMR52pXIOaTg417w.5F3FuPwA8O5KR0wg0RtJnRTpKKCLmu6Kav36weGPElDj11GjDPZ7PyRTdvTjodfafL-g5iuju39Ze74fh0hPJtOZx9M_X6TVhy1SixhR3gg4IB20SCwuZEq-q5CBcsCfvpr2Mz-O7Bmb9KQJxQPSEhDnNNm-V7fES_3fQY505dMct751ns631RvHGI-Y7K4ZQEIKxuaN5MvTZRZUu-ldkgqQtAi2p9_BD3i5XXKpwmbOV23riumKi_7G4z41Fi4G6ezvFoJvIm9LzakNKDwshxNtMdi4ri3wumwq_oZMZmqI2UOv0Jv5GAgqjHhRGXbpQ8J_R0boH9AuH7szwxfmKrVQ5HWYA5k9PqH6oqn75dpdeoiNpNtgFGAjOC0DfAkt0_qZ6OPeSWwmTyPdsPy9XDwMviWFxKf5wK2iFHW7QaKlKonGMD_jJ-Tj3IaZcocas0fr6j6MXm9AH_an2kn_rGdUG0NBWeygJr3bkpJ_HdY.dqIvSi2gwzcHN4pZzg5dQw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","pathname":"/v3/company/*********5356807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","protocol":"https:","res":{"_consuming":false,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"newListener":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["64:ff9b::3428:382b:443","*************:443","64:ff9b::22d6:479a:443","************:443","64:ff9b::22d0:2453:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":false,"servername":"sandbox-quickbooks.api.intuit.com","ssl":null,"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Mon, 02 Jun 2025 06:04:13 GMT","Content-Type","text/html;charset=utf-8","Content-Length","435","Connection","keep-alive","x-spanid","7ec0560e-e8dc-dfd2-55fc-3c55f1ef0904","x-amzn-trace-id","Root=1-683d3edd-42384e556336fa923a46ca4e","X-Content-Type-Options","nosniff","Content-Language","en","x-envoy-upstream-service-time","60","Server","istio-envoy","x-envoy-decorator-operation","v3-facade-service-desired-service.dev-devx-v3facadeservice-usw2-stg-ids.svc.cluster.local:8090/*","Strict-Transport-Security","max-age=********","intuit_tid","1-683d3edd-42384e556336fa923a46ca4e","x-request-id","1-683d3edd-42384e556336fa923a46ca4e"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account+WHERE+Active+%3D+false+MAXRESULTS+1000&minorversion=75&filter[fieldName]=Active&filter[fieldValue]=false&filter[operator]=%3D&filter[fieldValue2]=true","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"stack":"AxiosError: Request failed with status code 400\n    at settle (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:599:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 11:34:13:3413"}
{"level":"info","message":"Response sent with status: 400 - MessageID: f4735b2e2ca7b2c16aff5196debf85b0","timestamp":"2025-06-02 11:34:13:3413"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:36:19:3619"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=false&fieldValue2=true&operator== - MessageID: 594247be6e39d811a693ba1e4d4ff9b3 ","timestamp":"2025-06-02 11:36:20:3620"}
{"level":"info","message":"2025-06-02T06:06:20.405Z  GET qbo account","timestamp":"2025-06-02 11:36:20:3620"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:36:20:3620"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 594247be6e39d811a693ba1e4d4ff9b3","timestamp":"2025-06-02 11:36:23:3623"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true&operator== - MessageID: b5b5cb0f61544090891d81a4b2775702 ","timestamp":"2025-06-02 11:38:18:3818"}
{"level":"info","message":"2025-06-02T06:08:18.530Z  GET qbo account","timestamp":"2025-06-02 11:38:18:3818"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:38:18:3818"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b5b5cb0f61544090891d81a4b2775702","timestamp":"2025-06-02 11:38:21:3821"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=Active&fieldValue=true - MessageID: e29de3613764d9c0ef2b004e47085f0b ","timestamp":"2025-06-02 11:38:43:3843"}
{"level":"info","message":"2025-06-02T06:08:43.718Z  GET qbo account","timestamp":"2025-06-02 11:38:43:3843"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:38:43:3843"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e29de3613764d9c0ef2b004e47085f0b","timestamp":"2025-06-02 11:38:46:3846"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: 3b10e8af3f89de049ebdf72de59e019b ","timestamp":"2025-06-02 11:40:31:4031"}
{"level":"info","message":"2025-06-02T06:10:31.179Z  GET qbo account","timestamp":"2025-06-02 11:40:31:4031"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:40:31:4031"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 3b10e8af3f89de049ebdf72de59e019b","timestamp":"2025-06-02 11:40:34:4034"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: 72c791065f2a4624619a02c474b3bbc5 ","timestamp":"2025-06-02 11:43:19:4319"}
{"level":"info","message":"2025-06-02T06:13:19.117Z  GET qbo account","timestamp":"2025-06-02 11:43:19:4319"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:43:19:4319"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 72c791065f2a4624619a02c474b3bbc5","timestamp":"2025-06-02 11:43:22:4322"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:43:53:4353"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:44:30:4430"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: b2da55dd90f890023c7f503677d127e8 ","timestamp":"2025-06-02 11:44:35:4435"}
{"level":"info","message":"2025-06-02T06:14:35.488Z  GET qbo account","timestamp":"2025-06-02 11:44:35:4435"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:44:35:4435"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b2da55dd90f890023c7f503677d127e8","timestamp":"2025-06-02 11:44:38:4438"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:44:51:4451"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:45:10:4510"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: 7e49c83d2af83adc3a957eb153d189cd ","timestamp":"2025-06-02 11:45:13:4513"}
{"level":"info","message":"2025-06-02T06:15:13.428Z  GET qbo account","timestamp":"2025-06-02 11:45:13:4513"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:45:13:4513"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 7e49c83d2af83adc3a957eb153d189cd","timestamp":"2025-06-02 11:45:16:4516"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:46:00:460"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:46:33:4633"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:46:42:4642"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:47:04:474"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:47:18:4718"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: 4b529a278a3ee54edbf5e13dbab0d9e6 ","timestamp":"2025-06-02 11:47:27:4727"}
{"level":"info","message":"2025-06-02T06:17:27.398Z  GET qbo account","timestamp":"2025-06-02 11:47:27:4727"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:47:27:4727"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 4b529a278a3ee54edbf5e13dbab0d9e6","timestamp":"2025-06-02 11:48:25:4825"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: b07b32f784973fefb5a49611cc694a3b ","timestamp":"2025-06-02 11:48:29:4829"}
{"level":"info","message":"2025-06-02T06:18:29.441Z  GET qbo account","timestamp":"2025-06-02 11:48:29:4829"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:48:29:4829"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b07b32f784973fefb5a49611cc694a3b","timestamp":"2025-06-02 11:48:32:4832"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:49:30:4930"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: 26362c08b3fc4e0f493aa27c6908e8b2 ","timestamp":"2025-06-02 11:49:32:4932"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:53:46:5346"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 11:54:03:543"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: ac8564d7dbb240c2d683bb91c066af30 ","timestamp":"2025-06-02 11:54:05:545"}
{"level":"info","message":"2025-06-02T06:24:05.498Z  GET qbo account","timestamp":"2025-06-02 11:54:05:545"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 11:54:05:545"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ac8564d7dbb240c2d683bb91c066af30","timestamp":"2025-06-02 11:54:31:5431"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:08:03:83"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&fieldValue2=2023-08-11&operator=between - MessageID: 39560c23939d8f03eaa70f58f5b4b5ec ","timestamp":"2025-06-02 12:09:02:92"}
{"level":"info","message":"2025-06-02T06:39:02.877Z  GET qbo account","timestamp":"2025-06-02 12:09:02:92"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:09:02:92"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 39560c23939d8f03eaa70f58f5b4b5ec","timestamp":"2025-06-02 12:09:06:96"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&operator=between - MessageID: 35f01d2e1506a739a7c8cace154db085 ","timestamp":"2025-06-02 12:11:00:110"}
{"level":"info","message":"2025-06-02T06:41:00.372Z  GET qbo account","timestamp":"2025-06-02 12:11:00:110"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:11:00:110"}
{"code":105,"errorDescription":"QueryParserError: Encountered \"between\" \"between\" at line 1, column 49","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&operator=between: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:220:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 12:11:03:113"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 35f01d2e1506a739a7c8cace154db085","timestamp":"2025-06-02 12:11:03:113"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:20:29:2029"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&operator=between - MessageID: 128aec5d6ddc1c22798924a452375145 ","timestamp":"2025-06-02 12:20:51:2051"}
{"level":"info","message":"2025-06-02T06:50:51.313Z  GET qbo account","timestamp":"2025-06-02 12:20:51:2051"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:20:51:2051"}
{"code":102,"errorDescription":"Operator 'between' requires a second value (fieldValue2)","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31&operator=between: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:138:15)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:319:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:20:51:2051"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 128aec5d6ddc1c22798924a452375145","timestamp":"2025-06-02 12:20:51:2051"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-31&fieldValue2=2023-08-11&operator=between - MessageID: 39a0b2015da4f3201e6988d28f7b3373 ","timestamp":"2025-06-02 12:21:08:218"}
{"level":"info","message":"2025-06-02T06:51:08.256Z  GET qbo account","timestamp":"2025-06-02 12:21:08:218"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:21:08:218"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 12:21:08:218"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-09-31' MAXRESULTS 1000","timestamp":"2025-06-02 12:21:08:218"}
{"code":105,"errorDescription":"QueryValidationError: value 2023-09-31 is not valid for property 'MetaData.CreateTime'","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-31&fieldValue2=2023-08-11&operator=between: Invalid query","stack":"Error: Invalid query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:287:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 12:21:11:2111"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 39a0b2015da4f3201e6988d28f7b3373","timestamp":"2025-06-02 12:21:11:2111"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=between - MessageID: 69a02f034eed14c6e4a1667393b4d2be ","timestamp":"2025-06-02 12:21:57:2157"}
{"level":"info","message":"2025-06-02T06:51:57.459Z  GET qbo account","timestamp":"2025-06-02 12:21:57:2157"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:21:57:2157"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 12:21:57:2157"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-09-30' MAXRESULTS 1000","timestamp":"2025-06-02 12:21:57:2157"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 69a02f034eed14c6e4a1667393b4d2be","timestamp":"2025-06-02 12:22:00:220"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:23:14:2314"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:23:43:2343"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:25:26:2526"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:25:32:2532"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=between - MessageID: b666b6043eace2f497f610bbd93f8877 ","timestamp":"2025-06-02 12:25:35:2535"}
{"level":"info","message":"2025-06-02T06:55:35.198Z  GET qbo account","timestamp":"2025-06-02 12:25:35:2535"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:25:35:2535"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 12:25:35:2535"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-09-30' MAXRESULTS 1000","timestamp":"2025-06-02 12:25:35:2535"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b666b6043eace2f497f610bbd93f8877","timestamp":"2025-06-02 12:25:38:2538"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&operator=between - MessageID: 9ce23e632e2497281e28ea722de3b351 ","timestamp":"2025-06-02 12:25:48:2548"}
{"level":"info","message":"2025-06-02T06:55:48.513Z  GET qbo account","timestamp":"2025-06-02 12:25:48:2548"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:25:48:2548"}
{"code":102,"errorDescription":"Operator 'between' requires a second value (fieldValue2)","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&operator=between: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:143:15)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:326:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:25:48:2548"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 9ce23e632e2497281e28ea722de3b351","timestamp":"2025-06-02 12:25:48:2548"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!! - MessageID: 75b4ac61c105a75199f5b7a2c4371134 ","timestamp":"2025-06-02 12:25:58:2558"}
{"level":"info","message":"2025-06-02T06:55:58.680Z  GET qbo account","timestamp":"2025-06-02 12:25:58:2558"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:25:58:2558"}
{"code":102,"errorDescription":"Invalid operator '!!' provided. Allowed operators are: >, <, =, >=, <=, LIKE","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!!: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:89:13)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:326:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:25:58:2558"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 75b4ac61c105a75199f5b7a2c4371134","timestamp":"2025-06-02 12:25:58:2558"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:26:19:2619"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:26:23:2623"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!! - MessageID: a01b122495841dfd14df3c0684f93076 ","timestamp":"2025-06-02 12:26:24:2624"}
{"level":"info","message":"2025-06-02T06:56:24.577Z  GET qbo account","timestamp":"2025-06-02 12:26:24:2624"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:26:24:2624"}
{"code":102,"errorDescription":"Invalid operator '!!' provided. Allowed operators are: >, <, =, >=, <=, LIKE, Between","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!!: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:89:13)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:326:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:26:24:2624"}
{"level":"info","message":"Response sent with status: 400 - MessageID: a01b122495841dfd14df3c0684f93076","timestamp":"2025-06-02 12:26:24:2624"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:26:44:2644"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!! - MessageID: 2f1479ae478940e60760fa8fd0a1756d ","timestamp":"2025-06-02 12:26:46:2646"}
{"level":"info","message":"2025-06-02T06:56:46.912Z  GET qbo account","timestamp":"2025-06-02 12:26:46:2646"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:26:46:2646"}
{"code":102,"errorDescription":"Invalid operator '!!' provided. Allowed operators are: >, <, =, >=, <=, LIKE, between","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=!!: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:89:13)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:326:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:223:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 12:26:46:2646"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 2f1479ae478940e60760fa8fd0a1756d","timestamp":"2025-06-02 12:26:46:2646"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=between - MessageID: e5699b36e947ae43b13030dafb2ab934 ","timestamp":"2025-06-02 12:29:39:2939"}
{"level":"info","message":"2025-06-02T06:59:39.049Z  GET qbo account","timestamp":"2025-06-02 12:29:39:2939"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:29:39:2939"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 12:29:39:2939"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-09-30' MAXRESULTS 1000","timestamp":"2025-06-02 12:29:39:2939"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=between: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:281:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 12:29:41:2941"}
{"level":"info","message":"Response sent with status: 401 - MessageID: e5699b36e947ae43b13030dafb2ab934","timestamp":"2025-06-02 12:29:41:2941"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=between - MessageID: c4cb18e7b433ed0925a3f3718a4e8f7e ","timestamp":"2025-06-02 12:30:26:3026"}
{"level":"info","message":"2025-06-02T07:00:26.976Z  GET qbo account","timestamp":"2025-06-02 12:30:26:3026"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:30:26:3026"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 12:30:26:3026"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-09-30' MAXRESULTS 1000","timestamp":"2025-06-02 12:30:26:3026"}
{"level":"info","message":"Response sent with status: 200 - MessageID: c4cb18e7b433ed0925a3f3718a4e8f7e","timestamp":"2025-06-02 12:30:30:3030"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:30:57:3057"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:30:59:3059"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:31:03:313"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:31:06:316"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:31:17:3117"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=between&sortFieldName=name&sortOrder=DESC - MessageID: 80fab49dcdc5ac94c08c3157bf86f1f5 ","timestamp":"2025-06-02 12:33:26:3326"}
{"level":"info","message":"2025-06-02T07:03:26.899Z  GET qbo account","timestamp":"2025-06-02 12:33:26:3326"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:33:26:3326"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 12:33:26:3326"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-09-30' ORDER BY FullyQualifiedName DESC MAXRESULTS 1000","timestamp":"2025-06-02 12:33:26:3326"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 80fab49dcdc5ac94c08c3157bf86f1f5","timestamp":"2025-06-02 12:33:29:3329"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:34:52:3452"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-09-30&fieldValue2=2023-08-11&operator=between&sortFieldName=name&sortOrder=DESC&startPosition=5 - MessageID: 75b070c8a132cca40c3a6b45e2353954 ","timestamp":"2025-06-02 12:39:16:3916"}
{"level":"info","message":"2025-06-02T07:09:16.375Z  GET qbo account","timestamp":"2025-06-02 12:39:16:3916"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 12:39:16:3916"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 12:39:16:3916"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-09-30' ORDER BY FullyQualifiedName DESC MAXRESULTS 1000 STARTPOSITION 5","timestamp":"2025-06-02 12:39:16:3916"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 75b070c8a132cca40c3a6b45e2353954","timestamp":"2025-06-02 12:39:19:3919"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:49:07:497"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:49:15:4915"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:49:41:4941"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 12:53:07:537"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:24:19:2419"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: 69b8598a0895941c43befa90654d6f43 ","timestamp":"2025-06-02 13:24:22:2422"}
{"level":"info","message":"2025-06-02T07:54:22.523Z  GET qbo account","timestamp":"2025-06-02 13:24:22:2422"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:24:22:2422"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 13:24:22:2422"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 69b8598a0895941c43befa90654d6f43","timestamp":"2025-06-02 13:24:25:2425"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: f0a7ac85f091c59361bd5e9f1f930ff3 ","timestamp":"2025-06-02 13:24:46:2446"}
{"level":"info","message":"2025-06-02T07:54:46.857Z  GET qbo account","timestamp":"2025-06-02 13:24:46:2446"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:24:46:2446"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account","timestamp":"2025-06-02 13:24:46:2446"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f0a7ac85f091c59361bd5e9f1f930ff3","timestamp":"2025-06-02 13:24:49:2449"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: cb54e2196182a87d39d896536edb40d6 ","timestamp":"2025-06-02 13:25:12:2512"}
{"level":"info","message":"2025-06-02T07:55:12.855Z  GET qbo account","timestamp":"2025-06-02 13:25:12:2512"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:25:12:2512"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 13:25:12:2512"}
{"level":"info","message":"Response sent with status: 200 - MessageID: cb54e2196182a87d39d896536edb40d6","timestamp":"2025-06-02 13:25:15:2515"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: 12581e7d7cc04e66220f87e6a942f71a ","timestamp":"2025-06-02 13:29:13:2913"}
{"level":"info","message":"2025-06-02T07:59:13.188Z  GET qbo account","timestamp":"2025-06-02 13:29:13:2913"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:29:13:2913"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 13:29:13:2913"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 12581e7d7cc04e66220f87e6a942f71a","timestamp":"2025-06-02 13:29:16:2916"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:29:58:2958"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: a07cbf8ad3c1ec22130a45e0b9548dbe ","timestamp":"2025-06-02 13:30:00:300"}
{"level":"info","message":"2025-06-02T08:00:00.326Z  GET qbo account","timestamp":"2025-06-02 13:30:00:300"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:30:00:300"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 13:30:00:300"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a07cbf8ad3c1ec22130a45e0b9548dbe","timestamp":"2025-06-02 13:30:03:303"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:31:08:318"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: 48c0ebd0a5273c19d7823ccaaf35b3c8 ","timestamp":"2025-06-02 13:31:15:3115"}
{"level":"info","message":"2025-06-02T08:01:15.012Z  GET qbo account","timestamp":"2025-06-02 13:31:15:3115"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:31:15:3115"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 13:31:15:3115"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 48c0ebd0a5273c19d7823ccaaf35b3c8","timestamp":"2025-06-02 13:31:18:3118"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:32:46:3246"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: 4be12716451a176d5daaa83e41ecc0fa ","timestamp":"2025-06-02 13:32:50:3250"}
{"level":"info","message":"2025-06-02T08:02:50.083Z  GET qbo account","timestamp":"2025-06-02 13:32:50:3250"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:32:50:3250"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 13:32:50:3250"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator==: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:266:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 13:32:52:3252"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 4be12716451a176d5daaa83e41ecc0fa","timestamp":"2025-06-02 13:32:52:3252"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: 6efc52b693bfab1ec53dc7a1dfdbd21e ","timestamp":"2025-06-02 13:33:34:3334"}
{"level":"info","message":"2025-06-02T08:03:34.242Z  GET qbo account","timestamp":"2025-06-02 13:33:34:3334"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:33:34:3334"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 13:33:34:3334"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6efc52b693bfab1ec53dc7a1dfdbd21e","timestamp":"2025-06-02 13:33:37:3337"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:34:36:3436"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:34:45:3445"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:34:54:3454"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: 997dbc229a9ae3d2c00bb140c44e1d85 ","timestamp":"2025-06-02 13:34:57:3457"}
{"level":"info","message":"2025-06-02T08:04:57.457Z  GET qbo account","timestamp":"2025-06-02 13:34:57:3457"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:34:57:3457"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 13:34:57:3457"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 997dbc229a9ae3d2c00bb140c44e1d85","timestamp":"2025-06-02 13:35:00:350"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46&operator== - MessageID: ca15ec265bcb23de77b6077a145d5b04 ","timestamp":"2025-06-02 13:43:39:4339"}
{"level":"info","message":"2025-06-02T08:13:39.338Z  GET qbo account","timestamp":"2025-06-02 13:43:39:4339"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:43:39:4339"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46'","timestamp":"2025-06-02 13:43:39:4339"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ca15ec265bcb23de77b6077a145d5b04","timestamp":"2025-06-02 13:43:42:4342"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:45:07:457"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 13:45:11:4511"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46&operator=LIKE%20 - MessageID: b57774c14f6ac7d55601690097ec1f19 ","timestamp":"2025-06-02 13:53:57:5357"}
{"level":"info","message":"2025-06-02T08:23:57.858Z  GET qbo account","timestamp":"2025-06-02 13:53:57:5357"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:53:57:5357"}
{"code":102,"errorDescription":"Invalid operator 'LIKE ' provided. Allowed operators are: >, <, =, >=, <=, LIKE, between","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46&operator=LIKE%20: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:82:13)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:311:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:219:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 13:53:57:5357"}
{"level":"info","message":"Response sent with status: 400 - MessageID: b57774c14f6ac7d55601690097ec1f19","timestamp":"2025-06-02 13:53:57:5357"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46.123456-07:00&operator== - MessageID: 65f6db451b66d82c3c9e9786848efcab ","timestamp":"2025-06-02 13:54:16:5416"}
{"level":"info","message":"2025-06-02T08:24:16.090Z  GET qbo account","timestamp":"2025-06-02 13:54:16:5416"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 13:54:16:5416"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-11T23:58:46.123456-07:00'","timestamp":"2025-06-02 13:54:16:5416"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 65f6db451b66d82c3c9e9786848efcab","timestamp":"2025-06-02 13:54:19:5419"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:07:54:754"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46.123456-07:00&operator== - MessageID: 960acca92a40d7e6db27c2c855eb4844 ","timestamp":"2025-06-02 14:07:59:759"}
{"level":"info","message":"2025-06-02T08:37:59.513Z  GET qbo account","timestamp":"2025-06-02 14:07:59:759"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:07:59:759"}
{"endDate":"2023-08-12T06:58:47.123-07:00","level":"info","message":"Converted datetime equality to range query","originalValue":"2023-08-11T23:58:46.123456-07:00","startDate":"2023-08-11T23:58:46.123456-07:00","timestamp":"2025-06-02 14:07:59:759"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46.123456-07:00' AND MetaData.CreateTime < '2023-08-12T06:58:47.123-07:00'","timestamp":"2025-06-02 14:07:59:759"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 960acca92a40d7e6db27c2c855eb4844","timestamp":"2025-06-02 14:08:02:82"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58&operator== - MessageID: 9272266ca7ed00edb3711ace95395ae7 ","timestamp":"2025-06-02 14:08:13:813"}
{"level":"info","message":"2025-06-02T08:38:13.642Z  GET qbo account","timestamp":"2025-06-02 14:08:13:813"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:08:13:813"}
{"endDate":"2023-08-11T18:28:01.000-11T23:58","level":"info","message":"Converted datetime equality to range query","originalValue":"2023-08-11T23:58","startDate":"2023-08-11T23:58","timestamp":"2025-06-02 14:08:13:813"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58' AND MetaData.CreateTime < '2023-08-11T18:28:01.000-11T23:58'","timestamp":"2025-06-02 14:08:13:813"}
{"code":105,"errorDescription":"QueryValidationError: value 2023-08-11T23:58 is not valid for property 'MetaData.CreateTime'","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58&operator==: Invalid query","stack":"Error: Invalid query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:320:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:08:16:816"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 9272266ca7ed00edb3711ace95395ae7","timestamp":"2025-06-02 14:08:16:816"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: 6685151648c40bf86c29000961b52dfc ","timestamp":"2025-06-02 14:08:39:839"}
{"level":"info","message":"2025-06-02T08:38:39.514Z  GET qbo account","timestamp":"2025-06-02 14:08:39:839"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:08:39:839"}
{"endDate":"2023-08-12T06:58:47.000-07:00","level":"info","message":"Converted datetime equality to range query","originalValue":"2023-08-11T23:58:46-07:00","startDate":"2023-08-11T23:58:46-07:00","timestamp":"2025-06-02 14:08:39:839"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00' AND MetaData.CreateTime < '2023-08-12T06:58:47.000-07:00'","timestamp":"2025-06-02 14:08:39:839"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6685151648c40bf86c29000961b52dfc","timestamp":"2025-06-02 14:08:42:842"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: 183f30c0810f6435f40705fcd29d36a2 ","timestamp":"2025-06-02 14:13:17:1317"}
{"level":"info","message":"2025-06-02T08:43:17.055Z  GET qbo account","timestamp":"2025-06-02 14:13:17:1317"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:13:17:1317"}
{"endDate":"2023-08-12T06:58:47.000-07:00","level":"info","message":"Converted datetime equality to range query","originalValue":"2023-08-11T23:58:46-07:00","startDate":"2023-08-11T23:58:46-07:00","timestamp":"2025-06-02 14:13:17:1317"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00' AND MetaData.CreateTime < '2023-08-12T06:58:47.000-07:00'","timestamp":"2025-06-02 14:13:17:1317"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 183f30c0810f6435f40705fcd29d36a2","timestamp":"2025-06-02 14:13:20:1320"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:19:46:1946"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&operator== - MessageID: d64f3aaa8381f99b099fac0031674ba3 ","timestamp":"2025-06-02 14:19:50:1950"}
{"level":"info","message":"2025-06-02T08:49:50.855Z  GET qbo account","timestamp":"2025-06-02 14:19:50:1950"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:19:50:1950"}
{"endDate":"2023-08-12T12:29:00-07:00","level":"info","message":"Converted datetime equality to minute range query","originalValue":"2023-08-11T23:58:46-07:00","startDate":"2023-08-12T12:28:00-07:00","timestamp":"2025-06-02 14:19:50:1950"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-12T12:28:00-07:00' AND MetaData.CreateTime < '2023-08-12T12:29:00-07:00'","timestamp":"2025-06-02 14:19:50:1950"}
{"level":"info","message":"Response sent with status: 200 - MessageID: d64f3aaa8381f99b099fac0031674ba3","timestamp":"2025-06-02 14:19:54:1954"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&fieldValue2=2023-08-11T23:58:46-07:00&operator=%3E= - MessageID: b7a406d3a86fc74665d0c02fb936b589 ","timestamp":"2025-06-02 14:20:28:2028"}
{"level":"info","message":"2025-06-02T08:50:28.240Z  GET qbo account","timestamp":"2025-06-02 14:20:28:2028"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:20:28:2028"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 14:20:28:2028"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b7a406d3a86fc74665d0c02fb936b589","timestamp":"2025-06-02 14:20:31:2031"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:21:00:210"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&fieldValue2=2023-08-11T23:58:46-07:00&operator=%3E= - MessageID: e89f4c5b2caa7e91a1cc976ecbacadb0 ","timestamp":"2025-06-02 14:21:00:210"}
{"level":"info","message":"2025-06-02T08:51:00.228Z  GET qbo account","timestamp":"2025-06-02 14:21:00:210"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:21:00:210"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 14:21:00:210"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e89f4c5b2caa7e91a1cc976ecbacadb0","timestamp":"2025-06-02 14:21:03:213"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&fieldValue2=2023-08-11T23:58:46-07:00&operator=between - MessageID: a4f649f0d3bd78481ed2f0c3f2d823b2 ","timestamp":"2025-06-02 14:21:11:2111"}
{"level":"info","message":"2025-06-02T08:51:11.313Z  GET qbo account","timestamp":"2025-06-02 14:21:11:2111"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:21:11:2111"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00' AND MetaData.CreateTime <= '2023-08-11T23:58:46-07:00'","timestamp":"2025-06-02 14:21:11:2111"}
{"level":"info","message":"Response sent with status: 200 - MessageID: a4f649f0d3bd78481ed2f0c3f2d823b2","timestamp":"2025-06-02 14:21:14:2114"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&fieldValue2=2023-08-11T23:58:47-07:00&operator=between - MessageID: e71775adef91b059e271f7ef06e17eb0 ","timestamp":"2025-06-02 14:21:22:2122"}
{"level":"info","message":"2025-06-02T08:51:22.104Z  GET qbo account","timestamp":"2025-06-02 14:21:22:2122"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:21:22:2122"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00' AND MetaData.CreateTime <= '2023-08-11T23:58:47-07:00'","timestamp":"2025-06-02 14:21:22:2122"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e71775adef91b059e271f7ef06e17eb0","timestamp":"2025-06-02 14:21:25:2125"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T23:58:46-07:00&fieldValue2=2023-08-11T23:58:47-07:00&operator=between - MessageID: 95d1c0e7667ed8ad5c1af0645e77fae2 ","timestamp":"2025-06-02 14:21:38:2138"}
{"level":"info","message":"2025-06-02T08:51:38.926Z  GET qbo account","timestamp":"2025-06-02 14:21:38:2138"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:21:38:2138"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 14:21:38:2138"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:47-07:00' AND MetaData.CreateTime <= '2023-08-12T23:58:46-07:00'","timestamp":"2025-06-02 14:21:38:2138"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 95d1c0e7667ed8ad5c1af0645e77fae2","timestamp":"2025-06-02 14:21:41:2141"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:21:53:2153"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:22:12:2212"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T23:58:46-07:00&fieldValue2=2023-08-11T23:59:47-07:00&operator=between - MessageID: 7ecd467402760077cad69f1cd483352b ","timestamp":"2025-06-02 14:22:29:2229"}
{"level":"info","message":"2025-06-02T08:52:29.931Z  GET qbo account","timestamp":"2025-06-02 14:22:29:2229"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:22:29:2229"}
{"level":"warn","message":"Swapping dates - start date was after end date","timestamp":"2025-06-02 14:22:29:2229"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:59:47-07:00' AND MetaData.CreateTime <= '2023-08-12T23:58:46-07:00'","timestamp":"2025-06-02 14:22:29:2229"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 7ecd467402760077cad69f1cd483352b","timestamp":"2025-06-02 14:22:32:2232"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&fieldValue2=2023-08-12T23:59:47-07:00&operator=between - MessageID: ed8d4c95d54e9f56b7f74295c1cd80e7 ","timestamp":"2025-06-02 14:24:12:2412"}
{"level":"info","message":"2025-06-02T08:54:12.626Z  GET qbo account","timestamp":"2025-06-02 14:24:12:2412"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:24:12:2412"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00' AND MetaData.CreateTime <= '2023-08-12T23:59:47-07:00'","timestamp":"2025-06-02 14:24:12:2412"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ed8d4c95d54e9f56b7f74295c1cd80e7","timestamp":"2025-06-02 14:24:15:2415"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:25:22:2522"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&fieldValue2=2023-08-12T23:59:47-07:00&operator=between - MessageID: 73b55c387cf0f45fdd43a2436d52284f ","timestamp":"2025-06-02 14:25:25:2525"}
{"level":"info","message":"2025-06-02T08:55:26.009Z  GET qbo account","timestamp":"2025-06-02 14:25:26:2526"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:25:26:2526"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00' AND MetaData.CreateTime <= '2023-08-12T23:59:47-07:00'","timestamp":"2025-06-02 14:25:26:2526"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 73b55c387cf0f45fdd43a2436d52284f","timestamp":"2025-06-02 14:25:29:2529"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:25:49:2549"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&fieldValue2=2023-08-12T23:59:47-07:00&operator=between - MessageID: ba31011bcfe82c9ead999ba2fded62b1 ","timestamp":"2025-06-02 14:25:53:2553"}
{"level":"info","message":"2025-06-02T08:55:53.705Z  GET qbo account","timestamp":"2025-06-02 14:25:53:2553"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:25:53:2553"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00' AND MetaData.CreateTime <= '2023-08-12T23:59:47-07:00'","timestamp":"2025-06-02 14:25:53:2553"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ba31011bcfe82c9ead999ba2fded62b1","timestamp":"2025-06-02 14:25:56:2556"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:26:36:2636"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11T23:58:46-07:00&fieldValue2=2023-08-12T23:59:47-07:00&operator=between - MessageID: d4eff3688ba3bb2c5f4637a2b7051ddd ","timestamp":"2025-06-02 14:26:37:2637"}
{"level":"info","message":"2025-06-02T08:56:37.098Z  GET qbo account","timestamp":"2025-06-02 14:26:37:2637"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:26:37:2637"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11T23:58:46-07:00' AND MetaData.CreateTime <= '2023-08-12T23:59:47-07:00'","timestamp":"2025-06-02 14:26:37:2637"}
{"level":"info","message":"Response sent with status: 200 - MessageID: d4eff3688ba3bb2c5f4637a2b7051ddd","timestamp":"2025-06-02 14:26:40:2640"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:26:59:2659"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11&fieldValue2=2023-08-12&operator=between - MessageID: ca5b40c86b88e82906f57ea61306df1a ","timestamp":"2025-06-02 14:27:14:2714"}
{"level":"info","message":"2025-06-02T08:57:14.126Z  GET qbo account","timestamp":"2025-06-02 14:27:14:2714"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:27:14:2714"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-08-12'","timestamp":"2025-06-02 14:27:14:2714"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ca5b40c86b88e82906f57ea61306df1a","timestamp":"2025-06-02 14:27:17:2717"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-11&fieldValue2=2023-08-11&operator=between - MessageID: 2b2ec641a39d10c565e780957da08e5a ","timestamp":"2025-06-02 14:27:33:2733"}
{"level":"info","message":"2025-06-02T08:57:33.271Z  GET qbo account","timestamp":"2025-06-02 14:27:33:2733"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:27:33:2733"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-08-11'","timestamp":"2025-06-02 14:27:33:2733"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 2b2ec641a39d10c565e780957da08e5a","timestamp":"2025-06-02 14:27:36:2736"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T06:58:43Z&operator=between - MessageID: ed648a81eb8cc6dc7e3f0f69276cf3af ","timestamp":"2025-06-02 14:34:30:3430"}
{"level":"info","message":"2025-06-02T09:04:30.268Z  GET qbo account","timestamp":"2025-06-02 14:34:30:3430"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:34:30:3430"}
{"code":102,"errorDescription":"Operator 'between' requires a second value (fieldValue2)","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T06:58:43Z&operator=between: Bad request","stack":"Error: Bad request\n    at buildQuery (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:136:15)\n    at Object.<anonymous> (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:311:17)\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:4:12)\n    at Object.get (D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:216:20)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:50:33\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\adapters\\service.adapter.ts:8:71","status":400,"timestamp":"2025-06-02 14:34:30:3430"}
{"level":"info","message":"Response sent with status: 400 - MessageID: ed648a81eb8cc6dc7e3f0f69276cf3af","timestamp":"2025-06-02 14:34:30:3430"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T06:58:43Z&operator== - MessageID: 8e5ab5cecece9fa317cf9067d808d50f ","timestamp":"2025-06-02 14:34:37:3437"}
{"level":"info","message":"2025-06-02T09:04:37.510Z  GET qbo account","timestamp":"2025-06-02 14:34:37:3437"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:34:37:3437"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-12T06:58:43Z'","timestamp":"2025-06-02 14:34:37:3437"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T06:58:43Z&operator==: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:266:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 14:34:40:3440"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 8e5ab5cecece9fa317cf9067d808d50f","timestamp":"2025-06-02 14:34:40:3440"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-08-12T06:58:43Z&operator== - MessageID: b89ff9a0190df237246015b8d376dbf8 ","timestamp":"2025-06-02 14:35:21:3521"}
{"level":"info","message":"2025-06-02T09:05:21.554Z  GET qbo account","timestamp":"2025-06-02 14:35:21:3521"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:35:21:3521"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-08-12T06:58:43Z'","timestamp":"2025-06-02 14:35:21:3521"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b89ff9a0190df237246015b8d376dbf8","timestamp":"2025-06-02 14:35:24:3524"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 55c9b8bfa5c289faee4044511f887198 ","timestamp":"2025-06-02 14:38:35:3835"}
{"level":"info","message":"2025-06-02T09:08:35.245Z  GET qbo account","timestamp":"2025-06-02 14:38:35:3835"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:38:35:3835"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:38:35:3835"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 55c9b8bfa5c289faee4044511f887198","timestamp":"2025-06-02 14:38:38:3838"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:41:15:4115"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:41:53:4153"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:42:01:421"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:42:04:424"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:42:10:4210"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:42:15:4215"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 7ac7da38eef8644146422bb559b4b719 ","timestamp":"2025-06-02 14:46:52:4652"}
{"level":"info","message":"2025-06-02T09:16:52.489Z  GET qbo account","timestamp":"2025-06-02 14:46:52:4652"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:46:52:4652"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:46:52:4652"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 7ac7da38eef8644146422bb559b4b719","timestamp":"2025-06-02 14:46:55:4655"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:48:13:4813"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:48:44:4844"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:49:26:4926"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:49:45:4945"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:49:49:4949"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: b61f99b2840aa7414da678cfcb0902f1 ","timestamp":"2025-06-02 14:49:50:4950"}
{"level":"info","message":"2025-06-02T09:19:51.070Z  GET qbo account","timestamp":"2025-06-02 14:49:51:4951"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:49:51:4951"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:49:51:4951"}
{"level":"info","message":"Response sent with status: 200 - MessageID: b61f99b2840aa7414da678cfcb0902f1","timestamp":"2025-06-02 14:49:54:4954"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:52:14:5214"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: a0447cf7e5e1728027fe53e15e5566b3 ","timestamp":"2025-06-02 14:52:24:5224"}
{"level":"info","message":"2025-06-02T09:22:24.465Z  GET qbo account","timestamp":"2025-06-02 14:52:24:5224"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:52:24:5224"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:52:24:5224"}
{"code":"ERR_UNESCAPED_CHARACTERS","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Request path contains unescaped characters","stack":"TypeError: Request path contains unescaped characters\n    at new ClientRequest (node:_http_client:185:13)\n    at Object.request (node:https:381:10)\n    at RedirectableRequest._performRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:337:24)\n    at new RedirectableRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:111:8)\n    at Object.request (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:543:14)\n    at dispatchHttpRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:464:21)\n    at D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:152:5\n    at new Promise (<anonymous>)\n    at wrapAsync (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:132:10)\n    at http (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:170:10)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-06-02 14:52:24:5224"}
{"level":"info","message":"Response sent with status: 500 - MessageID: a0447cf7e5e1728027fe53e15e5566b3","timestamp":"2025-06-02 14:52:24:5224"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:53:15:5315"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: ec779ff29f999717f8c74ce7c0017fb8 ","timestamp":"2025-06-02 14:53:17:5317"}
{"level":"info","message":"2025-06-02T09:23:17.742Z  GET qbo account","timestamp":"2025-06-02 14:53:17:5317"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:53:17:5317"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:53:17:5317"}
{"code":"ERR_UNESCAPED_CHARACTERS","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Request path contains unescaped characters","stack":"TypeError: Request path contains unescaped characters\n    at new ClientRequest (node:_http_client:185:13)\n    at Object.request (node:https:381:10)\n    at RedirectableRequest._performRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:337:24)\n    at new RedirectableRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:111:8)\n    at Object.request (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:543:14)\n    at dispatchHttpRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:464:21)\n    at D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:152:5\n    at new Promise (<anonymous>)\n    at wrapAsync (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:132:10)\n    at http (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:170:10)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-06-02 14:53:17:5317"}
{"level":"info","message":"Response sent with status: 500 - MessageID: ec779ff29f999717f8c74ce7c0017fb8","timestamp":"2025-06-02 14:53:17:5317"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:53:54:5354"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:53:57:5357"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: d4b47fef149cf8af937daccb066e3a0c ","timestamp":"2025-06-02 14:53:58:5358"}
{"level":"info","message":"2025-06-02T09:23:58.780Z  GET qbo account","timestamp":"2025-06-02 14:53:58:5358"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:53:58:5358"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:53:58:5358"}
{"level":"info","message":"Response sent with status: 200 - MessageID: d4b47fef149cf8af937daccb066e3a0c","timestamp":"2025-06-02 14:54:01:541"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:54:48:5448"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:54:54:5454"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:54:57:5457"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 05f55b96396a22450477dbad5f7afcb2 ","timestamp":"2025-06-02 14:54:58:5458"}
{"level":"info","message":"2025-06-02T09:24:58.577Z  GET qbo account","timestamp":"2025-06-02 14:54:58:5458"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:54:58:5458"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:54:58:5458"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <IDENTIFIER> \"FROMaccountWHEREMetaData \"\" at line 1, column 8.\nWas expecting one of:\n    <EOF> \n    \",\" ...\n    \"from\" ...\n    \"iterator\" ...\n    \"maxresults\" ...\n    \"order\" ...\n    \"orderby\" ...\n    \"startposition\" ...\n    \"where\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:55:01:551"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 05f55b96396a22450477dbad5f7afcb2","timestamp":"2025-06-02 14:55:01:551"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:55:20:5520"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: d1ed49dd1f9e487273fa9dc66ffeb163 ","timestamp":"2025-06-02 14:55:22:5522"}
{"level":"info","message":"2025-06-02T09:25:22.460Z  GET qbo account","timestamp":"2025-06-02 14:55:22:5522"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:55:22:5522"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:55:22:5522"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <IDENTIFIER> \"FROMaccountWHEREMetaData \"\" at line 1, column 8.\nWas expecting one of:\n    <EOF> \n    \",\" ...\n    \"from\" ...\n    \"iterator\" ...\n    \"maxresults\" ...\n    \"order\" ...\n    \"orderby\" ...\n    \"startposition\" ...\n    \"where\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:55:25:5525"}
{"level":"info","message":"Response sent with status: 400 - MessageID: d1ed49dd1f9e487273fa9dc66ffeb163","timestamp":"2025-06-02 14:55:25:5525"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:55:37:5537"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:55:39:5539"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:55:44:5544"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 26e5b91ab3eec5609af7f99c9474d5d9 ","timestamp":"2025-06-02 14:55:44:5544"}
{"level":"info","message":"2025-06-02T09:25:44.138Z  GET qbo account","timestamp":"2025-06-02 14:55:44:5544"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:55:44:5544"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:55:44:5544"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:55:47:5547"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 26e5b91ab3eec5609af7f99c9474d5d9","timestamp":"2025-06-02 14:55:47:5547"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:56:03:563"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:56:06:566"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: dabf288dcbb5b7cbe001fe01164bb9c4 ","timestamp":"2025-06-02 14:56:07:567"}
{"level":"info","message":"2025-06-02T09:26:07.578Z  GET qbo account","timestamp":"2025-06-02 14:56:07:567"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:56:07:567"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:56:07:567"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:56:10:5610"}
{"level":"info","message":"Response sent with status: 400 - MessageID: dabf288dcbb5b7cbe001fe01164bb9c4","timestamp":"2025-06-02 14:56:10:5610"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:56:37:5637"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:56:46:5646"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 138943285de6527cfe69479372bdf90d ","timestamp":"2025-06-02 14:56:47:5647"}
{"level":"info","message":"2025-06-02T09:26:47.457Z  GET qbo account","timestamp":"2025-06-02 14:56:47:5647"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:56:47:5647"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:56:47:5647"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" \"=\" \"= \"\" at line 1, column 1.\nWas expecting:\n    \"select\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:56:50:5650"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 138943285de6527cfe69479372bdf90d","timestamp":"2025-06-02 14:56:50:5650"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:59:28:5928"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: c942ff9e67c23dcd047ff7942b63b125 ","timestamp":"2025-06-02 14:59:32:5932"}
{"level":"info","message":"2025-06-02T09:29:32.164Z  GET qbo account","timestamp":"2025-06-02 14:59:32:5932"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 14:59:32:5932"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 14:59:32:5932"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" \"=\" \"= \"\" at line 1, column 1.\nWas expecting:\n    \"select\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 14:59:35:5935"}
{"level":"info","message":"Response sent with status: 400 - MessageID: c942ff9e67c23dcd047ff7942b63b125","timestamp":"2025-06-02 14:59:35:5935"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 14:59:45:5945"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:00:16:016"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:00:38:038"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:00:49:049"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:00:54:054"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:01:00:10"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:01:08:18"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 72c79cb99e68e53a4b04743c4583c48e ","timestamp":"2025-06-02 15:01:08:18"}
{"level":"info","message":"2025-06-02T09:31:08.500Z  GET qbo account","timestamp":"2025-06-02 15:01:08:18"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:01:08:18"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:01:08:18"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:01:11:111"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 72c79cb99e68e53a4b04743c4583c48e","timestamp":"2025-06-02 15:01:11:111"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:01:27:127"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 84ec06134dda0f228d6e2df23321ad1a ","timestamp":"2025-06-02 15:01:28:128"}
{"level":"info","message":"2025-06-02T09:31:28.297Z  GET qbo account","timestamp":"2025-06-02 15:01:28:128"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:01:28:128"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:01:28:128"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:01:31:131"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 84ec06134dda0f228d6e2df23321ad1a","timestamp":"2025-06-02 15:01:31:131"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:01:44:144"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:01:52:152"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: dfc4cf11c80d152814a7309660a7d1e2 ","timestamp":"2025-06-02 15:02:05:25"}
{"level":"info","message":"2025-06-02T09:32:05.642Z  GET qbo account","timestamp":"2025-06-02 15:02:05:25"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:02:05:25"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:02:05:25"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:02:08:28"}
{"level":"info","message":"Response sent with status: 400 - MessageID: dfc4cf11c80d152814a7309660a7d1e2","timestamp":"2025-06-02 15:02:08:28"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:02:40:240"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:02:42:242"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:02:53:253"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:03:00:30"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 965449998b27e971c16210961cb7d41f ","timestamp":"2025-06-02 15:03:01:31"}
{"level":"info","message":"2025-06-02T09:33:01.884Z  GET qbo account","timestamp":"2025-06-02 15:03:01:31"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:03:01:31"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:03:01:31"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <INTEGER> \"2023 \"\" at line 1, column 51.\nWas expecting one of:\n    \"false\" ...\n    \"true\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:03:05:35"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 965449998b27e971c16210961cb7d41f","timestamp":"2025-06-02 15:03:05:35"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:03:18:318"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:03:57:357"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: f8fe101ebabd5acb80483f530c71dbb8 ","timestamp":"2025-06-02 15:03:59:359"}
{"level":"info","message":"2025-06-02T09:33:59.789Z  GET qbo account","timestamp":"2025-06-02 15:03:59:359"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:03:59:359"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:03:59:359"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 74.  Encountered: \" \" (32), after : \"&\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:04:02:42"}
{"level":"info","message":"Response sent with status: 400 - MessageID: f8fe101ebabd5acb80483f530c71dbb8","timestamp":"2025-06-02 15:04:02:42"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:04:15:415"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 40891af8e83b3e03775d10726a0e59a2 ","timestamp":"2025-06-02 15:04:18:418"}
{"level":"info","message":"2025-06-02T09:34:18.105Z  GET qbo account","timestamp":"2025-06-02 15:04:18:418"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:04:18:418"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:04:18:418"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 74.  Encountered: \" \" (32), after : \"&\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:04:21:421"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 40891af8e83b3e03775d10726a0e59a2","timestamp":"2025-06-02 15:04:21:421"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:05:44:544"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 5531e5c94df4b0102f7e5ad2f68bd235 ","timestamp":"2025-06-02 15:05:44:544"}
{"level":"info","message":"2025-06-02T09:35:44.880Z  GET qbo account","timestamp":"2025-06-02 15:05:44:544"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:05:44:544"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:05:44:544"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:05:47:547"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 5531e5c94df4b0102f7e5ad2f68bd235","timestamp":"2025-06-02 15:05:47:547"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:06:07:67"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:06:09:69"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:06:12:612"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:06:17:617"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 5112b17f72da48ade736bbce05a29dba ","timestamp":"2025-06-02 15:07:21:721"}
{"level":"info","message":"2025-06-02T09:37:21.783Z  GET qbo account","timestamp":"2025-06-02 15:07:21:721"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:07:21:721"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:07:21:721"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:07:25:725"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 5112b17f72da48ade736bbce05a29dba","timestamp":"2025-06-02 15:07:25:725"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:07:36:736"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 0b61c8b56ec13b6edd51d4eec77272f3 ","timestamp":"2025-06-02 15:07:38:738"}
{"level":"info","message":"2025-06-02T09:37:38.075Z  GET qbo account","timestamp":"2025-06-02 15:07:38:738"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:07:38:738"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:07:38:738"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <INTEGER> \"2023 \"\" at line 1, column 51.\nWas expecting one of:\n    \"false\" ...\n    \"true\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:07:40:740"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 0b61c8b56ec13b6edd51d4eec77272f3","timestamp":"2025-06-02 15:07:40:740"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:07:58:758"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 84f3e9d2228e79d7f2284e37b9e17bdc ","timestamp":"2025-06-02 15:08:04:84"}
{"level":"info","message":"2025-06-02T09:38:04.948Z  GET qbo account","timestamp":"2025-06-02 15:08:04:84"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:08:04:84"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:08:04:84"}
{"code":105,"errorDescription":"QueryParserError: Encountered \" <INTEGER> \"2023 \"\" at line 1, column 51.\nWas expecting one of:\n    \"false\" ...\n    \"true\" ...\n    ","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:279:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:08:07:87"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 84f3e9d2228e79d7f2284e37b9e17bdc","timestamp":"2025-06-02 15:08:07:87"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:08:19:819"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 1f5c60c5af04e9ce7eeb363512e462b7 ","timestamp":"2025-06-02 15:08:22:822"}
{"level":"info","message":"2025-06-02T09:38:22.386Z  GET qbo account","timestamp":"2025-06-02 15:08:22:822"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:08:22:822"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:08:22:822"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 1f5c60c5af04e9ce7eeb363512e462b7","timestamp":"2025-06-02 15:08:25:825"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:09:19:919"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:10:34:1034"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:11:49:1149"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:11:52:1152"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:11:59:1159"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:12:03:123"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 0316ec973711a2ee261c088a9130119d ","timestamp":"2025-06-02 15:12:08:128"}
{"level":"info","message":"2025-06-02T09:42:08.327Z  GET qbo account","timestamp":"2025-06-02 15:12:08:128"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:12:08:128"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:12:08:128"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 0316ec973711a2ee261c088a9130119d","timestamp":"2025-06-02 15:12:11:1211"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:12:24:1224"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:12:29:1229"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: bfde8f0f11516053b9a2633da3d0d268 ","timestamp":"2025-06-02 15:12:33:1233"}
{"level":"info","message":"2025-06-02T09:42:33.097Z  GET qbo account","timestamp":"2025-06-02 15:12:33:1233"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:12:33:1233"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:12:33:1233"}
{"level":"info","message":"Response sent with status: 200 - MessageID: bfde8f0f11516053b9a2633da3d0d268","timestamp":"2025-06-02 15:12:36:1236"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:13:51:1351"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:13:57:1357"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:14:12:1412"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:16:26:1626"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 212dc98434b8ea95a27fbf7c752a92dc ","timestamp":"2025-06-02 15:16:30:1630"}
{"level":"info","message":"2025-06-02T09:46:30.463Z  GET qbo account","timestamp":"2025-06-02 15:16:30:1630"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:16:30:1630"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:16:30:1630"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 212dc98434b8ea95a27fbf7c752a92dc","timestamp":"2025-06-02 15:16:33:1633"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:20:04:204"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:20:17:2017"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:20:19:2019"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:20:31:2031"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: ca04317f5a44bbfd68bda0254bca877b ","timestamp":"2025-06-02 15:20:36:2036"}
{"level":"info","message":"2025-06-02T09:50:36.187Z  GET qbo account","timestamp":"2025-06-02 15:20:36:2036"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:20:36:2036"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:20:36:2036"}
{"level":"info","message":"Response sent with status: 200 - MessageID: ca04317f5a44bbfd68bda0254bca877b","timestamp":"2025-06-02 15:20:39:2039"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:21:06:216"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:21:09:219"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:21:21:2121"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:21:33:2133"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:23:01:231"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 211f0f7c11e55e307724fb247d222edb ","timestamp":"2025-06-02 15:23:06:236"}
{"level":"info","message":"2025-06-02T09:53:06.086Z  GET qbo account","timestamp":"2025-06-02 15:23:06:236"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:23:06:236"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:23:06:236"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 211f0f7c11e55e307724fb247d222edb","timestamp":"2025-06-02 15:23:08:238"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:23:23:2323"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:23:30:2330"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:23:52:2352"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:23:59:2359"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 2367109f1ec262482b86dcdf34541467 ","timestamp":"2025-06-02 15:24:06:246"}
{"level":"info","message":"2025-06-02T09:54:06.845Z  GET qbo account","timestamp":"2025-06-02 15:24:06:246"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:24:06:246"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:24:06:246"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 2367109f1ec262482b86dcdf34541467","timestamp":"2025-06-02 15:24:09:249"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:24:29:2429"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 1b1de7c29c50c1719c20f05f56960d19 ","timestamp":"2025-06-02 15:24:30:2430"}
{"level":"info","message":"2025-06-02T09:54:30.964Z  GET qbo account","timestamp":"2025-06-02 15:24:30:2430"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:24:30:2430"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:24:30:2430"}
{"code":105,"errorDescription":"QueryParserError: Invalid content. Lexical error at line 1, column 7.  Encountered: \"%\" (37), after : \"\"","level":"error","message":"GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator==: Error parsing query","stack":"Error: Error parsing query\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:280:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":400,"timestamp":"2025-06-02 15:24:34:2434"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 1b1de7c29c50c1719c20f05f56960d19","timestamp":"2025-06-02 15:24:34:2434"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:24:49:2449"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:26:59:2659"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 045ab20a9801efe1219e045e588928c3 ","timestamp":"2025-06-02 15:27:22:2722"}
{"level":"info","message":"2025-06-02T09:57:22.163Z  GET qbo account","timestamp":"2025-06-02 15:27:22:2722"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:27:22:2722"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:27:22:2722"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 045ab20a9801efe1219e045e588928c3","timestamp":"2025-06-02 15:27:25:2725"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:28:22:2822"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:28:40:2840"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:28:45:2845"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&fieldName=createdDate&fieldValue=2023-07-31T11:10:14&operator== - MessageID: 9b2c7ea1ab604ce745dac3b296b4e531 ","timestamp":"2025-06-02 15:28:49:2849"}
{"level":"info","message":"2025-06-02T09:58:49.310Z  GET qbo account","timestamp":"2025-06-02 15:28:49:2849"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 15:28:49:2849"}
{"entity":"account","level":"info","message":"Generated QBO Query:","query":"SELECT * FROM account WHERE MetaData.CreateTime = '2023-07-31T11:10:14'","timestamp":"2025-06-02 15:28:49:2849"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 9b2c7ea1ab604ce745dac3b296b4e531","timestamp":"2025-06-02 15:28:52:2852"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:45:42:4542"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:45:45:4545"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 15:47:45:4745"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 16:45:38:4538"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 16:45:40:4540"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 16:45:47:4547"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:05:15:515"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:05:49:549"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:06:23:623"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:06:30:630"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&dateField=createdDate&fieldValue=2023-07-31 - MessageID: d01a0e01d6002b7f9e7ceff464f8f121 ","timestamp":"2025-06-02 17:18:11:1811"}
{"level":"info","message":"2025-06-02T11:48:11.052Z  GET qbo account","timestamp":"2025-06-02 17:18:11:1811"}
{"code":102,"errorDescription":"startDate is required when dateField is provided","level":"error","message":"GET /api/service?service=qbo&entity=account&dateField=createdDate&fieldValue=2023-07-31: Bad request","stack":"Error: Bad request\n    at validateDateParams (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:50:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:93:7\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-02 17:18:11:1811"}
{"level":"info","message":"Response sent with status: 400 - MessageID: d01a0e01d6002b7f9e7ceff464f8f121","timestamp":"2025-06-02 17:18:11:1811"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-07-31 - MessageID: f326bb48256602b98558b29fbabb8cf6 ","timestamp":"2025-06-02 17:18:22:1822"}
{"level":"info","message":"2025-06-02T11:48:22.816Z  GET qbo account","timestamp":"2025-06-02 17:18:22:1822"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 17:18:22:1822"}
{"level":"info","message":"QBO query: SELECT * FROM account WHERE MetaData.CreateTime >= '2023-07-31'","timestamp":"2025-06-02 17:18:22:1822"}
{"code":"ERR_UNESCAPED_CHARACTERS","level":"error","message":"GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-07-31: Request path contains unescaped characters","stack":"TypeError: Request path contains unescaped characters\n    at new ClientRequest (node:_http_client:185:13)\n    at Object.request (node:https:381:10)\n    at RedirectableRequest._performRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:337:24)\n    at new RedirectableRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:111:8)\n    at Object.request (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:543:14)\n    at dispatchHttpRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:464:21)\n    at D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:152:5\n    at new Promise (<anonymous>)\n    at wrapAsync (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:132:10)\n    at http (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:170:10)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"timestamp":"2025-06-02 17:18:22:1822"}
{"level":"info","message":"Response sent with status: 500 - MessageID: f326bb48256602b98558b29fbabb8cf6","timestamp":"2025-06-02 17:18:22:1822"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:18:59:1859"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-07-31 - MessageID: 8ed04f15f833b3c8a8d4d3b6551a74d2 ","timestamp":"2025-06-02 17:19:01:191"}
{"level":"info","message":"2025-06-02T11:49:01.305Z  GET qbo account","timestamp":"2025-06-02 17:19:01:191"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 17:19:01:191"}
{"level":"info","message":"QBO query: SELECT * FROM account WHERE MetaData.CreateTime >= '2023-07-31'","timestamp":"2025-06-02 17:19:01:191"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-07-31: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:99:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-02 17:19:04:194"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 8ed04f15f833b3c8a8d4d3b6551a74d2","timestamp":"2025-06-02 17:19:04:194"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-07-31 - MessageID: 6a09bf457b8b1645b7c9309166873fd9 ","timestamp":"2025-06-02 17:21:06:216"}
{"level":"info","message":"2025-06-02T11:51:06.781Z  GET qbo account","timestamp":"2025-06-02 17:21:06:216"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 17:21:06:216"}
{"level":"info","message":"QBO query: SELECT * FROM account WHERE MetaData.CreateTime >= '2023-07-31'","timestamp":"2025-06-02 17:21:06:216"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 6a09bf457b8b1645b7c9309166873fd9","timestamp":"2025-06-02 17:21:09:219"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-11 - MessageID: 4dd99a80af3bcbce3b77a69d4fcfeb27 ","timestamp":"2025-06-02 17:21:53:2153"}
{"level":"info","message":"2025-06-02T11:51:53.201Z  GET qbo account","timestamp":"2025-06-02 17:21:53:2153"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 17:21:53:2153"}
{"level":"info","message":"QBO query: SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-08-11'","timestamp":"2025-06-02 17:21:53:2153"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 4dd99a80af3bcbce3b77a69d4fcfeb27","timestamp":"2025-06-02 17:21:56:2156"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:24:49:2449"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:24:54:2454"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:25:01:251"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:25:18:2518"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:25:20:2520"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:25:44:2544"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:25:47:2547"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:25:52:2552"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-11 - MessageID: f2976e96d1436afb1ce2d45302ceb1dd ","timestamp":"2025-06-02 17:26:23:2623"}
{"level":"info","message":"2025-06-02T11:56:23.532Z  GET qbo account","timestamp":"2025-06-02 17:26:23:2623"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 17:26:23:2623"}
{"level":"info","message":"QBO query: SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-08-11'","timestamp":"2025-06-02 17:26:23:2623"}
{"level":"info","message":"Response sent with status: 200 - MessageID: f2976e96d1436afb1ce2d45302ceb1dd","timestamp":"2025-06-02 17:26:26:2626"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account&dateField=createdDate&startDate=2023-08-11&endDate=2023-08-12 - MessageID: c6b1cff1d7c92cb1e8c92998b8b90972 ","timestamp":"2025-06-02 17:26:31:2631"}
{"level":"info","message":"2025-06-02T11:56:31.814Z  GET qbo account","timestamp":"2025-06-02 17:26:31:2631"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-02 17:26:31:2631"}
{"level":"info","message":"QBO query: SELECT * FROM account WHERE MetaData.CreateTime >= '2023-08-11' AND MetaData.CreateTime <= '2023-08-12'","timestamp":"2025-06-02 17:26:31:2631"}
{"level":"info","message":"Response sent with status: 200 - MessageID: c6b1cff1d7c92cb1e8c92998b8b90972","timestamp":"2025-06-02 17:26:34:2634"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-02 17:27:27:2727"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-03 13:20:43:2043"}
{"level":"info","message":"Request received: POST /api/service?service=qbo&entity=vendor - MessageID: 352bc516478fef09f502041d70e58cec ","timestamp":"2025-06-03 13:21:00:210"}
{"level":"error","message":"POST /api/service?service=qbo&entity=vendor: Path not found","stack":"Error: Path not found\n    at notFoundHandler (D:\\Zact_New_Arch\\account-service\\app\\utils\\error-handler.ts:67:22)\n    at Layer.handleRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:342:13)\n    at D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:297:9\n    at processParams (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:582:12)\n    at next (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:291:5)\n    at Function.handle (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:186:3)\n    at router (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:60:12)\n    at Layer.handleRequest (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (D:\\Zact_New_Arch\\account-service\\node_modules\\router\\index.js:342:13)","status":404,"timestamp":"2025-06-03 13:21:00:210"}
{"level":"info","message":"Response sent with status: 404 - MessageID: 352bc516478fef09f502041d70e58cec","timestamp":"2025-06-03 13:21:00:210"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor - MessageID: 583ec05ebf3f68cc3df5aa9a6a031257 ","timestamp":"2025-06-03 13:21:08:218"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=vendor: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 13:21:08:218"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 583ec05ebf3f68cc3df5aa9a6a031257","timestamp":"2025-06-03 13:21:08:218"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 3f575c9074debbce28727d2d5d7ef803 ","timestamp":"2025-06-03 13:21:20:2120"}
{"level":"info","message":"2025-06-03T07:51:20.047Z  GET qbo account","timestamp":"2025-06-03 13:21:20:2120"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 13:21:20:2120"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 13:21:20:2120"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:84:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 13:21:21:2121"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 3f575c9074debbce28727d2d5d7ef803","timestamp":"2025-06-03 13:21:21:2121"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: c653d57e1147ef9f60e5276570233fd8 ","timestamp":"2025-06-03 13:37:03:373"}
{"level":"info","message":"2025-06-03T08:07:03.048Z  GET qbo account","timestamp":"2025-06-03 13:37:03:373"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 13:37:03:373"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 13:37:03:373"}
{"level":"info","message":"Response sent with status: 200 - MessageID: c653d57e1147ef9f60e5276570233fd8","timestamp":"2025-06-03 13:37:07:377"}
{"level":"info","message":"Request received: GET /health - MessageID: d475793f20fc8d56a297564c12918841 ","timestamp":"2025-06-03 13:38:00:380"}
{"level":"info","message":"Response sent with status: 404 - MessageID: d475793f20fc8d56a297564c12918841","timestamp":"2025-06-03 13:38:00:380"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: c6490e938c473b92a9c41f13e2e86a5f ","timestamp":"2025-06-03 13:43:51:4351"}
{"level":"info","message":"2025-06-03T08:13:51.015Z  GET qbo account","timestamp":"2025-06-03 13:43:51:4351"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 13:43:51:4351"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 13:43:51:4351"}
{"cause":{"code":"ECONNRESET","errno":-4077,"syscall":"read"},"code":"ECONNRESET","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"https://sandbox-quickbooks.api.intuit.com/v3/company","env":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..h9g10dQBotDUXJ10WFiwqQ.wVb4kqNNMCyXTIwQfcWHwQouEq_Ksg1nwFCgy87MjlaINjwQx7DYN2ebJbZFdAkCHlZl7xn_X8xiF_1JQBNhjjcdRF_cI9Cxjo-yaFuPtgLEH2W0HbBisFtnKy5zvhissFVxOzyZCek-rygYpnzw2kiQHvOnOGGx59lhlOQ9mHzbWsH-hAgLmQi97HtMZ4ZpTUwM32L1in3m30ZgGt9Wde6jSc_TizwvzuTZ-JOQtJf42XMtifxv68jV2AUsCBhbF1RHJr2aI5wmll_YEv5zOJkhqxXsEK7MUDqwXbeNT8CBtoKJNMQtTBQvf2kKjDZxqegesI3xAZUTrSuSnO-dv1MlTPjud_qN8fT_AKRpQiLwlvC7n215w6ygYh0RHvBWyeobwzMvYoEEPTXx5dgTlOBnqjJf7WQ4cDMnttxoXfjDQg3cWJYyFfnfgq3aXPxaIaqIUrKZ1CZTCl1MoJA2pmzUmBEjasrZP3cX65T7ZtI.VbT6vLGvgTLgGGr-erxbiw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"minorversion":"75","query":"SELECT * FROM account"},"timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/*********5356807410/query","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errno":-4077,"level":"error","message":"GET /api/service?service=qbo&entity=account: read ECONNRESET","name":"Error","request":{"_currentRequest":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/*********5356807410/query?query=SELECT+*+FROM+account&minorversion=75 HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..h9g10dQBotDUXJ10WFiwqQ.wVb4kqNNMCyXTIwQfcWHwQouEq_Ksg1nwFCgy87MjlaINjwQx7DYN2ebJbZFdAkCHlZl7xn_X8xiF_1JQBNhjjcdRF_cI9Cxjo-yaFuPtgLEH2W0HbBisFtnKy5zvhissFVxOzyZCek-rygYpnzw2kiQHvOnOGGx59lhlOQ9mHzbWsH-hAgLmQi97HtMZ4ZpTUwM32L1in3m30ZgGt9Wde6jSc_TizwvzuTZ-JOQtJf42XMtifxv68jV2AUsCBhbF1RHJr2aI5wmll_YEv5zOJkhqxXsEK7MUDqwXbeNT8CBtoKJNMQtTBQvf2kKjDZxqegesI3xAZUTrSuSnO-dv1MlTPjud_qN8fT_AKRpQiLwlvC7n215w6ygYh0RHvBWyeobwzMvYoEEPTXx5dgTlOBnqjJf7WQ4cDMnttxoXfjDQg3cWJYyFfnfgq3aXPxaIaqIUrKZ1CZTCl1MoJA2pmzUmBEjasrZP3cX65T7ZtI.VbT6vLGvgTLgGGr-erxbiw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null],"end":[null,null],"newListener":[null,null]},"_eventsCount":10,"_hadError":true,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":"[Circular]","_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":939,"pendingcb":1,"writelen":939},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["*************:443","************:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":5000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account&minorversion=75","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_currentUrl":"https://sandbox-quickbooks.api.intuit.com/v3/company/*********5356807410/query?query=SELECT+*+FROM+account&minorversion=75","_ended":true,"_ending":true,"_events":{},"_eventsCount":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..h9g10dQBotDUXJ10WFiwqQ.wVb4kqNNMCyXTIwQfcWHwQouEq_Ksg1nwFCgy87MjlaINjwQx7DYN2ebJbZFdAkCHlZl7xn_X8xiF_1JQBNhjjcdRF_cI9Cxjo-yaFuPtgLEH2W0HbBisFtnKy5zvhissFVxOzyZCek-rygYpnzw2kiQHvOnOGGx59lhlOQ9mHzbWsH-hAgLmQi97HtMZ4ZpTUwM32L1in3m30ZgGt9Wde6jSc_TizwvzuTZ-JOQtJf42XMtifxv68jV2AUsCBhbF1RHJr2aI5wmll_YEv5zOJkhqxXsEK7MUDqwXbeNT8CBtoKJNMQtTBQvf2kKjDZxqegesI3xAZUTrSuSnO-dv1MlTPjud_qN8fT_AKRpQiLwlvC7n215w6ygYh0RHvBWyeobwzMvYoEEPTXx5dgTlOBnqjJf7WQ4cDMnttxoXfjDQg3cWJYyFfnfgq3aXPxaIaqIUrKZ1CZTCl1MoJA2pmzUmBEjasrZP3cX65T7ZtI.VbT6vLGvgTLgGGr-erxbiw","Content-Type":"application/json","User-Agent":"QBOV3-OAuth2-Postman-Collection"},"hostname":"sandbox-quickbooks.api.intuit.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"sandbox-quickbooks.api.intuit.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null],"end":[null,null],"newListener":[null,null]},"_eventsCount":10,"_hadError":true,"_host":"sandbox-quickbooks.api.intuit.com","_httpMessage":{"_closed":false,"_contentLength":0,"_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /v3/company/*********5356807410/query?query=SELECT+*+FROM+account&minorversion=75 HTTP/1.1\r\nAccept: application/json\r\nContent-Type: application/json\r\nAuthorization: Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9..h9g10dQBotDUXJ10WFiwqQ.wVb4kqNNMCyXTIwQfcWHwQouEq_Ksg1nwFCgy87MjlaINjwQx7DYN2ebJbZFdAkCHlZl7xn_X8xiF_1JQBNhjjcdRF_cI9Cxjo-yaFuPtgLEH2W0HbBisFtnKy5zvhissFVxOzyZCek-rygYpnzw2kiQHvOnOGGx59lhlOQ9mHzbWsH-hAgLmQi97HtMZ4ZpTUwM32L1in3m30ZgGt9Wde6jSc_TizwvzuTZ-JOQtJf42XMtifxv68jV2AUsCBhbF1RHJr2aI5wmll_YEv5zOJkhqxXsEK7MUDqwXbeNT8CBtoKJNMQtTBQvf2kKjDZxqegesI3xAZUTrSuSnO-dv1MlTPjud_qN8fT_AKRpQiLwlvC7n215w6ygYh0RHvBWyeobwzMvYoEEPTXx5dgTlOBnqjJf7WQ4cDMnttxoXfjDQg3cWJYyFfnfgq3aXPxaIaqIUrKZ1CZTCl1MoJA2pmzUmBEjasrZP3cX65T7ZtI.VbT6vLGvgTLgGGr-erxbiw\r\nUser-Agent: QBOV3-OAuth2-Postman-Collection\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: sandbox-quickbooks.api.intuit.com\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"destroyed":false,"finished":true,"host":"sandbox-quickbooks.api.intuit.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account&minorversion=75","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":939,"pendingcb":1,"writelen":939},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"autoSelectFamilyAttemptedAddresses":["*************:443","************:443","***********:443"],"connecting":false,"encrypted":true,"handle":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":5000}]},"totalSocketCount":1}}},"path":"/v3/company/*********5356807410/query?query=SELECT+*+FROM+account&minorversion=75","pathname":"/v3/company/*********5356807410/query","port":"","protocol":"https:","search":"?query=SELECT+*+FROM+account&minorversion=75"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"stack":"Error: read ECONNRESET\n    at Function.AxiosError.from (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\adapters\\http.js:620:25)\n    at RedirectableRequest.emit (node:events:518:28)\n    at RedirectableRequest.emit (node:domain:489:12)\n    at ClientRequest.eventHandlers.<computed> (D:\\Zact_New_Arch\\account-service\\node_modules\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at ClientRequest.emit (node:domain:489:12)\n    at emitErrorEvent (node:_http_client:104:11)\n    at TLSSocket.socketErrorListener (node:_http_client:518:5)\n    at TLSSocket.emit (node:events:518:28)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":500,"syscall":"read","timestamp":"2025-06-03 13:44:32:4432"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 5589045c06a3462cec0f205d96992fe3 ","timestamp":"2025-06-03 13:44:45:4445"}
{"level":"info","message":"2025-06-03T08:14:45.588Z  GET qbo account","timestamp":"2025-06-03 13:44:45:4445"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 13:44:45:4445"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 13:44:45:4445"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 5589045c06a3462cec0f205d96992fe3","timestamp":"2025-06-03 13:44:48:4448"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: e01b6507adb4fd81141cb95528bebc0d ","timestamp":"2025-06-03 14:06:25:625"}
{"level":"info","message":"2025-06-03T08:36:25.717Z  GET qbo account","timestamp":"2025-06-03 14:06:25:625"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 14:06:25:625"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 14:06:25:625"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e01b6507adb4fd81141cb95528bebc0d","timestamp":"2025-06-03 14:06:28:628"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 676f06092f1c65c28e488ae10d4a9fa1 ","timestamp":"2025-06-03 14:07:07:77"}
{"level":"info","message":"2025-06-03T08:37:07.501Z  GET qbo account","timestamp":"2025-06-03 14:07:07:77"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 14:07:07:77"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 14:07:07:77"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 676f06092f1c65c28e488ae10d4a9fa1","timestamp":"2025-06-03 14:07:10:710"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: c680fdd51d0763f48810f1b12749a8be ","timestamp":"2025-06-03 14:20:32:2032"}
{"level":"info","message":"2025-06-03T08:50:32.950Z  GET qbo account","timestamp":"2025-06-03 14:20:32:2032"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 14:20:32:2032"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 14:20:32:2032"}
{"level":"info","message":"Response sent with status: 200 - MessageID: c680fdd51d0763f48810f1b12749a8be","timestamp":"2025-06-03 14:20:36:2036"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: fdd38850d6cab6e09c9369988ce28566 ","timestamp":"2025-06-03 14:25:31:2531"}
{"level":"info","message":"2025-06-03T08:55:31.009Z  GET qbo account","timestamp":"2025-06-03 14:25:31:2531"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 14:25:31:2531"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 14:25:31:2531"}
{"level":"info","message":"Response sent with status: 200 - MessageID: fdd38850d6cab6e09c9369988ce28566","timestamp":"2025-06-03 14:25:34:2534"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=accoun - MessageID: e3996496d80bcca940fa3a8aac9b20e3 ","timestamp":"2025-06-03 14:25:43:2543"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=accoun: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 14:25:43:2543"}
{"level":"info","message":"Response sent with status: 400 - MessageID: e3996496d80bcca940fa3a8aac9b20e3","timestamp":"2025-06-03 14:25:43:2543"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 60de6bca3eff39eccf3475db39d3f4f7 ","timestamp":"2025-06-03 14:38:22:3822"}
{"level":"info","message":"2025-06-03T09:08:22.033Z  GET qbo account","timestamp":"2025-06-03 14:38:22:3822"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 14:38:22:3822"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 14:38:22:3822"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:84:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 14:38:24:3824"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 60de6bca3eff39eccf3475db39d3f4f7","timestamp":"2025-06-03 14:38:24:3824"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 03cfae4eb4b620d8158558f953991711 ","timestamp":"2025-06-03 14:38:54:3854"}
{"level":"info","message":"2025-06-03T09:08:54.738Z  GET qbo account","timestamp":"2025-06-03 14:38:54:3854"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 14:38:54:3854"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 14:38:54:3854"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:84:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 14:38:57:3857"}
{"level":"info","message":"Response sent with status: 401 - MessageID: 03cfae4eb4b620d8158558f953991711","timestamp":"2025-06-03 14:38:57:3857"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 147c40c09896bc37113becab0a1d2158 ","timestamp":"2025-06-03 14:43:21:4321"}
{"level":"info","message":"2025-06-03T09:13:21.720Z  GET qbo account","timestamp":"2025-06-03 14:43:21:4321"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 14:43:21:4321"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 14:43:21:4321"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 147c40c09896bc37113becab0a1d2158","timestamp":"2025-06-03 14:43:25:4325"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=accoun - MessageID: 0684d2fc14334184f8032e32d937400e ","timestamp":"2025-06-03 14:43:42:4342"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=accoun: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 14:43:42:4342"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 0684d2fc14334184f8032e32d937400e","timestamp":"2025-06-03 14:43:42:4342"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=account - MessageID: 62f3838278446cde93e620607d4c7dd2 ","timestamp":"2025-06-03 14:43:45:4345"}
{"level":"info","message":"2025-06-03T09:13:45.379Z  GET qbo account","timestamp":"2025-06-03 14:43:45:4345"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 14:43:45:4345"}
{"level":"info","message":"QBO query: SELECT * FROM account","timestamp":"2025-06-03 14:43:45:4345"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 62f3838278446cde93e620607d4c7dd2","timestamp":"2025-06-03 14:43:48:4348"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-03 18:38:35:3835"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor&totalCount=true - MessageID: ea6dbff12ba5d7b3c5894dff65bba42e ","timestamp":"2025-06-03 18:42:04:424"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=vendor&totalCount=true: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 18:42:04:424"}
{"level":"info","message":"Response sent with status: 400 - MessageID: ea6dbff12ba5d7b3c5894dff65bba42e","timestamp":"2025-06-03 18:42:04:424"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&entity=vendor&totalCount=true - MessageID: 2105039cb9889f67713d33765cbe775a ","timestamp":"2025-06-03 18:42:14:4214"}
{"code":102,"errorDescription":"Invalid entity. Allowed: 'account' or 'ACCOUNT'","level":"error","message":"GET /api/service?service=qbo&entity=vendor&totalCount=true: Bad request","stack":"Error: Bad request\n    at validateServiceAndEntity (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:28:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:80:5\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 18:42:14:4214"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 2105039cb9889f67713d33765cbe775a","timestamp":"2025-06-03 18:42:14:4214"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&totalCount=true&entity=account - MessageID: df0e530a3cbde3f14d5c2d767bc74779 ","timestamp":"2025-06-03 18:42:22:4222"}
{"level":"info","message":"2025-06-03T13:12:22.407Z  GET qbo account","timestamp":"2025-06-03 18:42:22:4222"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 18:42:22:4222"}
{"level":"info","message":"QBO query: SELECT COUNT(*) FROM account","timestamp":"2025-06-03 18:42:22:4222"}
{"code":103,"errorDescription":"Invalid access token or company id","level":"error","message":"GET /api/service?service=qbo&totalCount=true&entity=account: Invalid access token or company id","stack":"Error: Invalid access token or company id\n    at D:\\Zact_New_Arch\\account-service\\app\\services\\qboApiServices\\index.ts:87:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:40:14)\n    at Axios.request (D:\\Zact_New_Arch\\account-service\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)","status":401,"timestamp":"2025-06-03 18:42:24:4224"}
{"level":"info","message":"Response sent with status: 401 - MessageID: df0e530a3cbde3f14d5c2d767bc74779","timestamp":"2025-06-03 18:42:24:4224"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&totalCount=true&entity=account - MessageID: 59477e1b763445bf84b1f527e32b8898 ","timestamp":"2025-06-03 18:43:07:437"}
{"level":"info","message":"2025-06-03T13:13:07.530Z  GET qbo account","timestamp":"2025-06-03 18:43:07:437"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 18:43:07:437"}
{"level":"info","message":"QBO query: SELECT COUNT(*) FROM account","timestamp":"2025-06-03 18:43:07:437"}
{"level":"info","message":"Response sent with status: 200 - MessageID: 59477e1b763445bf84b1f527e32b8898","timestamp":"2025-06-03 18:43:10:4310"}
{"level":"info","message":"Server is listening on port 8003","timestamp":"2025-06-03 18:43:57:4357"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&totalCount=true&entity=account - MessageID: c2d7011d6b6f14fc6b46da805ec1388a ","timestamp":"2025-06-03 18:44:15:4415"}
{"level":"info","message":"2025-06-03T13:14:15.175Z  GET qbo account","timestamp":"2025-06-03 18:44:15:4415"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 18:44:15:4415"}
{"level":"info","message":"QBO query: SELECT COUNT(*) FROM account","timestamp":"2025-06-03 18:44:15:4415"}
{"level":"info","message":"Response sent with status: 200 - MessageID: c2d7011d6b6f14fc6b46da805ec1388a","timestamp":"2025-06-03 18:44:17:4417"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&startDate=2025-04-11&endDate=2025-08-12&totalCount=true&entity=account - MessageID: 2c5dfc17f24932660fe1e17e5aab3e3c ","timestamp":"2025-06-03 18:44:47:4447"}
{"level":"info","message":"2025-06-03T13:14:47.414Z  GET qbo account","timestamp":"2025-06-03 18:44:47:4447"}
{"code":102,"errorDescription":"dateField is required when startDate is provided","level":"error","message":"GET /api/service?service=qbo&startDate=2025-04-11&endDate=2025-08-12&totalCount=true&entity=account: Bad request","stack":"Error: Bad request\n    at validateDateParams (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:57:11)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:98:7\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:4:12)\n    at D:\\Zact_New_Arch\\account-service\\app\\router\\api.routes.ts:75:52\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:25:28\n    at Generator.next (<anonymous>)\n    at D:\\Zact_New_Arch\\account-service\\app\\utils\\async-handler.ts:8:71","status":400,"timestamp":"2025-06-03 18:44:47:4447"}
{"level":"info","message":"Response sent with status: 400 - MessageID: 2c5dfc17f24932660fe1e17e5aab3e3c","timestamp":"2025-06-03 18:44:47:4447"}
{"level":"info","message":"Request received: GET /api/service?service=qbo&dateField=createdDate&startDate=2025-04-11&endDate=2025-08-12&totalCount=true&entity=account - MessageID: e3c156185b967da5dfc428bb4c85439f ","timestamp":"2025-06-03 18:44:51:4451"}
{"level":"info","message":"2025-06-03T13:14:51.469Z  GET qbo account","timestamp":"2025-06-03 18:44:51:4451"}
{"level":"info","message":"QBO GET request | entity: account | companyId: *********5356807410","timestamp":"2025-06-03 18:44:51:4451"}
{"level":"info","message":"QBO query: SELECT COUNT(*) FROM account WHERE MetaData.CreateTime >= '2025-04-11' AND MetaData.CreateTime <= '2025-08-12'","timestamp":"2025-06-03 18:44:51:4451"}
{"level":"info","message":"Response sent with status: 200 - MessageID: e3c156185b967da5dfc428bb4c85439f","timestamp":"2025-06-03 18:44:53:4453"}
