# ArgoCD Application for ZACT microservices
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: zact-production
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default

  source:
    repoURL: https://github.com/your-org/zact-microservices
    targetRevision: main
    path: zact-umbrella-chart
    helm:
      valueFiles:
        - environments/production.yaml

  destination:
    server: https://kubernetes.default.svc
    namespace: zact-prod

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

---
# ArgoCD Application for ZACT staging
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: zact-staging
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default

  source:
    repoURL: https://github.com/your-org/zact-microservices
    targetRevision: staging
    path: zact-umbrella-chart
    helm:
      valueFiles:
        - environments/staging.yaml

  destination:
    server: https://kubernetes.default.svc
    namespace: zact-staging

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
