1. Get the application URL by running these commands:
{{- if eq .Values.services.apiGateway.type "LoadBalancer" }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ .Release.Namespace }} svc -w {{ .Values.services.apiGateway.name }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ .Values.services.apiGateway.name }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo http://$SERVICE_IP:{{ .Values.services.apiGateway.port }}
{{- else if contains "NodePort" .Values.services.apiGateway.type }}
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ .Values.services.apiGateway.name }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "ClusterIP" .Values.services.apiGateway.type }}
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app={{ .Values.services.apiGateway.name }},release={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 8080:$CONTAINER_PORT
{{- end }}

2. The following services are available:
   - API Gateway: {{ .Values.services.apiGateway.name }}:{{ .Values.services.apiGateway.port }}
   - Account Service: {{ .Values.services.accountService.name }}:{{ .Values.services.accountService.port }}
   - Bill Service: {{ .Values.services.billService.name }}:{{ .Values.services.billService.port }}
   - Class Service: {{ .Values.services.classService.name }}:{{ .Values.services.classService.port }}
   - Journal Entry Service: {{ .Values.services.journalentryService.name }}:{{ .Values.services.journalentryService.port }}
   - Payment Service: {{ .Values.services.paymentService.name }}:{{ .Values.services.paymentService.port }}
   - Vendor Service: {{ .Values.services.vendorService.name }}:{{ .Values.services.vendorService.port }}

3. To check the status of your deployment:
   kubectl get pods -l "release={{ .Release.Name }}"

4. To view logs for a specific service:
   kubectl logs -l "app=<service-name>" -f