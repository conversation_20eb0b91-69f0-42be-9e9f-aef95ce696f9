import express from "express";
import { processServiceRequest } from "../adapters";
import { RequestExtended } from "../interfaces/globalInterface";
import ApiException from "../utils/api-exception";
import asyncHandler from "../utils/async-handler";
import { errorHandler, notFoundHandler } from "../utils/error-handler";
import { ErrorCodes } from "../utils/response";

const router = express.Router();

// === Validation Helpers ===
const validateServiceAndEntity = (service?: string, entity?: string) => {
  if (!service || !["qbo"].includes(service.toLowerCase())) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: `Invalid service: ${service}. Supported: qbo`,
    });
  }

  if (!entity) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "Entity is required",
    });
  }

  if (!["payment", "PAYMENT"].includes(entity)) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription: "Invalid entity. Allowed: 'payment' or 'PAYMENT'",
    });
  }
};

const validateAuthParams = (
  accessToken?: string | string[],
  companyId?: string | string[]
) => {
  if (!accessToken || !companyId) {
    throw new ApiException({
      ...ErrorCodes.BAD_REQUEST,
      errorDescription:
        "Missing required authentication: accessToken and companyId",
    });
  }
};

// === POST ===
router.post(
  "/service",
  asyncHandler(async (req: RequestExtended, res) => {
    const { data } = req.body;
    const { service, entity, ...queryParams } = req.query;
    const accessToken = req.headers["accesstoken"];
    const companyId = req.headers["companyid"];

    validateServiceAndEntity(service as string, entity as string);
    validateAuthParams(accessToken as string, companyId as string);

    if (!data) {
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: "POST body must include 'data'",
      });
    }

    req.log?.(`POST ${service} ${entity}`);

    const result = await processServiceRequest({
      service: service as string,
      entity: entity as string,
      requestType: "post",
      accessToken: accessToken as string,
      companyId: companyId as string,
      bodyData: data,
      queryParams,
    });

    return result;
  })
);

// === Error Handling Middleware ===
router.use(notFoundHandler);
router.use(errorHandler);

export default router;
