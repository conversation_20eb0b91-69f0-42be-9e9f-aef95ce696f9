{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/account","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:22:57:2257"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/account","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:25:29:2529"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/account","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:25:49:2549"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:31:11:3111"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:31:19:3119"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:31:31:3131"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/api/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:32:35:3235"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:33:13:3313"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:34:21:3421"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:34:36:3436"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:35:32:3532"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 13:37:07:377"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/account","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:37:38:3738"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/health","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 13:37:57:3757"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 13:44:48:4448"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:225:20)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 14:06:29:629"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from account Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 14:07:10:710"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Authentication required","Invalid token"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"2814ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=account","path":"api/service"},"response":{"cacheControl":"no-store, no-cache, must-revalidate, max-age=0","contentLength":"159","contentType":"application/json; charset=utf-8","etag":"W/\"9f-mQGejeD2TqsA5GMqYMuMcgryey0\"","statusCode":401,"statusMessage":"Unauthorized"},"service":"ACCOUNT","target":{"service":"account","url":"http://localhost:8003"},"timestamp":"2025-06-03 14:38:24:3824","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8003api/service","checkService":"curl -X GET http://localhost:8003/health","serviceStatus":"Check if target service is running"}}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Authentication required","Invalid token"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"2802ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=account","path":"api/service"},"response":{"cacheControl":"no-store, no-cache, must-revalidate, max-age=0","contentLength":"159","contentType":"application/json; charset=utf-8","etag":"W/\"9f-mQGejeD2TqsA5GMqYMuMcgryey0\"","statusCode":401,"statusMessage":"Unauthorized"},"service":"ACCOUNT","target":{"service":"account","url":"http://localhost:8003"},"timestamp":"2025-06-03 14:38:57:3857","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8003api/service","checkService":"curl -X GET http://localhost:8003/health","serviceStatus":"Check if target service is running"}}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:37:39:3739"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:39:12:3912"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:39:15:3915"}
{"clientIp":"::1","errorResponse":{"error":{"code":102,"errorDescription":"Request body is required for POST requests to vendor service","status":400},"message":"Bad request: Empty request body","responseStatus":400},"level":"error","message":"🚫 EMPTY REQUEST BODY","method":"POST","path":"/","service":"VENDOR","timestamp":"2025-06-03 15:39:42:3942","userAgent":"PostmanRuntime/7.44.0"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"9ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:05:405","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"3ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:20:4020","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"7ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:40:59:4059","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"10ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 15:41:28:4128","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"level":"error","message":"❌ Error response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:06:426"}
{"level":"error","message":"❌ Error response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:08:428"}
{"level":"error","message":"❌ Error response from vendor","path":"api/service","status":400,"timestamp":"2025-06-03 15:42:23:4223"}
{"error":{"error":{"code":105,"errorDescription":"Error from vendor service","message":"Internal server error","status":500},"responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 15:42:32:4232"}
{"error":{"error":{"code":105,"errorDescription":"Error from vendor service","message":"Internal server error","status":500},"responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 15:42:36:4236"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:43:04:434"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:43:04:434"}
{"error":{"error":{"code":105,"errorDescription":"Error from vendor service","message":"Internal server error","status":500},"responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 15:43:45:4345"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:43:59:4359"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:43:59:4359"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:44:12:4412"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:44:44:4444"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:44:56:4456"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:44:56:4456"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:45:13:4513"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:45:14:4514"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"GET","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:45:18:4518"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 15:48:06:486"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:192:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 15:48:06:486"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:48:21:4821"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:50:58:5058"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:51:00:510"}
{"ip":"::1","level":"error","message":"🚫 EMPTY BODY ERROR","method":"POST","path":"/","service":"VENDOR","timestamp":"2025-06-03 15:51:32:5132"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway error","method":"POST","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 15:54:47:5447"}
{"ip":"::1","level":"error","message":"🚫 EMPTY BODY ERROR","method":"POST","path":"/","service":"VENDOR","timestamp":"2025-06-03 16:03:50:350"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:05:20:520"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:05:59:559"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:07:53:753"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:08:28:828"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:11:51:1151"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:13:55:1355"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:14:25:1425"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:15:01:151"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:15:50:1550"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service (unparseable)","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:24:19:2419"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service (unparseable)","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:27:13:2713"}
{"error":{"error":{"code":400,"message":"Unknown error from vendor","status":400},"errorDescription":"Error from vendor service (unparseable)","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:27:15:2715"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"Error from vendor service","responseStatus":false},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:28:19:2819"}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"44ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 16:28:43:2843","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"errorDetails":{"classification":"CLIENT_ERROR","possibleCauses":["Invalid request","Validation error"],"statusCategory":"4xx - Client Error"},"level":"error","message":"❌ PROXY RESPONSE ERROR","method":"GET","performance":{"responseTime":"2ms","status":"ERROR"},"request":{"clientIp":"::1","originalUrl":"api/service?service=qbo&entity=vendor","path":"api/service"},"response":{"statusCode":400,"statusMessage":"Bad Request"},"service":"VENDOR","target":{"service":"vendor","url":"http://localhost:8001"},"timestamp":"2025-06-03 16:28:44:2844","troubleshooting":{"checkEndpoint":"curl -X GET http://localhost:8001api/service","checkService":"curl -X GET http://localhost:8001/health","serviceStatus":"Check if target service is running"}}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:30:59:3059"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:30:59:3059"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:31:16:3116"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:32:05:325"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:32:42:3242"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:32:42:3242"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:32:51:3251"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:32:51:3251"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:33:00:330"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:33:00:330"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:33:19:3319"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:33:19:3319"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:33:25:3325"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:33:25:3325"}
{"error":{"error":{"code":103,"errorDescription":"Invalid access token or company id","status":401},"message":"Invalid access token or company id","responseStatus":401},"level":"error","message":"❌ Error from vendor","status":401,"timestamp":"2025-06-03 16:33:50:3350"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:33:50:3350"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:34:42:3442"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"POST","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:16:3516"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:22:3522"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:23:3523"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:25:3525"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:25:3525"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:35:26:3526"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:36:22:3622"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:36:23:3623"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:36:24:3624"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:36:42:3642"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:38:03:383"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:39:03:393"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:39:24:3924"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"GET","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:40:19:4019"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:221:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:40:30:4030"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:40:45:4045"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:40:45:4045"}
{"error":"Unexpected end of JSON input","level":"error","message":"🚨 Gateway Proxy Error","method":"POST","name":"SyntaxError","path":"/api/vendor","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage.emit (node:domain:489:12)","timestamp":"2025-06-03 16:41:03:413"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:51:18:5118"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:51:18:5118"}
{"error":{"error":{"code":105,"message":"Internal server error","status":500},"errorDescription":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"utf-8\">\n<title>Error</title>\n</head>\n<body>\n<pre>SyntaxError: Unexpected end of JSON input<br> &nbsp; &nbsp;at JSON.parse (&lt;anonymous&gt;)<br> &nbsp; &nbsp;at createStrictSyntaxError (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:156:10)<br> &nbsp; &nbsp;at parse (D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\types\\json.js:71:15)<br> &nbsp; &nbsp;at D:\\Zact_New_Arch\\vendor-service\\node_modules\\body-parser\\lib\\read.js:123:18<br> &nbsp; &nbsp;at AsyncResource.runInAsyncScope (node:async_hooks:214:14)<br> &nbsp; &nbsp;at invokeCallback (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:238:16)<br> &nbsp; &nbsp;at done (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:227:7)<br> &nbsp; &nbsp;at IncomingMessage.onEnd (D:\\Zact_New_Arch\\vendor-service\\node_modules\\raw-body\\index.js:287:7)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:events:518:28)<br> &nbsp; &nbsp;at IncomingMessage.emit (node:domain:489:12)</pre>\n</body>\n</html>\n","responseStatus":500},"level":"error","message":"❌ Error from vendor","status":400,"timestamp":"2025-06-03 16:51:32:5132"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at ServerResponse.json (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:278:15)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:191:54)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:51:32:5132"}
{"error":"Unexpected end of JSON input","ip":"::1","level":"error","message":"🚫 JSON PARSE ERROR for vendor","method":"POST","path":"/","service":"VENDOR","timestamp":"2025-06-03 16:55:46:5546"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:222:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 16:56:08:568"}
{"code":"ERR_HTTP_HEADERS_SENT","level":"error","message":"💥 Error processing response from vendor Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.header (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:794:10)\n    at ServerResponse.send (D:\\Zact_New_Arch\\api-gateway\\node_modules\\express\\lib\\response.js:174:12)\n    at IncomingMessage.<anonymous> (D:\\Zact_New_Arch\\api-gateway\\app\\proxy.ts:222:40)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-03 17:03:19:319"}
