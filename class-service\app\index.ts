// Import third-party libraries
import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import helmet from "helmet";
import md5 from "md5";
import client from "prom-client";
import { v4 as uuidv4 } from "uuid";

// Import internal modules
import { logger } from "./utils/logger";
import { RequestExtended } from "./interfaces/globalInterface";
import { rateLimiter } from "./middleware/ratelimiter.middleware";
dotenv.config();
import routes from "./router/api.routes";

// Load environment variables from .env file

const PORT = process.env.PORT;
console.log("PORT: ", PORT);

// Initialize Express application
const app = express();
app.disable("x-powered-by"); // Hide Express version

/**
 * ----------------------
 * Security Middlewares
 * ----------------------
 */
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        frameAncestors: ["'none'"],
        // Add other directives if needed
      },
    },
    referrerPolicy: {
      policy: "strict-origin-when-cross-origin",
    },
    strictTransportSecurity: {
      maxAge: 31536000,
      includeSubDomains: true,
    },
  })
);

// Set remaining security headers not handled by Helmet
app.use((req, res, next) => {
  res.setHeader("Permissions-Policy", "interest-cohort=()");
  res.setHeader(
    "Cache-Control",
    "no-store, no-cache, must-revalidate, max-age=0"
  );
  res.setHeader("Expires", "Wed, 11 Jan 1984 05:00:00 GMT");
  res.setHeader("Pragma", "no-cache");
  res.removeHeader("Server"); // Remove default Node server header
  next();
});

app.use(cors()); // Enable CORS
app.use(rateLimiter); // Rate limiting middleware

/**
 * -------------------------
 * Prometheus Metrics Setup
 * -------------------------
 */
const collectDefaultMetrics = client.collectDefaultMetrics;
collectDefaultMetrics();

const httpRequestCounter = new client.Counter({
  name: "http_requests_total",
  help: "Total number of HTTP requests",
  labelNames: ["method", "route", "status"],
});

const httpRequestDurationHistogram = new client.Histogram({
  name: "http_request_duration_seconds",
  help: "Histogram of HTTP request durations in seconds",
  labelNames: ["method", "route", "status"],
  buckets: [0.1, 0.5, 1, 2, 5],
});

app.use((req: RequestExtended, res, next) => {
  const end = httpRequestDurationHistogram.startTimer();
  res.on("finish", () => {
    const routePath = req.route?.path || req.path;
    httpRequestCounter.inc({
      method: req.method,
      route: routePath,
      status: res.statusCode,
    });
    end({
      method: req.  method,
      route: routePath,
      status: res.statusCode,
    });
  });
  next();
});

app.get("/metrics", async (req, res) => {
  res.set("Content-Type", client.register.contentType);
  res.send(await client.register.metrics());
});

/**
 * ---------------------
 * Logging & Tracing
 * ---------------------
 */
app.use((req: any, res, next) => {
  req.messageId = req.header("messageId") || md5(uuidv4());
  req.id = md5(uuidv4());
  // req.traceId = req.header("eg-request-id") || "-";

  // req.logId = [`messageId[${req.messageId}]`].join(" ");

  req.log = (...args: any[]) => {
    const logMessage = [new Date().toISOString(), req.logId, ...args].join(" ");
    logger.info(logMessage);
  };

  req.error = (...args: any[]) => {
    const errorMessage = [new Date().toISOString(), req.logId, ...args].join(
      " "
    );
    logger.error(errorMessage);
  };

  logger.info(
    `Request received: ${req.method} ${req.originalUrl} - MessageID: ${req.messageId} `
  );
             
  res.on("finish", () => {
    logger.info(
      `Response sent with status: ${res.statusCode} - MessageID: ${req.messageId}`
    );
  });

  next();
});

/**
 * -----------------------------
 * Body Parsing & Routing
 * -----------------------------
 */
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use("/api", routes);

/**
 * ---------------------
 * Start Server
 * ---------------------
 */
app.listen(PORT, () => {
  logger.info("Server is listening on port " + PORT);
});
